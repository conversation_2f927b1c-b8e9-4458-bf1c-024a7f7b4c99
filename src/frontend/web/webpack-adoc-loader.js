const path = require('path')
const loaderUtils = require('loader-utils')
const asciidoctor = require('asciidoctor')()

const rootPath = path.resolve(__dirname, '../../..')
console.log(rootPath)
const getSvnInfo = () => {
  if (!getSvnInfo.cachedResult) {
    const svnInfo = require('svn-info').sync(rootPath)
    getSvnInfo.cachedResult = {
      ...svnInfo,
      branch: svnInfo.url.substring(svnInfo.url.lastIndexOf('/') + 1)
    }
    setTimeout(() => { getSvnInfo.cachedResult = null }, 1000)
  }
  return getSvnInfo.cachedResult
}

asciidoctor.Extensions.register(function () {
  this.block(function () {
    const self = this
    self.named('mermaid')
    self.onContext('literal')
    self.process(function(parent, reader, attributes) {
      if (attributes.caption) {
        const { caption, title } = attributes
        const lines = [
          '<figure class="mermaid-figure">', '<div class="mermaid">',
          ...reader.getLines(),
          '</div>',
          `<figcaption>${title ? `${caption} ${title}` : caption}</figcaption>`,
          '</figure>']
        return self.createBlock(parent, 'pass', lines)
      }
      const lines = [
        '<div class="mermaid">', ...reader.getLines(), '</div>']
      return self.createBlock(parent, 'pass', lines)
    })
  })

  this.preprocessor(function () {
    const self = this
    self.process(function (doc, reader) {
      const svn = getSvnInfo()
      doc.setAttribute('svn_revision', String(svn.revision))
      doc.setAttribute('svn_url', svn.url)
      doc.setAttribute('svn_branch', svn.branch)
      return reader
    })
  })
})

module.exports = function (adoc) {
  const options = loaderUtils.getOptions(this)
  this.cacheable()
  return asciidoctor.convert(adoc, options)
}
