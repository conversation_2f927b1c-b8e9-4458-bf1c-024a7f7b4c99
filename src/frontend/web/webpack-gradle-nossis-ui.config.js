const path = require('path')
const shell = require('shelljs')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const configParts = require('./webpack.config.parts.js')

module.exports = env => {
  const mode = getMode(env)
  const outputDir = typeof env.outputDir === 'string' ? env.outputDir : 'dist'

  return {
    mode,
    entry: {
      'portal/nossis-ui': './src/nossis-ui-contribution.js'
    },
    output: {
      path: path.resolve(__dirname, outputDir),
      filename: outputFilenameNameByMode[mode],
    },
    module: {
      rules: [
        ...configParts.module.rules,
        {
          test: /\.css$/,
          exclude: /\.(raw|inline)\.css$/,
          use: [
            MiniCssExtractPlugin.loader,
            { loader: 'css-loader', options: { sourceMap: true } }
          ]
        }, {
          test: /\.(raw|inline)\.css$/,
          include: function (resourcePath) {
            return resourcePath.includes('/node_modules/@alticelabsprojects/nossis-orchestration-web-components'.replace(/\//g, path.sep)) ||
                (!resourcePath.includes('/node_modules/') && !resourcePath.includes('/bower_components/'))
          },
          use: {
            loader: 'css-loader',
          }
        }, {
          test: /\.s[ac]ss$/,
          exclude: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            MiniCssExtractPlugin.loader,
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        }, {
          test: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        }, {
          test: { or: [/\.(element|template|directive)\.html$/, /\/implementation\.html$/] },
          include: function (resourcePath) {
            return resourcePath.includes('/node_modules/@alticelabsprojects/nossis-orchestration-web-components'.replace(/\//g, path.sep)) ||
                (!resourcePath.includes('/node_modules/') && !resourcePath.includes('/bower_components/'))
          },
          use: {
            loader: 'raw-loader',
          }
        }, {
          test: /\.(png|jpeg)$/,
          include: /src\/web-components/,
          type: 'asset/inline'
        }, {
          test: /\.(woff|woff2|eot|ttf|otf|wav|mp3|png|jpg|jpeg|gif|svg)(\?v=\d+\.\d+\.\d+)?$/,
          include: /node_modules/,
          loader: 'file-loader',
          options: {
            outputPath: 'na-portal-assets-vendors/vendors-assets',
            publicPath: 'vendors-assets'
          }
        },
        {
          test: /\.(png|jpg|gif|svg)$/,
          use: [
            {
              loader: 'file-loader',
              options: {
                limit: 8192,
                name: '/na-ext/images/[name].[ext]'
              },
            },
          ],
        }
      ]
    },
    resolve: configParts.resolve,
    optimization: {
      chunkIds: 'named',
      splitChunks: {
        cacheGroups: {
          'web-components': {
            test: /[\\/]src[\\/]web-components[\\/]/,
            name(module) {
              const moduleFileName = module.identifier().split('/').reduceRight((item) => item)
              const chunkName = moduleFileName.split('.')[0]
              return `web-components/async-${chunkName}`
            },
            chunks: 'async',
            priority: 2,
            enforce: true // https://github.com/webpack-contrib/mini-css-extract-plugin/issues/257#issuecomment-432594711
          },
        }
      }
    },
    plugins: [
      new MiniCssExtractPlugin(MiniCssExtractPluginOptionsByMode[mode]),
      scriptsScalaHtmlGeneration({
        chunks: ['portal/nossis-ui'],
        template: '../play/portal/app/na/portal/views/imports/nossisuiscripts.scala.html',
        filename: 'play-scripts-nossis-ui.html'
      }),
      webPackHooksPlugin({
        beforeRun: () => {
          shell.rm('-rf', outputDir)
        },
        AfterRun: ({ entrypoints }) => {
          const assets = entrypoints.get('portal/nossis-ui').getFiles()
          const jsAssets = assets.filter(js => js.endsWith('.js'))
          const cssAssets = assets.filter(css => css.endsWith('.css'))

          const resourcesConfOutputDir = `${outputDir}/conf`
          const resourcesConfOutput = `${resourcesConfOutputDir}/resources.conf.SAMPLE`
          shell.mkdir('-p', resourcesConfOutputDir)
          shell.cp('../play/portal/conf/frontend/portal-nossis-resources.conf.SAMPLE', resourcesConfOutput)
          shell.sed('-i', /{{ webpack:assets:nossis-ui:js }}/g, jsAssets.map(_ => `"${_}"`).join(','), resourcesConfOutput)
          shell.sed('-i', /{{ webpack:assets:nossis-ui:css }}/g, cssAssets.map(_ => `"/${_}"`).join(','), resourcesConfOutput)

          shell.mkdir('-p', `${outputDir}/portal/app/na/portal/views/imports`)
          shell.mv(`${outputDir}/play-scripts-nossis-ui.html`, `${outputDir}/portal/app/na/portal/views/imports/nossisuiscripts.scala.html`)
        }
      }),
    ].concat(additionPluginsByMode[mode]),

    performance: {
      hints: mode === PRODUCTION ? 'warning' : false,
      maxEntrypointSize: 1024000
    }
  }
}

// constants
const DEVELOPMENT = 'development'
const PRODUCTION = 'production'

const outputFilenameNameByMode = {
  [DEVELOPMENT]: '[name].js',
  [PRODUCTION]: '[name].[contenthash].min.js'
}

const MiniCssExtractPluginOptionsByMode = {
  [DEVELOPMENT]: {
    filename: '[name].css',
    chunkFilename: '[name].css'
  },
  [PRODUCTION]: {
    filename: '[name].[contenthash].css',
    chunkFilename: '[name].[contenthash].css'
  }
}

const additionPluginsByMode = {
  [DEVELOPMENT]: [],
  // CompressionPlugin is most likely not needed
  [PRODUCTION]: [new CompressionPlugin({ test: /\.js(\?.*)?$/i })]
}

/*
 * Guarantees that the result is either "development" or "production"
 */
function getMode({ NODE_ENV }) {
  return typeof NODE_ENV === 'string' &&
    NODE_ENV.toLowerCase() === PRODUCTION
    ? PRODUCTION : DEVELOPMENT
}

/*
 * The HtmlWebpackPlugin is wrapped in this function to simplify the configuration
 * of the "imports/script.scala.html" file, the generated file is created on the
 * webpack output directory, which later is copied to its respective location
 */
function scriptsScalaHtmlGeneration({ chunks, template, filename }) {
  return new HtmlWebpackPlugin({
    inject: false,
    templateParameters: (compilation, assets, assetTags, options) => ({ assets }),
    template,
    chunks,
    filename
  })
}

/*
 * Small plugin to add hooks to webpack compilation process.
 * Used to execute the hooks every time the build is run during watch mode
 */
function webPackHooksPlugin({ beforeRun, AfterRun }) {
  return {
    apply: (compiler) => {
      compiler.hooks.beforeRun.tap('beforeRunPlugin', beforeRun)
      compiler.hooks.afterEmit.tap('AfterEmitPlugin', AfterRun)
    }
  }
}
