require('babel-plugin-require-context-hook/register')()
const jsdom = require('jsdom')
const { JSDOM } = jsdom
const addHook = require('pirates').addHook
const jsStringEscape = require('js-string-escape')

const moduleAlias = require('module-alias')
moduleAlias.addAliases(require('./webpack.config.parts.js').resolve.alias)

function matcher(/* filename */) {
  return true
}

addHook(
  (code, filename) => `module.exports = "${jsStringEscape(code)}" `,
  { exts: ['.css', '.scss', '.html', '.pt', '.en', '.fr'], matcher }
)

class IntersectionObserver {
  constructor() {}

  disconnect() {
    return null
  }

  observe() {
    return null
  }

  takeRecords() {
    return null
  }

  unobserve() {
    return null
  }
}

class ResizeObserver {
  constructor() {}

  disconnect() {
    return null
  }

  observe() {
    return null
  }

  unobserve() {
    return null
  }
}

class CSSStyleSheet {
  constructor() {}

  replace(css) {
    return Promise.resolve(this)
  }

  replaceSync(css) {}
}

const mockStorage = () => {
  let data = {}
  return {
    get length() { return Object.keys(data).length },
    getItem: (key) => data[key],
    setItem: (key, value) => { data[key] = String(value) },
    clear: () => { data = {} },
  }
}

// prepare global vars used to test custom elements before importing and running test files
const { window: jsDomWindow } = new JSDOM('')

const mockSessionStorage = mockStorage()
const mockLocalStorage = mockStorage()

const window = new Proxy(jsDomWindow, {
  get(target, p, receiver) {
    switch (p) {
      case 'sessionStorage': return mockSessionStorage
      case 'localStorage': return mockLocalStorage
      default:
        return target[p]
    }
  }
})

globalThis.window = window
globalThis.localStorage = window.localStorage
globalThis.sessionStorage = window.sessionStorage
globalThis.document = window.document
globalThis.window.requestAnimationFrame = (fn) => setTimeout(fn, 0)
globalThis.requestAnimationFrame = (fn) => setTimeout(fn, 0)
globalThis.cancelAnimationFrame = (timeout) => clearTimeout(timeout)
globalThis.MutationObserver = window.MutationObserver
globalThis.Element = window.Element
globalThis.HTMLElement = window.HTMLElement
globalThis.Node = window.Node
globalThis.Event = window.Event
globalThis.Option = window.Option
globalThis.getComputedStyle = window.getComputedStyle
globalThis.customElements = window.customElements
globalThis.CustomEvent = window.CustomEvent
globalThis.IntersectionObserver = IntersectionObserver
globalThis.ResizeObserver = ResizeObserver
globalThis.CSSStyleSheet = CSSStyleSheet
document.adoptedStyleSheets = []

// import jquery
const $ = require('jquery')
globalThis.$ = $
globalThis.jQuery = $
window.$ = $
window.jQuery = $
// add select2 on jquery
const select2 = require('select2/dist/js/select2.full')
select2($)
// add datatables on jquery
const datatables = require('datatables.net')
datatables(window, $)

function importAll (requires) {
  requires.keys().forEach(requires)
}

importAll(require.context('./src', true, /\.spec\.(ts|js)$/))
importAll(require.context('./src', true, /\.spec-unit\.(ts|js)$/))
