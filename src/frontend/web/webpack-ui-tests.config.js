const path = require('node:path')
const webpack = require('webpack')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const configParts = require('./webpack.config.parts.js')

const pages = {

}

module.exports = env => {
  const mode = getMode(env)
  const outputDir = typeof env.outputDir === 'string' ? env.outputDir : 'demo'
  return {
    mode,
    entry: Object.values(pages)
      .reduce((acc, page) => {
        acc[page.chunk] = page.entry
        return acc
      }, {}),
    output: {
      path: path.resolve(__dirname, outputDir),
      filename: outputFilenameNameByMode[mode]
    },
    module: {
      rules: [
        ...configParts.module.rules,
        {
          test: { or: [/\.(element|template|directive)\.html$/, /\/implementation\.html$/] },
          exclude: /(node_modules|bower_components)/,
          use: {
            loader: 'raw-loader',
          }
        }, {
          test: /\.css$/,
          exclude: /\.(raw|inline)\.css$/,
          use: [
            MiniCssExtractPlugin.loader,
            { loader: 'css-loader', options: { sourceMap: true } }
          ]
        }, {
          test: /\.(raw|inline)\.css$/,
          exclude: /(node_modules|bower_components)/,
          use: {
            loader: 'css-loader',
          }
        }, {
          test: /\.s[ac]ss$/,
          exclude: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            MiniCssExtractPlugin.loader,
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        },
        {
          test: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        }, {
          test: /\.(woff|woff2|eot|ttf|otf|wav|mp3|png|jpg|jpeg|gif|svg)(\?v=\d+\.\d+\.\d+)?$/,
          include: /node_modules/,
          loader: 'file-loader',
          options: {
            outputPath: 'na-portal-assets-vendors/vendors-assets',
            publicPath: 'vendors-assets'
          }
        }]
    },
    resolve: configParts.resolve,
    optimization: {
      ...configParts.optimization,
      splitChunks: {
        // include all types of chunks
        cacheGroups: {

          ...configParts.optimization.splitChunks.cacheGroups,

          // Demo specific vendors, used to identify the css and js origins

          'vendors-angular': {
            test: /[\\/]node_modules[\\/]angular[\\/]/,
            name: 'demo-assets/vendors-angular',
            chunks: 'all',
            priority: 20,
            enforce: true
          },

          'vendors-other': {
            test: /[\\/]node_modules[\\/]/,
            name: 'demo-assets/vendors-other',
            chunks: 'all',
            priority: 1
          }

        }
      }
    },

    plugins: [
      new MiniCssExtractPlugin(MiniCssExtractPluginOptionsByMode[mode]),
      ...Object.values(pages).map(page => new HtmlWebpackPlugin({
        chunks: [page.chunk],
        title: page.title,
        inject: false,
        filename: page.htmlOutput,
        template: page.htmlTemplate,
        chunksSortMode: 'manual',
        templateParameters: (compilation, assets) => {
          const { js, css } = assets
          return {
            js: js.map(js => `<script defer src="${js}"></script>`).join('\n'),
            css: orderedCss(css).map(css => `<link rel="stylesheet" href="${css}">`).join('\n'),
            title: page.title,
          }
        }
      })),
      new webpack.ProvidePlugin({
        $: 'jquery',
        jQuery: 'jquery',
        'window.jQuery': 'jquery'
      })
    ],

    performance: {
      hints: mode === PRODUCTION ? 'warning' : false,
      maxEntrypointSize: 1024000
    }
  }
}

// constants
const DEVELOPMENT = 'development'
const PRODUCTION = 'production'

const outputFilenameNameByMode = {
  [DEVELOPMENT]: '[name].js',
  [PRODUCTION]: '[name].[contenthash].min.js'
}

const MiniCssExtractPluginOptionsByMode = {
  [DEVELOPMENT]: {
    filename: '[name].css',
    chunkFilename: '[name].css'
  },
  [PRODUCTION]: {
    filename: '[name].[contenthash].css',
    chunkFilename: '[name].[contenthash].css'
  }
}

/*
 * Guarantees that the result is either "development" or "production"
 */
function getMode({ NODE_ENV }) {
  return typeof NODE_ENV === 'string' && NODE_ENV.toLowerCase() === PRODUCTION ? PRODUCTION : DEVELOPMENT
}

function orderedCss(cssList) {
  const demoCss = cssList.filter(css => css.includes('demo-assets/'))
  const vendorsCss = cssList.filter(css => css.includes('na-portal-vendors/'))
  const vendorFuxiCss = vendorsCss.filter(css => css.includes('na-portal-assets-vendors/vendors-fuxi'))
  const otherVendorsCss = vendorsCss.filter(css => !vendorFuxiCss.includes(css))
  const otherCss = cssList.filter(css => !vendorsCss.includes(css) && !demoCss.includes(css))
  return [...demoCss, ...otherVendorsCss, ...vendorFuxiCss, ...otherCss]
}
