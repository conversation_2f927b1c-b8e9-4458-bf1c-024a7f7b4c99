{"name": "portal-fuxi-assets", "version": "1.0.0", "description": "Assets to be used on portal dev mode", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "npm install && node index.js"}, "author": "", "license": "ISC", "dependencies": {"basepack": "^1.14.1", "cors": "^2.8.5", "express": "^4.17.1", "express-http-proxy": "^1.6.0", "fuxi": "^2.15.2", "fuxi-nossis": "^3.1.0", "http-server": "^0.12.3"}}