{"name": "portal-fuxi-assets", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "portal-fuxi-assets", "version": "1.0.0", "license": "ISC", "dependencies": {"basepack": "^1.14.1", "cors": "^2.8.5", "express": "^4.17.1", "express-http-proxy": "^1.6.0", "fuxi": "^2.15.2", "fuxi-nossis": "^3.1.0", "http-server": "^0.12.3"}}, "node_modules/@gar/promisify": {"version": "1.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@gar/promisify/-/promisify-1.1.3.tgz", "integrity": "sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==", "license": "MIT"}, "node_modules/@npmcli/fs": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/fs/-/fs-1.1.1.tgz", "integrity": "sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==", "license": "ISC", "dependencies": {"@gar/promisify": "^1.0.1", "semver": "^7.3.5"}}, "node_modules/@npmcli/git": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/git/-/git-2.1.0.tgz", "integrity": "sha512-/hBFX/QG1b+N7PZBFs0bi+evgRZcK9nWBxQKZkGoXUT5hJSwl5c4d7y8/hm+NQZRPhQ67RzFaj5UM9YeyKoryw==", "license": "ISC", "dependencies": {"@npmcli/promise-spawn": "^1.3.2", "lru-cache": "^6.0.0", "mkdirp": "^1.0.4", "npm-pick-manifest": "^6.1.1", "promise-inflight": "^1.0.1", "promise-retry": "^2.0.1", "semver": "^7.3.5", "which": "^2.0.2"}}, "node_modules/@npmcli/git/node_modules/mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/@npmcli/git/node_modules/which": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/@npmcli/installed-package-contents": {"version": "1.0.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/installed-package-contents/-/installed-package-contents-1.0.7.tgz", "integrity": "sha512-9rufe0wnJusCQoLpV9ZPKIVP55itrM5BxOXs10DmdbRfgWtHy1LDyskbwRnBghuB0PrF7pNPOqREVtpz4HqzKw==", "license": "ISC", "dependencies": {"npm-bundled": "^1.1.1", "npm-normalize-package-bin": "^1.0.1"}, "bin": {"installed-package-contents": "index.js"}, "engines": {"node": ">= 10"}}, "node_modules/@npmcli/move-file": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/move-file/-/move-file-1.1.2.tgz", "integrity": "sha512-1SUf/Cg2GzGDyaf15aR9St9TWlb+XvbZXWpDx8YKs7MLzMH/BCeopv+y9vzrzgkfykCGuWOlSu3mZhj2+FQcrg==", "deprecated": "This functionality has been moved to @npmcli/fs", "license": "MIT", "dependencies": {"mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "engines": {"node": ">=10"}}, "node_modules/@npmcli/move-file/node_modules/mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/@npmcli/node-gyp": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/node-gyp/-/node-gyp-1.0.3.tgz", "integrity": "sha512-fnkhw+fmX65kiLqk6E3BFLXNC26rUhK90zVwe2yncPliVT/Qos3xjhTLE59Df8KnPlcwIERXKVlU1bXoUQ+liA==", "license": "ISC"}, "node_modules/@npmcli/promise-spawn": {"version": "1.3.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/promise-spawn/-/promise-spawn-1.3.2.tgz", "integrity": "sha512-QyAGYo/Fbj4MXeGdJcFzZ+FkDkomfRBrPM+9QYJSg+PxgAUL+LU3FneQk37rKR2/zjqkCV1BLHccX98wRXG3Sg==", "license": "ISC", "dependencies": {"infer-owner": "^1.0.4"}}, "node_modules/@npmcli/run-script": {"version": "1.8.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/run-script/-/run-script-1.8.6.tgz", "integrity": "sha512-e42bVZnC6VluBZBAFEr3YrdqSspG3bgilyg4nSLBJ7TRGNCzxHa92XAHxQBLYg0BmgwO4b2mf3h/l5EkEWRn3g==", "license": "ISC", "dependencies": {"@npmcli/node-gyp": "^1.0.2", "@npmcli/promise-spawn": "^1.3.2", "node-gyp": "^7.1.0", "read-package-json-fast": "^2.0.1"}}, "node_modules/@sindresorhus/is": {"version": "0.14.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@sindresorhus/is/-/is-0.14.0.tgz", "integrity": "sha512-9NET910DNaIPngYnLLPeg+Ogzqsi9uM4mSboU5y6p8S5DzMTVEsJZrawi+BoDNUVBa2DhJqQYUFvMDfgU062LQ==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@szmarczak/http-timer": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@szmarczak/http-timer/-/http-timer-1.1.2.tgz", "integrity": "sha512-XIB2XbzHTN6ieIjfIMV9hlVcfPU26s2vafYWQcZHWXHOxiaRZYEDKEwdl129Zyg50+foYV2jCgtrqSA6qNuNSA==", "license": "MIT", "dependencies": {"defer-to-connect": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/@tootallnate/once": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@tootallnate/once/-/once-1.1.2.tgz", "integrity": "sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==", "license": "ISC"}, "node_modules/accepts": {"version": "1.3.7", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/accepts/-/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "license": "MIT", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/agent-base": {"version": "6.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/agent-base/node_modules/debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/agent-base/node_modules/ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/agentkeepalive": {"version": "4.6.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/agentkeepalive/-/agentkeepalive-4.6.0.tgz", "integrity": "sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==", "license": "MIT", "dependencies": {"humanize-ms": "^1.2.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/aggregate-error": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/aggregate-error/-/aggregate-error-3.1.0.tgz", "integrity": "sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==", "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-align": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-align/-/ansi-align-3.0.1.tgz", "integrity": "sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==", "license": "ISC", "dependencies": {"string-width": "^4.1.0"}}, "node_modules/ansi-align/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-align/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-align/node_modules/string-width": {"version": "4.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/ansi-align/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/ansi-regex": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/aproba": {"version": "1.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/aproba/-/aproba-1.2.0.tgz", "integrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==", "license": "ISC"}, "node_modules/are-we-there-yet": {"version": "1.1.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/are-we-there-yet/-/are-we-there-yet-1.1.7.tgz", "integrity": "sha512-nxwy40TuMiUGqMyRHgCSWZ9FM4VAoRP4xUYSTv5ImRog+h9yISPbVH7H8fASCIzYn9wlEv4zvFL7uKDMCFQm3g==", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "node_modules/argparse": {"version": "1.0.10", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/array-each": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/array-each/-/array-each-1.0.1.tgz", "integrity": "sha512-zHjL5SZa68hkKHBFBK6DJCTtr9sfTCPCaph/L7tMSLcTFgy+zX7E+6q5UArbtOtMBCtxdICpfTCspRse+ywyXA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "license": "MIT"}, "node_modules/array-slice": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/array-slice/-/array-slice-1.1.0.tgz", "integrity": "sha512-B1qMD3RBP7O8o0H2KbrXDyB0IccejMF15+87Lvlor12ONPRHP6gTjXMNkt/d3ZuOGbAe66hFmaCfECI24Ufp6w==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/asn1": {"version": "0.2.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "2.6.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/async/-/async-2.6.3.tgz", "integrity": "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=", "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "license": "MIT"}, "node_modules/aws-sign2": {"version": "0.7.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.13.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/aws4/-/aws4-1.13.2.tgz", "integrity": "sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw==", "license": "MIT"}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "license": "MIT"}, "node_modules/basepack": {"version": "1.14.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/basepack/-/basepack-1.14.1.tgz", "integrity": "sha512-26MpRYEYx9tej+YYZSzJEOyEHMwRLfXPpTvca7/NYDjjo61l7kwwRk6xabadAyvXNA0QtKrl0cmGDbDLv7Zd2Q==", "license": "Altice Labs License Terms and Conditions (see http://www.alticelabs.com/licenses/)"}, "node_modules/basic-auth": {"version": "1.1.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/basic-auth/-/basic-auth-1.1.0.tgz", "integrity": "sha1-RSIe5Cn37h5QNb4/UVM/HN/SmIQ=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/body-parser": {"version": "1.19.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/body-parser/-/body-parser-1.19.0.tgz", "integrity": "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=", "license": "MIT", "dependencies": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "engines": {"node": ">= 0.8"}}, "node_modules/boxen": {"version": "4.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/boxen/-/boxen-4.2.0.tgz", "integrity": "sha512-eB4uT9RGzg2odpER62bBwSLvUeGC+WbRjjyyFhGsKnc8wp/m0+hQsMUvUe3H2V0D5vw0nBdO1hCJoZo5mKeuIQ==", "license": "MIT", "dependencies": {"ansi-align": "^3.0.0", "camelcase": "^5.3.1", "chalk": "^3.0.0", "cli-boxes": "^2.2.0", "string-width": "^4.1.0", "term-size": "^2.1.0", "type-fest": "^0.8.1", "widest-line": "^3.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/boxen/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/boxen/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/boxen/node_modules/string-width": {"version": "4.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/boxen/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/brace-expansion": {"version": "1.1.12", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/builtins": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/builtins/-/builtins-1.0.3.tgz", "integrity": "sha512-uYBjakWipfaO/bXI7E8rq6kpwHRZK5cNYrUv2OzZSI/FvmdMyXJ2tG9dKcjEC5YHmHpUAwsargWIZNWdxb/bnQ==", "license": "MIT"}, "node_modules/bytes": {"version": "3.1.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/bytes/-/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cacache": {"version": "15.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cacache/-/cacache-15.3.0.tgz", "integrity": "sha512-VVdYzXEn+cnbXpFgWs5hTT7OScegHVmLhJIR8Ufqk3iFD6A6j5iSX1KuBTfNEv4tdJWE2PzA6IVFtcLC7fN9wQ==", "license": "ISC", "dependencies": {"@npmcli/fs": "^1.0.0", "@npmcli/move-file": "^1.0.1", "chownr": "^2.0.0", "fs-minipass": "^2.0.0", "glob": "^7.1.4", "infer-owner": "^1.0.4", "lru-cache": "^6.0.0", "minipass": "^3.1.1", "minipass-collect": "^1.0.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.2", "mkdirp": "^1.0.3", "p-map": "^4.0.0", "promise-inflight": "^1.0.1", "rimraf": "^3.0.2", "ssri": "^8.0.1", "tar": "^6.0.2", "unique-filename": "^1.1.1"}, "engines": {"node": ">= 10"}}, "node_modules/cacache/node_modules/mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/cacheable-request": {"version": "6.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cacheable-request/-/cacheable-request-6.1.0.tgz", "integrity": "sha512-<PERSON>j3cAGPCqOZX7Rz64Uny2GYAZNliQSqfbePrgAQ1wKAihYmCUnraBtJtKcGR4xz7wF+LoJC+ssFZvv5BgF9Igg==", "license": "MIT", "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^3.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^4.1.0", "responselike": "^1.0.2"}, "engines": {"node": ">=8"}}, "node_modules/cacheable-request/node_modules/get-stream": {"version": "5.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/get-stream/-/get-stream-5.2.0.tgz", "integrity": "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==", "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cacheable-request/node_modules/lowercase-keys": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/lowercase-keys/-/lowercase-keys-2.0.0.tgz", "integrity": "sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/camelcase": {"version": "5.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caseless": {"version": "0.12.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/caseless/-/caseless-0.12.0.tgz", "integrity": "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==", "license": "Apache-2.0"}, "node_modules/chalk": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/chalk/-/chalk-3.0.0.tgz", "integrity": "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "node_modules/chownr": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/chownr/-/chownr-2.0.0.tgz", "integrity": "sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/ci-info": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ci-info/-/ci-info-2.0.0.tgz", "integrity": "sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==", "license": "MIT"}, "node_modules/cint": {"version": "8.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cint/-/cint-8.2.1.tgz", "integrity": "sha512-gyWqJHXgDFPNx7PEyFJotutav+al92TTC3dWlMFyTETlOyKBQMZb7Cetqmj3GlrnSILHwSJRwf4mIGzc7C5lXw==", "license": "ISC"}, "node_modules/clean-stack": {"version": "2.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/clean-stack/-/clean-stack-2.2.0.tgz", "integrity": "sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cli-boxes": {"version": "2.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cli-boxes/-/cli-boxes-2.2.1.tgz", "integrity": "sha512-y4coMcylgSCdVinjiDBuR8PCC2bLjyGTwEmPb9NHR/QaNU6EUOXcTY/s6VjGMD6ENSEaeQYHCY0GNGS5jfMwPw==", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-table": {"version": "0.3.11", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cli-table/-/cli-table-0.3.11.tgz", "integrity": "sha512-IqLQi4lO0nIB4tcdTpN4LCB9FI3uqrJZK7RC515EnhZ6qBaglkIgICb1wjeAqpdoOabm1+SuQtkXIPdYC93jhQ==", "dependencies": {"colors": "1.0.3"}, "engines": {"node": ">= 0.2.0"}}, "node_modules/cli-table/node_modules/colors": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/colors/-/colors-1.0.3.tgz", "integrity": "sha512-pFGrxThWcWQ2MsAz6RtgeWe4NK2kUE1WfsrvvlctdII745EW9I0yflqhe7++M5LEc7bV2c/9/5zc8sFcpL0Drw==", "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/clone-response": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/clone-response/-/clone-response-1.0.3.tgz", "integrity": "sha512-ROoL94jJH2dUVML2Y/5PEDNaSHgeOdSDicUyS7izcF63G6sTc/FTjLub4b8Il9S8S0beOfYt0TaA5qvFK+w0wA==", "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/code-point-at": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/code-point-at/-/code-point-at-1.1.0.tgz", "integrity": "sha512-RpAVKQA5T63xEj6/giIbUEtZwJ4UFIc3ZtvEkiaUERylqe8xb5IvqcgOurZLahv93CLKfxcw5YI+DZcUBRyLXA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "license": "MIT"}, "node_modules/colors": {"version": "1.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/colors/-/colors-1.4.0.tgz", "integrity": "sha1-xQSRR51MG9rtLJztMs98fcI2D3g=", "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "5.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/commander/-/commander-5.1.0.tgz", "integrity": "sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "license": "MIT"}, "node_modules/configstore": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/configstore/-/configstore-5.0.1.tgz", "integrity": "sha512-aMKprgk5YhBNyH25hj8wGt2+D52Sw1DRRIzqBwLp2Ya9mFmY8KPvvtvmna8SxVR9JMZ4kzMD68N22vlaRpkeFA==", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dot-prop": "^5.2.0", "graceful-fs": "^4.1.2", "make-dir": "^3.0.0", "unique-string": "^2.0.0", "write-file-atomic": "^3.0.0", "xdg-basedir": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/console-control-strings": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/console-control-strings/-/console-control-strings-1.1.0.tgz", "integrity": "sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==", "license": "ISC"}, "node_modules/content-disposition": {"version": "0.5.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/content-disposition/-/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/content-type/-/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cookie/-/cookie-0.4.0.tgz", "integrity": "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "license": "MIT"}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cors/-/cors-2.8.5.tgz", "integrity": "sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/corser": {"version": "2.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/corser/-/corser-2.0.1.tgz", "integrity": "sha1-jtolLsqrWEDc2XXOuQ2TcMgZ/4c=", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/crypto-random-string": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/crypto-random-string/-/crypto-random-string-2.0.0.tgz", "integrity": "sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/debug": {"version": "2.6.9", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/decompress-response": {"version": "3.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/decompress-response/-/decompress-response-3.3.0.tgz", "integrity": "sha512-BzRPQuY1ip+qDonAOz42gRm/pg9F768C+npV/4JOsxRC2sq+Rlk+Q4ZCAsOhnIaMrgarILY+RMUIvMmmX1qAEA==", "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/deep-extend": {"version": "0.6.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/deep-extend/-/deep-extend-0.6.0.tgz", "integrity": "sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/defer-to-connect": {"version": "1.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/defer-to-connect/-/defer-to-connect-1.1.3.tgz", "integrity": "sha512-0ISdNousHvZT2EiFlZeZAHBUvSxmKswVCEf8hW7KWgG4a8MVEu/3Vb6uWYozkjylyCxe0JBIiRB1jV45S70WVQ==", "license": "MIT"}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/delegates": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/delegates/-/delegates-1.0.0.tgz", "integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==", "license": "MIT"}, "node_modules/depd": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/destroy": {"version": "1.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=", "license": "MIT"}, "node_modules/detect-file": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/detect-file/-/detect-file-1.0.0.tgz", "integrity": "sha512-DtCOLG98P007x7wiiOmfI0fi3eIKyWiLTGJ2MDnVi/E04lWGbf+JzrRHMm0rgIIZJGtHpKpbVgLWHrv8xXpc3Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/dot-prop": {"version": "5.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/dot-prop/-/dot-prop-5.3.0.tgz", "integrity": "sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==", "license": "MIT", "dependencies": {"is-obj": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/duplexer3": {"version": "0.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/duplexer3/-/duplexer3-0.1.5.tgz", "integrity": "sha512-1A8za6ws41LQgv9HrE/66jyC5yuSjQ3L/KOpFtoBilsAK2iA2wuS5rTt1OCzIvtS2V7nVmedsUU+DGRcjBmOYA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ecc-jsbn": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/ecc-jsbn/node_modules/jsbn": {"version": "0.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==", "license": "MIT"}, "node_modules/ecstatic": {"version": "3.3.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ecstatic/-/ecstatic-3.3.2.tgz", "integrity": "sha1-bR3UmBTQBZRoLGUq22YHamnUbEg=", "deprecated": "This package is unmaintained and deprecated. See the GH Issue 259.", "license": "MIT", "dependencies": {"he": "^1.1.1", "mime": "^1.6.0", "minimist": "^1.1.0", "url-join": "^2.0.5"}, "bin": {"ecstatic": "lib/ecstatic.js"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/encoding": {"version": "0.1.13", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "license": "MIT", "optional": true, "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/encoding/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/end-of-stream": {"version": "1.4.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/end-of-stream/-/end-of-stream-1.4.5.tgz", "integrity": "sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/env-paths": {"version": "2.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/env-paths/-/env-paths-2.2.1.tgz", "integrity": "sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/err-code": {"version": "2.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/err-code/-/err-code-2.0.3.tgz", "integrity": "sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==", "license": "MIT"}, "node_modules/es6-promise": {"version": "4.2.8", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/es6-promise/-/es6-promise-4.2.8.tgz", "integrity": "sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo=", "license": "MIT"}, "node_modules/escape-goat": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/escape-goat/-/escape-goat-2.1.1.tgz", "integrity": "sha512-8/uIhbG12Csjy2JEW7D9pHbreaVaS/OpN3ycnyvElTdwM5n6GY6W6e2IPemfvGZeUMqZ9A/3GqIZMgKnBhAw/Q==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter3": {"version": "4.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/eventemitter3/-/eventemitter3-4.0.4.tgz", "integrity": "sha1-tUY6zmNaCD0Bi9x8kXtMXxCoU4Q=", "license": "MIT"}, "node_modules/expand-tilde": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/expand-tilde/-/expand-tilde-2.0.2.tgz", "integrity": "sha512-A5EmesHW6rfnZ9ysHQjPdJRni0SRar0tjtG5MNtm9n5TUvsYU8oozprtRD4AqHxcZWWlVuAmQo2nWKfN9oyjTw==", "license": "MIT", "dependencies": {"homedir-polyfill": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/express": {"version": "4.17.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/express/-/express-4.17.1.tgz", "integrity": "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=", "license": "MIT", "dependencies": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express-http-proxy": {"version": "1.6.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/express-http-proxy/-/express-http-proxy-1.6.0.tgz", "integrity": "sha1-hnKxCTzJa4qT6OPalI3REaZo7yI=", "license": "MIT", "dependencies": {"debug": "^3.0.1", "es6-promise": "^4.1.1", "raw-body": "^2.3.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/express-http-proxy/node_modules/debug": {"version": "3.2.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/express-http-proxy/node_modules/ms": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "license": "MIT"}, "node_modules/extend": {"version": "3.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "license": "MIT"}, "node_modules/extsprintf": {"version": "1.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==", "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fast-diff/-/fast-diff-1.3.0.tgz", "integrity": "sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==", "license": "Apache-2.0"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "license": "MIT"}, "node_modules/figgy-pudding": {"version": "3.5.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/figgy-pudding/-/figgy-pudding-3.5.2.tgz", "integrity": "sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw==", "deprecated": "This module is no longer supported.", "license": "ISC"}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/finalhandler/-/finalhandler-1.1.2.tgz", "integrity": "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-up": {"version": "4.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/findup-sync": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/findup-sync/-/findup-sync-4.0.0.tgz", "integrity": "sha512-6jvvn/12IC4quLBL1KNokxC7wWTvYncaVUYSoxWw7YykPLuRrnv4qdHcSOywOI5RpkOVGeQRtWM8/q+G6W6qfQ==", "license": "MIT", "dependencies": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^4.0.2", "resolve-dir": "^1.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/fined": {"version": "1.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fined/-/fined-1.2.0.tgz", "integrity": "sha512-ZYDqPLGxDkDhDZBjZBb+oD1+j0rA4E0pXY50eplAAOPg2N/gUBSSk5IM1/QhPfyVo19lJ+CvXpqfvk+b2p/8Ng==", "license": "MIT", "dependencies": {"expand-tilde": "^2.0.2", "is-plain-object": "^2.0.3", "object.defaults": "^1.1.0", "object.pick": "^1.2.0", "parse-filepath": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/flagged-respawn": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/flagged-respawn/-/flagged-respawn-1.0.1.tgz", "integrity": "sha512-lNaHNVymajmk0OJMBn8fVUAU1BtDeKIqKoVhk4xAALB57aALg6b4W0MfJ/cUE0g9YBXy5XhSlPIpYIJ7HaY/3Q==", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/follow-redirects": {"version": "1.12.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/follow-redirects/-/follow-redirects-1.12.1.tgz", "integrity": "sha1-3lSmIFMRuT1gOY68Ac9wFWgjErY=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/for-in": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/for-in/-/for-in-1.0.2.tgz", "integrity": "sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/for-own": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/for-own/-/for-own-1.0.0.tgz", "integrity": "sha512-0OABksIGrxKK8K4kynWkQ7y1zounQxP+CWnyclVwj81KW3vlLlGUx57DKGcP/LH216GzqnstnPocF16Nxs0Ycg==", "license": "MIT", "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/forever-agent": {"version": "0.6.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha512-j0K<PERSON><PERSON>hm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "2.3.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/forwarded": {"version": "0.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-minipass": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fs-minipass/-/fs-minipass-2.1.0.tgz", "integrity": "sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/fuxi": {"version": "2.16.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxi/-/fuxi-2.16.2.tgz", "integrity": "sha512-Ac7DB03Nx32raJg6Ql4kyCnhHcpLJg6NT0fZBc/DwxETruatHwK/Ncv1XUiYINIGJ9+3f0/y6Q7GgEE4+lPT4g==", "license": "SEE LICENSE IN LICENSE", "dependencies": {"grunt-cli": "^1.3.2", "npm-check-updates": "^4.1.2"}}, "node_modules/fuxi-nossis": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxi-nossis/-/fuxi-nossis-3.1.0.tgz", "integrity": "sha512-XxERS48iXtmsxCv7Xi/snQKas10OvQJRZoc3yspYUltmU6Mgss7s/KEpxqJqitqTP+8YSpJYqYsCprp4loxpEA==", "license": "Altice Labs License Terms and Conditions (see http://www.alticelabs.com/licenses/)"}, "node_modules/gauge": {"version": "2.7.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/gauge/-/gauge-2.7.4.tgz", "integrity": "sha512-14x4kjc6lkD3ltw589k0NrPD6cCNTD6CWoVUNpB85+DrtONoZn+Rug6xZU5RvSC4+TZPxA5AnBibQYAvZn41Hg==", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "node_modules/get-stdin": {"version": "7.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/get-stdin/-/get-stdin-7.0.0.tgz", "integrity": "sha512-zRKcywvrXlXsA0v0i9Io4KDRaAw7+a1ZpjRwl9Wox8PFlVCCHra7E9c4kqXCoCM9nR5tBkaTTZRBoCm60bFqTQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/get-stream": {"version": "4.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/get-stream/-/get-stream-4.1.0.tgz", "integrity": "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==", "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "deprecated": "Glob versions prior to v9 are no longer supported", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/global-dirs": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-dirs/-/global-dirs-2.1.0.tgz", "integrity": "sha512-MG6kdOUh/xBnyo9cJFeIKkLEc1AyFq42QTU4XiX51i2NEdxLxLWXIjEjmqKeSuKR7pAZjTqUVoT2b2huxVLgYQ==", "license": "MIT", "dependencies": {"ini": "1.3.7"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/global-dirs/node_modules/ini": {"version": "1.3.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ini/-/ini-1.3.7.tgz", "integrity": "sha512-iKpRpXP+CrP2jyrxvg1kMUpXDyRUFDWurxbnVT1vQPx+Wz9uCYsMIqYuSBLV+PAaZG/d7kRLKRFc9oDMsH+mFQ==", "license": "ISC"}, "node_modules/global-modules": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-modules/-/global-modules-1.0.0.tgz", "integrity": "sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg==", "license": "MIT", "dependencies": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/global-prefix": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-prefix/-/global-prefix-1.0.2.tgz", "integrity": "sha512-5lsx1NUDHtSjfg0eHlmYvZKv8/nVqX4ckFbM+FrGcQ+04KWcWFo9P5MxPZYSzUvyzmdTbI7Eix8Q4IbELDqzKg==", "license": "MIT", "dependencies": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}, "engines": {"node": ">=0.10.0"}}, "node_modules/got": {"version": "9.6.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/got/-/got-9.6.0.tgz", "integrity": "sha512-R7eWptXuGYxwijs0eV+v3o6+XH1IqVK8dJOEecQfTmkncw9AV4dcw/Dhxi8MdlqPthxxpZyizMzyg8RTmEsG+Q==", "license": "MIT", "dependencies": {"@sindresorhus/is": "^0.14.0", "@szmarczak/http-timer": "^1.1.2", "cacheable-request": "^6.0.0", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^4.1.0", "lowercase-keys": "^1.0.1", "mimic-response": "^1.0.1", "p-cancelable": "^1.0.0", "to-readable-stream": "^1.0.0", "url-parse-lax": "^3.0.0"}, "engines": {"node": ">=8.6"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "license": "ISC"}, "node_modules/grunt-cli": {"version": "1.5.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/grunt-cli/-/grunt-cli-1.5.0.tgz", "integrity": "sha512-rILKAFoU0dzlf22SUfDtq2R1fosChXXlJM5j7wI6uoW8gwmXDXzbUvirlKZSYCdXl3LXFbR+8xyS+WFo+b6vlA==", "license": "MIT", "dependencies": {"grunt-known-options": "~2.0.0", "interpret": "~1.1.0", "liftup": "~3.0.1", "nopt": "~5.0.0", "v8flags": "^4.0.1"}, "bin": {"grunt": "bin/grunt"}, "engines": {"node": ">=10"}}, "node_modules/grunt-known-options": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/grunt-known-options/-/grunt-known-options-2.0.0.tgz", "integrity": "sha512-GD7cTz0I4SAede1/+pAbmJRG44zFLPipVtdL9o3vqx9IEyb7b4/Y3s7r6ofI3CchR5GvYJ+8buCSioDv5dQLiA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/har-schema": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==", "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/har-validator/-/har-validator-5.1.5.tgz", "integrity": "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==", "deprecated": "this library is no longer supported", "license": "MIT", "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/has-ansi": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==", "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-unicode": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==", "license": "ISC"}, "node_modules/has-yarn": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/has-yarn/-/has-yarn-2.1.0.tgz", "integrity": "sha512-UqBRqi4ju7T+TqGNdqAO0PaSVGsDGJUBQvk9eUWNGRY1CFGDzYhLWoM7JQEemnlvVcv/YEmc2wNW8BC24EnUsw==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/he/-/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8=", "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/homedir-polyfill": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz", "integrity": "sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA==", "license": "MIT", "dependencies": {"parse-passwd": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hosted-git-info": {"version": "4.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/hosted-git-info/-/hosted-git-info-4.1.0.tgz", "integrity": "sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/http-cache-semantics": {"version": "4.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz", "integrity": "sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ==", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-errors": {"version": "1.7.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-errors/-/http-errors-1.7.2.tgz", "integrity": "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-proxy": {"version": "1.18.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-proxy-agent": {"version": "4.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "integrity": "sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==", "license": "MIT", "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/http-proxy-agent/node_modules/debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/http-proxy-agent/node_modules/ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/http-server": {"version": "0.12.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-server/-/http-server-0.12.3.tgz", "integrity": "sha1-ugRx0OzEJYhmFss1xPryeRQKDTc=", "license": "MIT", "dependencies": {"basic-auth": "^1.0.3", "colors": "^1.4.0", "corser": "^2.0.1", "ecstatic": "^3.3.2", "http-proxy": "^1.18.0", "minimist": "^1.2.5", "opener": "^1.5.1", "portfinder": "^1.0.25", "secure-compare": "3.0.1", "union": "~0.5.0"}, "bin": {"hs": "bin/http-server", "http-server": "bin/http-server"}, "engines": {"node": ">=6"}}, "node_modules/http-signature": {"version": "1.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "integrity": "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/https-proxy-agent/node_modules/debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/https-proxy-agent/node_modules/ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/humanize-ms": {"version": "1.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/humanize-ms/-/humanize-ms-1.2.1.tgz", "integrity": "sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==", "license": "MIT", "dependencies": {"ms": "^2.0.0"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore-walk": {"version": "3.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ignore-walk/-/ignore-walk-3.0.4.tgz", "integrity": "sha512-PY6Ii8o1jMRA1z4F2hRkH/xN59ox43DavKvD3oDpfurRlOJyAHpifIwpbdv1n4jt4ov0jSpw3kQ4GhJnpBL6WQ==", "license": "ISC", "dependencies": {"minimatch": "^3.0.4"}}, "node_modules/import-lazy": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/import-lazy/-/import-lazy-2.1.0.tgz", "integrity": "sha512-m7ZEHgtw69qOGw+jwxXkHlrlIPdTGkyh66zXZ1ajZbxkDBNjSY/LGbmjc7h0s2ELsUDTAhFr55TrPSSqJGPG0A==", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/indent-string/-/indent-string-4.0.0.tgz", "integrity": "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/infer-owner": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/infer-owner/-/infer-owner-1.0.4.tgz", "integrity": "sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==", "license": "ISC"}, "node_modules/inflight": {"version": "1.0.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ini/-/ini-1.3.8.tgz", "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==", "license": "ISC"}, "node_modules/interpret": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/interpret/-/interpret-1.1.0.tgz", "integrity": "sha512-CLM8SNMDu7C5psFCn6Wg/tgpj/bKAg7hc2gWqcuR9OD5Ft9PhBpIu8PLicPeis+xDd6YX2ncI8MCA64I9tftIA==", "license": "MIT"}, "node_modules/ip-address": {"version": "9.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ip-address/-/ip-address-9.0.5.tgz", "integrity": "sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==", "license": "MIT", "dependencies": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">= 12"}}, "node_modules/ip-address/node_modules/sprintf-js": {"version": "1.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/sprintf-js/-/sprintf-js-1.1.3.tgz", "integrity": "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-absolute": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-absolute/-/is-absolute-1.0.0.tgz", "integrity": "sha512-dOWoqflvcydARa360Gvv18DZ/gRuHKi2NU/wU5X1ZFzdYfH29nkiNZsF3mp4OJ3H4yo9Mx8A/uAGNzpzPN3yBA==", "license": "MIT", "dependencies": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-ci": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-ci/-/is-ci-2.0.0.tgz", "integrity": "sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==", "license": "MIT", "dependencies": {"ci-info": "^2.0.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha512-1pqUqRjkhPJ9miNq9SwMfdvi6lBJcd6eFxvfaivQhaH3SgisfiuudvFntdKOmxuee/77l+FPjKrQjWvmPjWrRw==", "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-installed-globally": {"version": "0.3.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-installed-globally/-/is-installed-globally-0.3.2.tgz", "integrity": "sha512-wZ8x1js7Ia0kecP/CHM/3ABkAmujX7WPvQk6uu3Fly/Mk44pySulQpnHG46OMjHGXApINnV4QhY3SWnECO2z5g==", "license": "MIT", "dependencies": {"global-dirs": "^2.0.1", "is-path-inside": "^3.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-lambda": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-lambda/-/is-lambda-1.0.1.tgz", "integrity": "sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==", "license": "MIT"}, "node_modules/is-npm": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-npm/-/is-npm-4.0.0.tgz", "integrity": "sha512-96ECIfh9xtDDlPylNPXhzjsykHsMJZ18ASpaWzQyBr4YRTcVjUvzaHayDAES2oU/3KpljhHUjtSRNiDwi0F0ig==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-obj": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-obj/-/is-obj-2.0.0.tgz", "integrity": "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-path-inside": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-path-inside/-/is-path-inside-3.0.3.tgz", "integrity": "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-object": {"version": "2.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-relative": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-relative/-/is-relative-1.0.0.tgz", "integrity": "sha512-Kw/ReK0iqwKeu0MITLFuj0jbPAmEiOsIwyIXvvbfa6QfmN9pkD1M+8pdk7Rl/dTKbH34/XBFMbgD4iMJhLQbGA==", "license": "MIT", "dependencies": {"is-unc-path": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==", "license": "MIT"}, "node_modules/is-unc-path": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-unc-path/-/is-unc-path-1.0.0.tgz", "integrity": "sha512-mrGpVd0fs7WWLfVsStvgF6iEJnbjDFZh9/emhRDcGWTduTfNHd9CHeUwH3gYIjdbwo4On6hunkztwOaAw0yllQ==", "license": "MIT", "dependencies": {"unc-path-regex": "^0.1.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-windows": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-yarn-global": {"version": "0.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-yarn-global/-/is-yarn-global-0.3.0.tgz", "integrity": "sha512-VjSeb/lHmkoyd8ryPVIKvOCn4D1koMqY+vqyjjUfc3xyKtP4dYOxM44sZrnqQSzSds3xyOrUTLTC9LVCVgLngw==", "license": "MIT"}, "node_modules/isarray": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isobject/-/isobject-3.0.1.tgz", "integrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isstream": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isstream/-/isstream-0.1.2.tgz", "integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==", "license": "MIT"}, "node_modules/jju": {"version": "1.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jju/-/jju-1.4.0.tgz", "integrity": "sha512-8wb9Yw966OSxApiCt0K3yNJL8pnNeIv+OEq2YMidz4FKP6nonSRoOXc80iXY4JaN2FC11B9qsNmDsm+ZOfMROA==", "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/js-yaml/-/js-yaml-3.14.1.tgz", "integrity": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsbn/-/jsbn-1.1.0.tgz", "integrity": "sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==", "license": "MIT"}, "node_modules/json-buffer": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-buffer/-/json-buffer-3.0.0.tgz", "integrity": "sha512-CuUqjv0FUZIdXkHPI8MezCnFCdaTAacej1TZYulLoAg1h/PhwkdXFN4V/gzY4g+fMBCOV2xF+rp7t2XD2ns/NQ==", "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "license": "MIT"}, "node_modules/json-parse-helpfulerror": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-parse-helpfulerror/-/json-parse-helpfulerror-1.0.3.tgz", "integrity": "sha512-XgP0FGR77+QhUxjXkwOMkC94k3WtqEBfcnjWqhRd82qTat4SWKRE+9kUnynz/shm3I4ea2+qISvTIeGTNU7kJg==", "license": "MIT", "dependencies": {"jju": "^1.1.0"}}, "node_modules/json-schema": {"version": "0.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-schema/-/json-schema-0.4.0.tgz", "integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==", "license": "(AFL-2.1 OR BSD-3-Clause)"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==", "license": "ISC"}, "node_modules/json5": {"version": "2.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonparse": {"version": "1.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsonparse/-/jsonparse-1.3.1.tgz", "integrity": "sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==", "engines": ["node >= 0.2.0"], "license": "MIT"}, "node_modules/jsprim": {"version": "1.4.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsprim/-/jsprim-1.4.2.tgz", "integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/keyv": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/keyv/-/keyv-3.1.0.tgz", "integrity": "sha512-9ykJ/46SN/9KPM/sichzQ7OvXyGDYKGTaDlKMGCAlg2UK8KRy4jb0d8sFc+0Tt0YYnThq8X2RZgCg74RPxgcVA==", "license": "MIT", "dependencies": {"json-buffer": "3.0.0"}}, "node_modules/kind-of": {"version": "6.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/kleur": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/kleur/-/kleur-3.0.3.tgz", "integrity": "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/latest-version": {"version": "5.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/latest-version/-/latest-version-5.1.0.tgz", "integrity": "sha512-weT+r0kTkRQdCdYCNtkMwWXQTMEswKrFBkm4ckQOMVhhqhIMI1UT2hMj+1iigIhgSZm5gTmrRXBNoGUgaTY1xA==", "license": "MIT", "dependencies": {"package-json": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/libnpmconfig": {"version": "1.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/libnpmconfig/-/libnpmconfig-1.2.1.tgz", "integrity": "sha512-9esX8rTQAHqarx6qeZqmGQKBNZR5OIbl/Ayr0qQDy3oXja2iFVQQI81R6GZ2a02bSNZ9p3YOGX1O6HHCb1X7kA==", "deprecated": "This module is not used anymore. npm config is parsed by npm itself and by @npmcli/config", "license": "ISC", "dependencies": {"figgy-pudding": "^3.5.1", "find-up": "^3.0.0", "ini": "^1.3.5"}}, "node_modules/libnpmconfig/node_modules/find-up": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/find-up/-/find-up-3.0.0.tgz", "integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/libnpmconfig/node_modules/locate-path": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/libnpmconfig/node_modules/p-locate": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==", "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/libnpmconfig/node_modules/path-exists": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/liftup": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/liftup/-/liftup-3.0.1.tgz", "integrity": "sha512-yR<PERSON>aiQDizWSzoXk3APcA71eOI/UuhEkNN9DiW2Tt44mhYzX4joFoCZlxsSOF7RyeLlfqzFLQI1ngFq3ggMPhOw==", "license": "MIT", "dependencies": {"extend": "^3.0.2", "findup-sync": "^4.0.0", "fined": "^1.2.0", "flagged-respawn": "^1.0.1", "is-plain-object": "^2.0.4", "object.map": "^1.0.1", "rechoir": "^0.7.0", "resolve": "^1.19.0"}, "engines": {"node": ">=10"}}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.17", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/lodash/-/lodash-4.17.17.tgz", "integrity": "sha1-2QGLOsxXqVydz0pFxrY7h3tsLUU=", "license": "MIT"}, "node_modules/lowercase-keys": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/lowercase-keys/-/lowercase-keys-1.0.1.tgz", "integrity": "sha512-G2Lj61tXDnVFFOi8VZds+SoQjtQC3dgokKdDG2mTm1tx4m50NUHBOZSBwQQHyy0V12A0JTG4icfZQH+xPyh8VA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/lru-cache": {"version": "6.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==", "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/make-fetch-happen": {"version": "9.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/make-fetch-happen/-/make-fetch-happen-9.1.0.tgz", "integrity": "sha512-+zopwDy7DNknmwPQplem5lAZX/eCOzSvSNNcSKm5eVwTkOBzoktEfXsa9L23J/GIRhxRsaxzkPEhrJEpE2F4Gg==", "license": "ISC", "dependencies": {"agentkeepalive": "^4.1.3", "cacache": "^15.2.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "minipass": "^3.1.3", "minipass-collect": "^1.0.2", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "socks-proxy-agent": "^6.0.0", "ssri": "^8.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/make-iterator": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/make-iterator/-/make-iterator-1.0.1.tgz", "integrity": "sha512-pxiuXh0iVEq7VM7KMIhs5gxsfxCux2URptUQaXo4iZZJxBAzTPOLE2BumO5dbfVYq/hBJFBR/a1mFDmOx5AGmw==", "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/map-cache": {"version": "0.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=", "license": "MIT"}, "node_modules/methods": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.44.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime-db/-/mime-db-1.44.0.tgz", "integrity": "sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.27", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime-types/-/mime-types-2.1.27.tgz", "integrity": "sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=", "license": "MIT", "dependencies": {"mime-db": "1.44.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-response": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mimic-response/-/mimic-response-1.0.1.tgz", "integrity": "sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/minimist/-/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=", "license": "MIT"}, "node_modules/minipass": {"version": "3.3.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-collect": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-collect/-/minipass-collect-1.0.2.tgz", "integrity": "sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-fetch": {"version": "1.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-fetch/-/minipass-fetch-1.4.1.tgz", "integrity": "sha512-CGH1eblLq26Y15+Azk7ey4xh0J/XfJfrCox5LDJiKqI2Q2iwOLOKrlmIaODiSQS8d18jalF6y2K2ePUm0CmShw==", "license": "MIT", "dependencies": {"minipass": "^3.1.0", "minipass-sized": "^1.0.3", "minizlib": "^2.0.0"}, "engines": {"node": ">=8"}, "optionalDependencies": {"encoding": "^0.1.12"}}, "node_modules/minipass-flush": {"version": "1.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-flush/-/minipass-flush-1.0.5.tgz", "integrity": "sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-json-stream": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-json-stream/-/minipass-json-stream-1.0.2.tgz", "integrity": "sha512-myxeeTm57lYs8pH2nxPzmEEg8DGIgW+9mv6D4JZD2pa81I/OBjeU7PtICXV6c9eRGTA5JMDsuIPUZRCyBMYNhg==", "license": "MIT", "dependencies": {"jsonparse": "^1.3.1", "minipass": "^3.0.0"}}, "node_modules/minipass-pipeline": {"version": "1.2.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz", "integrity": "sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-sized": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-sized/-/minipass-sized-1.0.3.tgz", "integrity": "sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minizlib/-/minizlib-2.1.2.tgz", "integrity": "sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==", "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/mkdirp": {"version": "0.5.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "license": "MIT", "dependencies": {"minimist": "^1.2.5"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/ms": {"version": "2.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/nested-error-stacks": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/nested-error-stacks/-/nested-error-stacks-2.0.1.tgz", "integrity": "sha512-Sr<PERSON>rok4CATudVzBS7coSz26QRSmlK9TzzoFbeKfcPBUFPjcQM9Rqvr/DlJkOrwI/0KcgvMub1n1g5Jt9EgRn4A==", "license": "MIT"}, "node_modules/node-alias": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/node-alias/-/node-alias-1.0.4.tgz", "integrity": "sha512-9uG48bfkbG9BlKe8QrlxuiPNaKl3wpQn6tJbrojVqgkJuWIO28ifRKrRDrrK+ee72rJ25EaE//PhSIo8E29lLw==", "license": "MIT", "dependencies": {"chalk": "^1.1.1", "lodash": "^4.2.0"}, "engines": {"node": ">=0.10"}}, "node_modules/node-alias/node_modules/ansi-styles": {"version": "2.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/node-alias/node_modules/chalk": {"version": "1.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/chalk/-/chalk-1.1.3.tgz", "integrity": "sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==", "license": "MIT", "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/node-alias/node_modules/supports-color": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/node-gyp": {"version": "7.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/node-gyp/-/node-gyp-7.1.2.tgz", "integrity": "sha512-CbpcIo7C3eMu3dL1c3d0xw449fHIGALIJsRP4DDPHpyiW8vcriNY7ubh9TE4zEKfSxscY7PjeFnshE7h75ynjQ==", "license": "MIT", "dependencies": {"env-paths": "^2.2.0", "glob": "^7.1.4", "graceful-fs": "^4.2.3", "nopt": "^5.0.0", "npmlog": "^4.1.2", "request": "^2.88.2", "rimraf": "^3.0.2", "semver": "^7.3.2", "tar": "^6.0.2", "which": "^2.0.2"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": ">= 10.12.0"}}, "node_modules/node-gyp/node_modules/which": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/nopt": {"version": "5.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/nopt/-/nopt-5.0.0.tgz", "integrity": "sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==", "license": "ISC", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": ">=6"}}, "node_modules/normalize-url": {"version": "4.5.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/normalize-url/-/normalize-url-4.5.1.tgz", "integrity": "sha512-9UZCFRHQdNrfTpGg8+1INIg93B6zE0aXMVFkw1WFwvO4SlZywU6aLg5Of0Ap/PgcbSw4LNxvMWXMeugwMCX0AA==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/npm-bundled": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-bundled/-/npm-bundled-1.1.2.tgz", "integrity": "sha512-x5DHup0SuyQcmL3s7Rx/YQ8sbw/Hzg0rj48eN0dV7hf5cmQq5PXIeioroH3raV1QC1yh3uTYuMThvEQF3iKgGQ==", "license": "ISC", "dependencies": {"npm-normalize-package-bin": "^1.0.1"}}, "node_modules/npm-check-updates": {"version": "4.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-check-updates/-/npm-check-updates-4.1.2.tgz", "integrity": "sha512-CRO20Z12fygKL/ow4j4pnpyxevda/PuFbWpsF5E9sFW0B+M3d32A1dD+fTHLDjgderhKXr64W8qQ6M/Gq8OLiw==", "license": "Apache-2.0", "dependencies": {"chalk": "^3.0.0", "cint": "^8.2.1", "cli-table": "^0.3.1", "commander": "^5.0.0", "fast-diff": "^1.2.0", "find-up": "4.1.0", "get-stdin": "^7.0.0", "json-parse-helpfulerror": "^1.0.3", "libnpmconfig": "^1.2.1", "lodash": "^4.17.15", "node-alias": "^1.0.4", "p-map": "^4.0.0", "pacote": "^11.1.4", "progress": "^2.0.3", "prompts": "^2.3.2", "rc-config-loader": "^3.0.0", "requireg": "^0.2.2", "semver": "^7.2.1", "semver-utils": "^1.1.4", "spawn-please": "^0.3.0", "update-notifier": "^4.1.0"}, "bin": {"ncu": "bin/ncu", "npm-check-updates": "bin/npm-check-updates"}, "engines": {"node": ">=8"}}, "node_modules/npm-install-checks": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-install-checks/-/npm-install-checks-4.0.0.tgz", "integrity": "sha512-09OmyDkNLYwqKPOnbI8exiOZU2GVVmQp7tgez2BPi5OZC8M82elDAps7sxC4l//uSUtotWqoEIDwjRvWH4qz8w==", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"semver": "^7.1.1"}, "engines": {"node": ">=10"}}, "node_modules/npm-normalize-package-bin": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.1.tgz", "integrity": "sha512-EPfafl6JL5/rU+ot6P3gRSCpPDW5VmIzX959Ob1+ySFUuuYHWHekXpwdUZcKP5C+DS4GEtdJluwBjnsNDl+fSA==", "license": "ISC"}, "node_modules/npm-package-arg": {"version": "8.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-package-arg/-/npm-package-arg-8.1.5.tgz", "integrity": "sha512-LhgZrg0n0VgvzVdSm1oiZworPbTxYHUJCgtsJW8mGvlDpxTM1vSJc3m5QZeUkhAHIzbz3VCHd/R4osi1L1Tg/Q==", "license": "ISC", "dependencies": {"hosted-git-info": "^4.0.1", "semver": "^7.3.4", "validate-npm-package-name": "^3.0.0"}, "engines": {"node": ">=10"}}, "node_modules/npm-packlist": {"version": "2.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-packlist/-/npm-packlist-2.2.2.tgz", "integrity": "sha512-Jt01acDvJRhJGthnUJVF/w6gumWOZxO7IkpY/lsX9//zqQgnF7OJaxgQXcerd4uQOLu7W5bkb4mChL9mdfm+Zg==", "license": "ISC", "dependencies": {"glob": "^7.1.6", "ignore-walk": "^3.0.3", "npm-bundled": "^1.1.1", "npm-normalize-package-bin": "^1.0.1"}, "bin": {"npm-packlist": "bin/index.js"}, "engines": {"node": ">=10"}}, "node_modules/npm-pick-manifest": {"version": "6.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-pick-manifest/-/npm-pick-manifest-6.1.1.tgz", "integrity": "sha512-dBsdBtORT84S8V8UTad1WlUyKIY9iMsAmqxHbLdeEeBNMLQDlDWWra3wYUx9EBEIiG/YwAy0XyNHDd2goAsfuA==", "license": "ISC", "dependencies": {"npm-install-checks": "^4.0.0", "npm-normalize-package-bin": "^1.0.1", "npm-package-arg": "^8.1.2", "semver": "^7.3.4"}}, "node_modules/npm-registry-fetch": {"version": "11.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-registry-fetch/-/npm-registry-fetch-11.0.0.tgz", "integrity": "sha512-jmlgSxoDNuhAtxUIG6pVwwtz840i994dL14FoNVZisrmZW5kWd63IUTNv1m/hyRSGSqWjCUp/YZlS1BJyNp9XA==", "license": "ISC", "dependencies": {"make-fetch-happen": "^9.0.1", "minipass": "^3.1.3", "minipass-fetch": "^1.3.0", "minipass-json-stream": "^1.0.1", "minizlib": "^2.0.0", "npm-package-arg": "^8.0.0"}, "engines": {"node": ">=10"}}, "node_modules/npmlog": {"version": "4.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npmlog/-/npmlog-4.1.2.tgz", "integrity": "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "node_modules/number-is-nan": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/number-is-nan/-/number-is-nan-1.0.1.tgz", "integrity": "sha512-4jbtZXNAsfZbAHiiqjLPBiCl16dES1zI4Hpzzxw61Tk+loF+sBDBKx1ICKKKwIqQ7M0mFn1TmkN7euSncWgHiQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/oauth-sign": {"version": "0.9.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object.defaults": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.defaults/-/object.defaults-1.1.0.tgz", "integrity": "sha512-c/K0mw/F11k4dEUBMW8naXUuBuhxRCfG7W+yFy8EcijU/rSmazOUd1XAEEe6bC0OuXY4HUKjTJv7xbxIMqdxrA==", "license": "MIT", "dependencies": {"array-each": "^1.0.1", "array-slice": "^1.0.0", "for-own": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.map": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.map/-/object.map-1.0.1.tgz", "integrity": "sha512-3+mAJu2PLfnSVGHwIWubpOFLscJANBKuB/6A4CxBstc4aqwQY0FWcsppuy4jU5GSB95yES5JHSI+33AWuS4k6w==", "license": "MIT", "dependencies": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.pick": {"version": "1.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/on-finished": {"version": "2.3.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/opener": {"version": "1.5.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/opener/-/opener-1.5.1.tgz", "integrity": "sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0=", "license": "(WTFPL OR MIT)", "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/p-cancelable": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-cancelable/-/p-cancelable-1.1.0.tgz", "integrity": "sha512-s73XxOZ4zpt1edZYZzvhqFa6uvQc1vwUa0K0BdtIZgQMAJj9IbebH+JkgKZc9h+B05PKHLOTl4ajG1BmNrVZlw==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/p-limit": {"version": "2.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-map": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-map/-/p-map-4.0.0.tgz", "integrity": "sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==", "license": "MIT", "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-json": {"version": "6.5.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/package-json/-/package-json-6.5.0.tgz", "integrity": "sha512-k3bdm2n25tkyxcjSKzB5x8kfVxlMdgsbPr0GkZcwHsLpba6cBjqCt1KlcChKEvxHIcTB1FVMuwoijZ26xex5MQ==", "license": "MIT", "dependencies": {"got": "^9.6.0", "registry-auth-token": "^4.0.0", "registry-url": "^5.0.0", "semver": "^6.2.0"}, "engines": {"node": ">=8"}}, "node_modules/package-json/node_modules/semver": {"version": "6.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/pacote": {"version": "11.3.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/pacote/-/pacote-11.3.5.tgz", "integrity": "sha512-fT375Yczn4zi+6Hkk2TBe1x1sP8FgFsEIZ2/iWaXY2r/NkhDJfxbcn5paz1+RTFCyNf+dPnaoBDJoAxXSU8Bkg==", "license": "ISC", "dependencies": {"@npmcli/git": "^2.1.0", "@npmcli/installed-package-contents": "^1.0.6", "@npmcli/promise-spawn": "^1.2.0", "@npmcli/run-script": "^1.8.2", "cacache": "^15.0.5", "chownr": "^2.0.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass": "^3.1.3", "mkdirp": "^1.0.3", "npm-package-arg": "^8.0.1", "npm-packlist": "^2.1.4", "npm-pick-manifest": "^6.0.0", "npm-registry-fetch": "^11.0.0", "promise-retry": "^2.0.1", "read-package-json-fast": "^2.0.1", "rimraf": "^3.0.2", "ssri": "^8.0.1", "tar": "^6.1.0"}, "bin": {"pacote": "lib/bin.js"}, "engines": {"node": ">=10"}}, "node_modules/pacote/node_modules/mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/parse-filepath": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/parse-filepath/-/parse-filepath-1.0.2.tgz", "integrity": "sha512-FwdRXKCohSVeXqwtYonZTXtbGJKrn+HNyWDYVcp5yuJlesTwNH4rsmRZ+GrKAPJ5bLpRxESMeS+Rl0VCHRvB2Q==", "license": "MIT", "dependencies": {"is-absolute": "^1.0.0", "map-cache": "^0.2.0", "path-root": "^0.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/parse-passwd": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/parse-passwd/-/parse-passwd-1.0.0.tgz", "integrity": "sha512-1Y1A//QUXEZK7YKz+rD9WydcE1+EuPr6ZBgKecAB8tmoW6UFv0NREVJe1p+jRxtThkcbbKkfwIbWJe/IeE6m2Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "license": "MIT"}, "node_modules/path-root": {"version": "0.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-root/-/path-root-0.1.1.tgz", "integrity": "sha512-QLcPegTHF11axjfojBIoDygmS2E3Lf+8+jI6wOVmNVenrKSo3mFdSGiIgdSHenczw3wPtlVMQaFVwGmM7BJdtg==", "license": "MIT", "dependencies": {"path-root-regex": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/path-root-regex": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-root-regex/-/path-root-regex-0.1.2.tgz", "integrity": "sha512-4GlJ6rZDhQZFE0DPVKh0e9jmZ5egZfxTkp7bcRDuPlJXbAwhxcl2dINPUAsjLdejqaLsCeg8axcLjIbvBjN4pQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-to-regexp": {"version": "0.1.7", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=", "license": "MIT"}, "node_modules/performance-now": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==", "license": "MIT"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/portfinder": {"version": "1.0.26", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/portfinder/-/portfinder-1.0.26.tgz", "integrity": "sha1-R1ZY1WyjC+1yrH8TeO01C9G2TnA=", "license": "MIT", "dependencies": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.1"}, "engines": {"node": ">= 0.12.0"}}, "node_modules/portfinder/node_modules/debug": {"version": "3.2.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/portfinder/node_modules/ms": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "license": "MIT"}, "node_modules/prepend-http": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/prepend-http/-/prepend-http-2.0.0.tgz", "integrity": "sha512-ravE6m9Atw9Z/jjttRUZ+clIXogdghyZAuWJ3qEzjT+jI/dL1ifAqhZeC5VHzQp1MSt1+jxKkFNemj/iO7tVUA==", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "license": "MIT"}, "node_modules/progress": {"version": "2.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/progress/-/progress-2.0.3.tgz", "integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/promise-inflight": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==", "license": "ISC"}, "node_modules/promise-retry": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/promise-retry/-/promise-retry-2.0.1.tgz", "integrity": "sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==", "license": "MIT", "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}, "node_modules/prompts": {"version": "2.4.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/prompts/-/prompts-2.4.2.tgz", "integrity": "sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/proxy-addr": {"version": "2.0.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/proxy-addr/-/proxy-addr-2.0.6.tgz", "integrity": "sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8=", "license": "MIT", "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/psl": {"version": "1.15.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/psl/-/psl-1.15.0.tgz", "integrity": "sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==", "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "funding": {"url": "https://github.com/sponsors/lupomontero"}}, "node_modules/pump": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/pump/-/pump-3.0.3.tgz", "integrity": "sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pupa": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/pupa/-/pupa-2.1.1.tgz", "integrity": "sha512-l1jNAspIBSFqbT+y+5FosojNpVpF94nlI+wDUpqP9enwOTfHx9f0gh5nB96vl+6yTpsJsypeNrwfzPrKuHB41A==", "license": "MIT", "dependencies": {"escape-goat": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/qs": {"version": "6.7.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/qs/-/qs-6.7.0.tgz", "integrity": "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/raw-body/-/raw-body-2.4.0.tgz", "integrity": "sha1-oc5vucm8NWylLoklarWQWeE9AzI=", "license": "MIT", "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/rc": {"version": "1.2.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rc/-/rc-1.2.8.tgz", "integrity": "sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==", "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/rc-config-loader": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rc-config-loader/-/rc-config-loader-3.0.0.tgz", "integrity": "sha512-bwfUSB37TWkHfP+PPjb/x8BUjChFmmBK44JMfVnU7paisWqZl/o5k7ttCH+EQLnrbn2Aq8Fo1LAsyUiz+WF4CQ==", "license": "MIT", "dependencies": {"debug": "^4.1.1", "js-yaml": "^3.12.0", "json5": "^2.1.1", "require-from-string": "^2.0.2"}}, "node_modules/rc-config-loader/node_modules/debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/rc-config-loader/node_modules/ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/read-package-json-fast": {"version": "2.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/read-package-json-fast/-/read-package-json-fast-2.0.3.tgz", "integrity": "sha512-W/BKtbL+dUjTuRL2vziuYhp76s5HZ9qQhd/dKfWIZveD0O40453QNyZhC0e63lqZrAQ4jiOapVoeJ7JrszenQQ==", "license": "ISC", "dependencies": {"json-parse-even-better-errors": "^2.3.0", "npm-normalize-package-bin": "^1.0.1"}, "engines": {"node": ">=10"}}, "node_modules/readable-stream": {"version": "2.3.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/rechoir": {"version": "0.7.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rechoir/-/rechoir-0.7.1.tgz", "integrity": "sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==", "license": "MIT", "dependencies": {"resolve": "^1.9.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/registry-auth-token": {"version": "4.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/registry-auth-token/-/registry-auth-token-4.2.2.tgz", "integrity": "sha512-PC5ZysNb42zpFME6D/XlIgtNGdTl8bBOCw90xQLVMpzuuubJKYDWFAEuUNc+Cn8Z8724tg2SDhDRrkVEsqfDMg==", "license": "MIT", "dependencies": {"rc": "1.2.8"}, "engines": {"node": ">=6.0.0"}}, "node_modules/registry-url": {"version": "5.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/registry-url/-/registry-url-5.1.0.tgz", "integrity": "sha512-8acYXXTI0AkQv6RAOjE3vOaIXZkT9wo4LOFbBKYQEEnnMNBpKqdUrI6S4NT0KPIo/WVvJ5tE/X5LF/TQUf0ekw==", "license": "MIT", "dependencies": {"rc": "^1.2.8"}, "engines": {"node": ">=8"}}, "node_modules/request": {"version": "2.88.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/request/-/request-2.88.2.tgz", "integrity": "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/request/node_modules/qs": {"version": "6.5.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/qs/-/qs-6.5.3.tgz", "integrity": "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/require-from-string": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/require-from-string/-/require-from-string-2.0.2.tgz", "integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requireg": {"version": "0.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/requireg/-/requireg-0.2.2.tgz", "integrity": "sha512-nYzyjnFcPNGR3lx9lwPPPnuQxv6JWEZd2Ci0u9opN7N5zUEPIhY/GbL3vMGOr2UXwEg9WwSyV9X9Y/kLFgPsOg==", "dependencies": {"nested-error-stacks": "~2.0.1", "rc": "~1.2.7", "resolve": "~1.7.1"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/requireg/node_modules/resolve": {"version": "1.7.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve/-/resolve-1.7.1.tgz", "integrity": "sha512-c7rwLofp8g1U+h1KNyHL/jicrKg1Ek4q+Lr33AL65uZTinUZHe30D5HlyN5V9NW0JX1D5dXQ4jqW5l7Sy/kGfw==", "license": "MIT", "dependencies": {"path-parse": "^1.0.5"}}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.10", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve/-/resolve-1.22.10.tgz", "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-dir": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve-dir/-/resolve-dir-1.0.1.tgz", "integrity": "sha512-R7uiTjECzvOsWSfdM0QKFNBVFcK27aHOUwdvK53BcW8zqnGdYp0Fbj82cy54+2A4P2tFM22J5kRfe1R+lM/1yg==", "license": "MIT", "dependencies": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/responselike": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/responselike/-/responselike-1.0.2.tgz", "integrity": "sha512-/Fpe5guzJk1gPqdJLJR5u7eG/gNY4nImjbRDaVWVMRhne55TCmj2i9Q+54PBRfatRC8v/rIiv9BN0pMd9OV5EQ==", "license": "MIT", "dependencies": {"lowercase-keys": "^1.0.0"}}, "node_modules/retry": {"version": "0.12.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/retry/-/retry-0.12.0.tgz", "integrity": "sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "deprecated": "Rimraf versions prior to v4 are no longer supported", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/secure-compare": {"version": "3.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/secure-compare/-/secure-compare-3.0.1.tgz", "integrity": "sha1-8aAymzCLIh+uN7mXTz1XjQypmeM=", "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/semver-diff": {"version": "3.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver-diff/-/semver-diff-3.1.1.tgz", "integrity": "sha512-GX0Ix/CJcHyB8c4ykpHGIAvLyOwOobtM/8d+TQkAd81/bEjgPHrfba41Vpesr7jX/t8Uh+R3EX9eAS5be+jQYg==", "license": "MIT", "dependencies": {"semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/semver-diff/node_modules/semver": {"version": "6.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/semver-utils": {"version": "1.1.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver-utils/-/semver-utils-1.1.4.tgz", "integrity": "sha512-EjnoLE5OGmDAVV/8YDoN5KiajNadjzIp9BAHOhYeQHt7j0UWxjmgsx4YD48wp4Ue1Qogq38F1GNUJNqF1kKKxA==", "license": "APACHEv2"}, "node_modules/send": {"version": "0.17.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/send/-/send-0.17.1.tgz", "integrity": "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/ms": {"version": "2.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=", "license": "MIT"}, "node_modules/serve-static": {"version": "1.14.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/serve-static/-/serve-static-1.14.1.tgz", "integrity": "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=", "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==", "license": "ISC"}, "node_modules/setprototypeof": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/setprototypeof/-/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=", "license": "ISC"}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "license": "ISC"}, "node_modules/sisteransi": {"version": "1.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/sisteransi/-/sisteransi-1.0.5.tgz", "integrity": "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==", "license": "MIT"}, "node_modules/smart-buffer": {"version": "4.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/smart-buffer/-/smart-buffer-4.2.0.tgz", "integrity": "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==", "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks": {"version": "2.8.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/socks/-/socks-2.8.6.tgz", "integrity": "sha512-pe4Y2yzru68lXCb38aAqRf5gvN8YdjP1lok5o0J7BOHljkyCGKVz7H3vpVIXKD27rj2giOJ7DwVyk/GWrPHDWA==", "license": "MIT", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks-proxy-agent": {"version": "6.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/socks-proxy-agent/-/socks-proxy-agent-6.2.1.tgz", "integrity": "sha512-a6KW9G+6B3nWZ1yB8G7pJwL3ggLy1uTzKAgCb7ttblwqdz9fMGJUuTy3uFzEP48FAs9FLILlmzDlE2JJhVQaXQ==", "license": "MIT", "dependencies": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}, "engines": {"node": ">= 10"}}, "node_modules/socks-proxy-agent/node_modules/debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/socks-proxy-agent/node_modules/ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/spawn-please": {"version": "0.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/spawn-please/-/spawn-please-0.3.0.tgz", "integrity": "sha512-gf9GJwAWhW0gnQp0dGui+nhIVICx1lGM1Ox95HzfaDBOQTauqlvHFLpo4vtAB3E377SA0YMIyRCh1w0S6R5m2w==", "license": "ISC", "engines": {"node": ">=0.10.0"}}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/sshpk": {"version": "1.18.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/sshpk/-/sshpk-1.18.0.tgz", "integrity": "sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==", "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sshpk/node_modules/jsbn": {"version": "0.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==", "license": "MIT"}, "node_modules/ssri": {"version": "8.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ssri/-/ssri-8.0.1.tgz", "integrity": "sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==", "license": "ISC", "dependencies": {"minipass": "^3.1.1"}, "engines": {"node": ">= 8"}}, "node_modules/statuses": {"version": "1.5.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/string_decoder": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/string-width": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string-width/-/string-width-1.0.2.tgz", "integrity": "sha512-0XsVpQLnVCXHJfyEs8tC0zpTVIr5PKKsQtkT29IwupnPTjtPmQ3xT/4yCREF9hYkV/3M3kzcUTSAZT6a6h81tw==", "license": "MIT", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-ansi": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==", "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tar": {"version": "6.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/tar/-/tar-6.2.1.tgz", "integrity": "sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==", "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar/node_modules/minipass": {"version": "5.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass/-/minipass-5.0.0.tgz", "integrity": "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==", "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/tar/node_modules/mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/term-size": {"version": "2.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/term-size/-/term-size-2.2.1.tgz", "integrity": "sha512-wK0Ri4fOGjv/XPy8SBHZChl8CM7uMc5VML7SqiQ0zG7+J5Vr+RMQDoHa2CNT6KHUnTGIXH34UDMkPzAUyapBZg==", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/to-readable-stream": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/to-readable-stream/-/to-readable-stream-1.0.0.tgz", "integrity": "sha512-Iq25XBt6zD5npPhlLVXGFN3/gyR2/qODcKNNyTMd4vbm39HUaOiAM4PMq0eMVC/Tkxz+Zjdsc55g9yyz+Yq00Q==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/toidentifier/-/toidentifier-1.0.0.tgz", "integrity": "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tough-cookie": {"version": "2.5.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/tough-cookie/-/tough-cookie-2.5.0.tgz", "integrity": "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==", "license": "Unlicense"}, "node_modules/type-fest": {"version": "0.8.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/type-fest/-/type-fest-0.8.1.tgz", "integrity": "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typedarray-to-buffer": {"version": "3.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz", "integrity": "sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==", "license": "MIT", "dependencies": {"is-typedarray": "^1.0.0"}}, "node_modules/unc-path-regex": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unc-path-regex/-/unc-path-regex-0.1.2.tgz", "integrity": "sha512-eXL4nmJT7oCpkZsHZUOJo8hcX3GbsiDOa0Qu9F646fi8dT3XuSVopVqAcEiVzSKKH7UoDti23wNX3qGFxcW5Qg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/union": {"version": "0.5.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/union/-/union-0.5.0.tgz", "integrity": "sha1-ssEb6E9gU4U3uEbtuboma6AJAHU=", "dependencies": {"qs": "^6.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/unique-filename": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unique-filename/-/unique-filename-1.1.1.tgz", "integrity": "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==", "license": "ISC", "dependencies": {"unique-slug": "^2.0.0"}}, "node_modules/unique-slug": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unique-slug/-/unique-slug-2.0.2.tgz", "integrity": "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}}, "node_modules/unique-string": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unique-string/-/unique-string-2.0.0.tgz", "integrity": "sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg==", "license": "MIT", "dependencies": {"crypto-random-string": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/update-notifier": {"version": "4.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/update-notifier/-/update-notifier-4.1.3.tgz", "integrity": "sha512-Yld6Z0RyCYGB6ckIjffGOSOmHXj1gMeE7aROz4MG+XMkmixBX4jUngrGXNYz7wPKBmtoD4MnBa2Anu7RSKht/A==", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boxen": "^4.2.0", "chalk": "^3.0.0", "configstore": "^5.0.1", "has-yarn": "^2.1.0", "import-lazy": "^2.1.0", "is-ci": "^2.0.0", "is-installed-globally": "^0.3.1", "is-npm": "^4.0.0", "is-yarn-global": "^0.3.0", "latest-version": "^5.0.0", "pupa": "^2.0.1", "semver-diff": "^3.1.1", "xdg-basedir": "^4.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/yeoman/update-notifier?sponsor=1"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-join": {"version": "2.0.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/url-join/-/url-join-2.0.5.tgz", "integrity": "sha1-WvIvGMBSoACkjXuCxenC4v7tpyg=", "license": "MIT"}, "node_modules/url-parse-lax": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/url-parse-lax/-/url-parse-lax-3.0.0.tgz", "integrity": "sha512-NjFKA0DidqPa5ciFcSrXnAltTtzz84ogy+NebPvfEgAck0+TNg4UJ4IN+fB7zRZfbgUf0syOo9MDxFkDSMuFaQ==", "license": "MIT", "dependencies": {"prepend-http": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "3.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/uuid/-/uuid-3.4.0.tgz", "integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/v8flags": {"version": "4.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/v8flags/-/v8flags-4.0.1.tgz", "integrity": "sha512-fcRLaS4H/hrZk9hYwbdRM35D0U8IYMfEClhXxCivOojl+yTRAZH3Zy2sSy6qVCiGbV9YAtPssP6jaChqC9vPCg==", "license": "MIT", "engines": {"node": ">= 10.13.0"}}, "node_modules/validate-npm-package-name": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/validate-npm-package-name/-/validate-npm-package-name-3.0.0.tgz", "integrity": "sha512-M6w37eVCMMouJ9V/sdPGnC5H4uDr73/+xdq0FBLO3TFFX1+7wiUY6Es328NN+y43tmY+doUdN9g9J21vqB7iLw==", "license": "ISC", "dependencies": {"builtins": "^1.0.3"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/verror": {"version": "1.10.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/verror/-/verror-1.10.0.tgz", "integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/verror/node_modules/core-util-is": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==", "license": "MIT"}, "node_modules/which": {"version": "1.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/wide-align": {"version": "1.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/wide-align/-/wide-align-1.1.5.tgz", "integrity": "sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==", "license": "ISC", "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "node_modules/widest-line": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/widest-line/-/widest-line-3.1.0.tgz", "integrity": "sha512-NsmoXalsWVDMGupxZ5R08ka9flZjjiLvHVAWYOKtiKM8ujtZWr9cRffak+uSE48+Ob8ObalXpwyeUiyDD6QFgg==", "license": "MIT", "dependencies": {"string-width": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/widest-line/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/widest-line/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/widest-line/node_modules/string-width": {"version": "4.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/widest-line/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "license": "ISC"}, "node_modules/write-file-atomic": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/write-file-atomic/-/write-file-atomic-3.0.3.tgz", "integrity": "sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "node_modules/xdg-basedir": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/xdg-basedir/-/xdg-basedir-4.0.0.tgz", "integrity": "sha512-PSNhEJDejZYV7h50BohL09Er9VaIefr2LMAf3OEmpCkjOi34eYyQYAXUTjEQtZJTKcF0E2UKTh+osDLsgNim9Q==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "license": "ISC"}}, "dependencies": {"@gar/promisify": {"version": "1.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@gar/promisify/-/promisify-1.1.3.tgz", "integrity": "sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw=="}, "@npmcli/fs": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/fs/-/fs-1.1.1.tgz", "integrity": "sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==", "requires": {"@gar/promisify": "^1.0.1", "semver": "^7.3.5"}}, "@npmcli/git": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/git/-/git-2.1.0.tgz", "integrity": "sha512-/hBFX/QG1b+N7PZBFs0bi+evgRZcK9nWBxQKZkGoXUT5hJSwl5c4d7y8/hm+NQZRPhQ67RzFaj5UM9YeyKoryw==", "requires": {"@npmcli/promise-spawn": "^1.3.2", "lru-cache": "^6.0.0", "mkdirp": "^1.0.4", "npm-pick-manifest": "^6.1.1", "promise-inflight": "^1.0.1", "promise-retry": "^2.0.1", "semver": "^7.3.5", "which": "^2.0.2"}, "dependencies": {"mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw=="}, "which": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "requires": {"isexe": "^2.0.0"}}}}, "@npmcli/installed-package-contents": {"version": "1.0.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/installed-package-contents/-/installed-package-contents-1.0.7.tgz", "integrity": "sha512-9rufe0wnJusCQoLpV9ZPKIVP55itrM5BxOXs10DmdbRfgWtHy1LDyskbwRnBghuB0PrF7pNPOqREVtpz4HqzKw==", "requires": {"npm-bundled": "^1.1.1", "npm-normalize-package-bin": "^1.0.1"}}, "@npmcli/move-file": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/move-file/-/move-file-1.1.2.tgz", "integrity": "sha512-1SUf/Cg2GzGDyaf15aR9St9TWlb+XvbZXWpDx8YKs7MLzMH/BCeopv+y9vzrzgkfykCGuWOlSu3mZhj2+FQcrg==", "requires": {"mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "dependencies": {"mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw=="}}}, "@npmcli/node-gyp": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/node-gyp/-/node-gyp-1.0.3.tgz", "integrity": "sha512-fnkhw+fmX65kiLqk6E3BFLXNC26rUhK90zVwe2yncPliVT/Qos3xjhTLE59Df8KnPlcwIERXKVlU1bXoUQ+liA=="}, "@npmcli/promise-spawn": {"version": "1.3.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/promise-spawn/-/promise-spawn-1.3.2.tgz", "integrity": "sha512-QyAGYo/Fbj4MXeGdJcFzZ+FkDkomfRBrPM+9QYJSg+PxgAUL+LU3FneQk37rKR2/zjqkCV1BLHccX98wRXG3Sg==", "requires": {"infer-owner": "^1.0.4"}}, "@npmcli/run-script": {"version": "1.8.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@npmcli/run-script/-/run-script-1.8.6.tgz", "integrity": "sha512-e42bVZnC6VluBZBAFEr3YrdqSspG3bgilyg4nSLBJ7TRGNCzxHa92XAHxQBLYg0BmgwO4b2mf3h/l5EkEWRn3g==", "requires": {"@npmcli/node-gyp": "^1.0.2", "@npmcli/promise-spawn": "^1.3.2", "node-gyp": "^7.1.0", "read-package-json-fast": "^2.0.1"}}, "@sindresorhus/is": {"version": "0.14.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@sindresorhus/is/-/is-0.14.0.tgz", "integrity": "sha512-9NET910DNaIPngYnLLPeg+Ogzqsi9uM4mSboU5y6p8S5DzMTVEsJZrawi+BoDNUVBa2DhJqQYUFvMDfgU062LQ=="}, "@szmarczak/http-timer": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@szmarczak/http-timer/-/http-timer-1.1.2.tgz", "integrity": "sha512-XIB2XbzHTN6ieIjfIMV9hlVcfPU26s2vafYWQcZHWXHOxiaRZYEDKEwdl129Zyg50+foYV2jCgtrqSA6qNuNSA==", "requires": {"defer-to-connect": "^1.0.1"}}, "@tootallnate/once": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@tootallnate/once/-/once-1.1.2.tgz", "integrity": "sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw=="}, "abbrev": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q=="}, "accepts": {"version": "1.3.7", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/accepts/-/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "requires": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}}, "agent-base": {"version": "6.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==", "requires": {"debug": "4"}, "dependencies": {"debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "requires": {"ms": "^2.1.3"}}, "ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}}}, "agentkeepalive": {"version": "4.6.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/agentkeepalive/-/agentkeepalive-4.6.0.tgz", "integrity": "sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==", "requires": {"humanize-ms": "^1.2.1"}}, "aggregate-error": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/aggregate-error/-/aggregate-error-3.1.0.tgz", "integrity": "sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==", "requires": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}}, "ajv": {"version": "6.12.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ansi-align": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-align/-/ansi-align-3.0.1.tgz", "integrity": "sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==", "requires": {"string-width": "^4.1.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="}, "string-width": {"version": "4.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "requires": {"ansi-regex": "^5.0.1"}}}}, "ansi-regex": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA=="}, "ansi-styles": {"version": "4.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "requires": {"color-convert": "^2.0.1"}}, "aproba": {"version": "1.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/aproba/-/aproba-1.2.0.tgz", "integrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw=="}, "are-we-there-yet": {"version": "1.1.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/are-we-there-yet/-/are-we-there-yet-1.1.7.tgz", "integrity": "sha512-nxwy40TuMiUGqMyRHgCSWZ9FM4VAoRP4xUYSTv5ImRog+h9yISPbVH7H8fASCIzYn9wlEv4zvFL7uKDMCFQm3g==", "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "argparse": {"version": "1.0.10", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "requires": {"sprintf-js": "~1.0.2"}}, "array-each": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/array-each/-/array-each-1.0.1.tgz", "integrity": "sha512-zHjL5SZa68hkKHBFBK6DJCTtr9sfTCPCaph/L7tMSLcTFgy+zX7E+6q5UArbtOtMBCtxdICpfTCspRse+ywyXA=="}, "array-flatten": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}, "array-slice": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/array-slice/-/array-slice-1.1.0.tgz", "integrity": "sha512-B1qMD3RBP7O8o0H2KbrXDyB0IccejMF15+87Lvlor12ONPRHP6gTjXMNkt/d3ZuOGbAe66hFmaCfECI24Ufp6w=="}, "asn1": {"version": "0.2.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "requires": {"safer-buffer": "~2.1.0"}}, "assert-plus": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw=="}, "async": {"version": "2.6.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/async/-/async-2.6.3.tgz", "integrity": "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=", "requires": {"lodash": "^4.17.14"}}, "asynckit": {"version": "0.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "aws-sign2": {"version": "0.7.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA=="}, "aws4": {"version": "1.13.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/aws4/-/aws4-1.13.2.tgz", "integrity": "sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw=="}, "balanced-match": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="}, "basepack": {"version": "1.14.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/basepack/-/basepack-1.14.1.tgz", "integrity": "sha512-26MpRYEYx9tej+YYZSzJEOyEHMwRLfXPpTvca7/NYDjjo61l7kwwRk6xabadAyvXNA0QtKrl0cmGDbDLv7Zd2Q=="}, "basic-auth": {"version": "1.1.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/basic-auth/-/basic-auth-1.1.0.tgz", "integrity": "sha1-RSIe5Cn37h5QNb4/UVM/HN/SmIQ="}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "requires": {"tweetnacl": "^0.14.3"}}, "body-parser": {"version": "1.19.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/body-parser/-/body-parser-1.19.0.tgz", "integrity": "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=", "requires": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}}, "boxen": {"version": "4.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/boxen/-/boxen-4.2.0.tgz", "integrity": "sha512-eB4uT9RGzg2odpER62bBwSLvUeGC+WbRjjyyFhGsKnc8wp/m0+hQsMUvUe3H2V0D5vw0nBdO1hCJoZo5mKeuIQ==", "requires": {"ansi-align": "^3.0.0", "camelcase": "^5.3.1", "chalk": "^3.0.0", "cli-boxes": "^2.2.0", "string-width": "^4.1.0", "term-size": "^2.1.0", "type-fest": "^0.8.1", "widest-line": "^3.1.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="}, "string-width": {"version": "4.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "requires": {"ansi-regex": "^5.0.1"}}}}, "brace-expansion": {"version": "1.1.12", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "requires": {"fill-range": "^7.1.1"}}, "builtins": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/builtins/-/builtins-1.0.3.tgz", "integrity": "sha512-uYBjakWipfaO/bXI7E8rq6kpwHRZK5cNYrUv2OzZSI/FvmdMyXJ2tG9dKcjEC5YHmHpUAwsargWIZNWdxb/bnQ=="}, "bytes": {"version": "3.1.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/bytes/-/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="}, "cacache": {"version": "15.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cacache/-/cacache-15.3.0.tgz", "integrity": "sha512-VVdYzXEn+cnbXpFgWs5hTT7OScegHVmLhJIR8Ufqk3iFD6A6j5iSX1KuBTfNEv4tdJWE2PzA6IVFtcLC7fN9wQ==", "requires": {"@npmcli/fs": "^1.0.0", "@npmcli/move-file": "^1.0.1", "chownr": "^2.0.0", "fs-minipass": "^2.0.0", "glob": "^7.1.4", "infer-owner": "^1.0.4", "lru-cache": "^6.0.0", "minipass": "^3.1.1", "minipass-collect": "^1.0.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.2", "mkdirp": "^1.0.3", "p-map": "^4.0.0", "promise-inflight": "^1.0.1", "rimraf": "^3.0.2", "ssri": "^8.0.1", "tar": "^6.0.2", "unique-filename": "^1.1.1"}, "dependencies": {"mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw=="}}}, "cacheable-request": {"version": "6.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cacheable-request/-/cacheable-request-6.1.0.tgz", "integrity": "sha512-<PERSON>j3cAGPCqOZX7Rz64Uny2GYAZNliQSqfbePrgAQ1wKAihYmCUnraBtJtKcGR4xz7wF+LoJC+ssFZvv5BgF9Igg==", "requires": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^3.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^4.1.0", "responselike": "^1.0.2"}, "dependencies": {"get-stream": {"version": "5.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/get-stream/-/get-stream-5.2.0.tgz", "integrity": "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==", "requires": {"pump": "^3.0.0"}}, "lowercase-keys": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/lowercase-keys/-/lowercase-keys-2.0.0.tgz", "integrity": "sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA=="}}}, "camelcase": {"version": "5.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="}, "caseless": {"version": "0.12.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/caseless/-/caseless-0.12.0.tgz", "integrity": "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw=="}, "chalk": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/chalk/-/chalk-3.0.0.tgz", "integrity": "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "chownr": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/chownr/-/chownr-2.0.0.tgz", "integrity": "sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ=="}, "ci-info": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ci-info/-/ci-info-2.0.0.tgz", "integrity": "sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ=="}, "cint": {"version": "8.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cint/-/cint-8.2.1.tgz", "integrity": "sha512-gyWqJHXgDFPNx7PEyFJotutav+al92TTC3dWlMFyTETlOyKBQMZb7Cetqmj3GlrnSILHwSJRwf4mIGzc7C5lXw=="}, "clean-stack": {"version": "2.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/clean-stack/-/clean-stack-2.2.0.tgz", "integrity": "sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A=="}, "cli-boxes": {"version": "2.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cli-boxes/-/cli-boxes-2.2.1.tgz", "integrity": "sha512-y4coMcylgSCdVinjiDBuR8PCC2bLjyGTwEmPb9NHR/QaNU6EUOXcTY/s6VjGMD6ENSEaeQYHCY0GNGS5jfMwPw=="}, "cli-table": {"version": "0.3.11", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/cli-table/-/cli-table-0.3.11.tgz", "integrity": "sha512-IqLQi4lO0nIB4tcdTpN4LCB9FI3uqrJZK7RC515EnhZ6qBaglkIgICb1wjeAqpdoOabm1+SuQtkXIPdYC93jhQ==", "requires": {"colors": "1.0.3"}, "dependencies": {"colors": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/colors/-/colors-1.0.3.tgz", "integrity": "sha512-pFGrxThWcWQ2MsAz6RtgeWe4NK2kUE1WfsrvvlctdII745EW9I0yflqhe7++M5LEc7bV2c/9/5zc8sFcpL0Drw=="}}}, "clone-response": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/clone-response/-/clone-response-1.0.3.tgz", "integrity": "sha512-ROoL94jJH2dUVML2Y/5PEDNaSHgeOdSDicUyS7izcF63G6sTc/FTjLub4b8Il9S8S0beOfYt0TaA5qvFK+w0wA==", "requires": {"mimic-response": "^1.0.0"}}, "code-point-at": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/code-point-at/-/code-point-at-1.1.0.tgz", "integrity": "sha512-RpAVKQA5T63xEj6/giIbUEtZwJ4UFIc3ZtvEkiaUERylqe8xb5IvqcgOurZLahv93CLKfxcw5YI+DZcUBRyLXA=="}, "color-convert": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "colors": {"version": "1.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/colors/-/colors-1.4.0.tgz", "integrity": "sha1-xQSRR51MG9rtLJztMs98fcI2D3g="}, "combined-stream": {"version": "1.0.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "5.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/commander/-/commander-5.1.0.tgz", "integrity": "sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg=="}, "concat-map": {"version": "0.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="}, "configstore": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/configstore/-/configstore-5.0.1.tgz", "integrity": "sha512-aMKprgk5YhBNyH25hj8wGt2+D52Sw1DRRIzqBwLp2Ya9mFmY8KPvvtvmna8SxVR9JMZ4kzMD68N22vlaRpkeFA==", "requires": {"dot-prop": "^5.2.0", "graceful-fs": "^4.1.2", "make-dir": "^3.0.0", "unique-string": "^2.0.0", "write-file-atomic": "^3.0.0", "xdg-basedir": "^4.0.0"}}, "console-control-strings": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/console-control-strings/-/console-control-strings-1.1.0.tgz", "integrity": "sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ=="}, "content-disposition": {"version": "0.5.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/content-disposition/-/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "requires": {"safe-buffer": "5.1.2"}}, "content-type": {"version": "1.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/content-type/-/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="}, "cookie": {"version": "0.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cookie/-/cookie-0.4.0.tgz", "integrity": "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo="}, "cookie-signature": {"version": "1.0.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "core-util-is": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="}, "cors": {"version": "2.8.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cors/-/cors-2.8.5.tgz", "integrity": "sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=", "requires": {"object-assign": "^4", "vary": "^1"}}, "corser": {"version": "2.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/corser/-/corser-2.0.1.tgz", "integrity": "sha1-jtolLsqrWEDc2XXOuQ2TcMgZ/4c="}, "crypto-random-string": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/crypto-random-string/-/crypto-random-string-2.0.0.tgz", "integrity": "sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA=="}, "dashdash": {"version": "1.14.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "requires": {"assert-plus": "^1.0.0"}}, "debug": {"version": "2.6.9", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "decompress-response": {"version": "3.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/decompress-response/-/decompress-response-3.3.0.tgz", "integrity": "sha512-BzRPQuY1ip+qDonAOz42gRm/pg9F768C+npV/4JOsxRC2sq+Rlk+Q4ZCAsOhnIaMrgarILY+RMUIvMmmX1qAEA==", "requires": {"mimic-response": "^1.0.0"}}, "deep-extend": {"version": "0.6.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/deep-extend/-/deep-extend-0.6.0.tgz", "integrity": "sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA=="}, "defer-to-connect": {"version": "1.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/defer-to-connect/-/defer-to-connect-1.1.3.tgz", "integrity": "sha512-0ISdNousHvZT2EiFlZeZAHBUvSxmKswVCEf8hW7KWgG4a8MVEu/3Vb6uWYozkjylyCxe0JBIiRB1jV45S70WVQ=="}, "delayed-stream": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="}, "delegates": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/delegates/-/delegates-1.0.0.tgz", "integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ=="}, "depd": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "destroy": {"version": "1.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="}, "detect-file": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/detect-file/-/detect-file-1.0.0.tgz", "integrity": "sha512-DtCOLG98P007x7wiiOmfI0fi3eIKyWiLTGJ2MDnVi/E04lWGbf+JzrRHMm0rgIIZJGtHpKpbVgLWHrv8xXpc3Q=="}, "dot-prop": {"version": "5.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/dot-prop/-/dot-prop-5.3.0.tgz", "integrity": "sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==", "requires": {"is-obj": "^2.0.0"}}, "duplexer3": {"version": "0.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/duplexer3/-/duplexer3-0.1.5.tgz", "integrity": "sha512-1A8za6ws41LQgv9HrE/66jyC5yuSjQ3L/KOpFtoBilsAK2iA2wuS5rTt1OCzIvtS2V7nVmedsUU+DGRcjBmOYA=="}, "ecc-jsbn": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}, "dependencies": {"jsbn": {"version": "0.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="}}}, "ecstatic": {"version": "3.3.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ecstatic/-/ecstatic-3.3.2.tgz", "integrity": "sha1-bR3UmBTQBZRoLGUq22YHamnUbEg=", "requires": {"he": "^1.1.1", "mime": "^1.6.0", "minimist": "^1.1.0", "url-join": "^2.0.5"}}, "ee-first": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "emoji-regex": {"version": "8.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "encodeurl": {"version": "1.0.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "encoding": {"version": "0.1.13", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "optional": true, "requires": {"iconv-lite": "^0.6.2"}, "dependencies": {"iconv-lite": {"version": "0.6.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "optional": true, "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}}}, "end-of-stream": {"version": "1.4.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/end-of-stream/-/end-of-stream-1.4.5.tgz", "integrity": "sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==", "requires": {"once": "^1.4.0"}}, "env-paths": {"version": "2.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/env-paths/-/env-paths-2.2.1.tgz", "integrity": "sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A=="}, "err-code": {"version": "2.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/err-code/-/err-code-2.0.3.tgz", "integrity": "sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA=="}, "es6-promise": {"version": "4.2.8", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/es6-promise/-/es6-promise-4.2.8.tgz", "integrity": "sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo="}, "escape-goat": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/escape-goat/-/escape-goat-2.1.1.tgz", "integrity": "sha512-8/uIhbG12Csjy2JEW7D9pHbreaVaS/OpN3ycnyvElTdwM5n6GY6W6e2IPemfvGZeUMqZ9A/3GqIZMgKnBhAw/Q=="}, "escape-html": {"version": "1.0.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="}, "esprima": {"version": "4.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="}, "etag": {"version": "1.8.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "eventemitter3": {"version": "4.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/eventemitter3/-/eventemitter3-4.0.4.tgz", "integrity": "sha1-tUY6zmNaCD0Bi9x8kXtMXxCoU4Q="}, "expand-tilde": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/expand-tilde/-/expand-tilde-2.0.2.tgz", "integrity": "sha512-A5EmesHW6rfnZ9ysHQjPdJRni0SRar0tjtG5MNtm9n5TUvsYU8oozprtRD4AqHxcZWWlVuAmQo2nWKfN9oyjTw==", "requires": {"homedir-polyfill": "^1.0.1"}}, "express": {"version": "4.17.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/express/-/express-4.17.1.tgz", "integrity": "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=", "requires": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}}, "express-http-proxy": {"version": "1.6.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/express-http-proxy/-/express-http-proxy-1.6.0.tgz", "integrity": "sha1-hnKxCTzJa4qT6OPalI3REaZo7yI=", "requires": {"debug": "^3.0.1", "es6-promise": "^4.1.1", "raw-body": "^2.3.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "extend": {"version": "3.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="}, "extsprintf": {"version": "1.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g=="}, "fast-deep-equal": {"version": "3.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}, "fast-diff": {"version": "1.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fast-diff/-/fast-diff-1.3.0.tgz", "integrity": "sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw=="}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="}, "figgy-pudding": {"version": "3.5.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/figgy-pudding/-/figgy-pudding-3.5.2.tgz", "integrity": "sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw=="}, "fill-range": {"version": "7.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "requires": {"to-regex-range": "^5.0.1"}}, "finalhandler": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/finalhandler/-/finalhandler-1.1.2.tgz", "integrity": "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=", "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}}, "find-up": {"version": "4.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "findup-sync": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/findup-sync/-/findup-sync-4.0.0.tgz", "integrity": "sha512-6jvvn/12IC4quLBL1KNokxC7wWTvYncaVUYSoxWw7YykPLuRrnv4qdHcSOywOI5RpkOVGeQRtWM8/q+G6W6qfQ==", "requires": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^4.0.2", "resolve-dir": "^1.0.1"}}, "fined": {"version": "1.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fined/-/fined-1.2.0.tgz", "integrity": "sha512-ZYDqPLGxDkDhDZBjZBb+oD1+j0rA4E0pXY50eplAAOPg2N/gUBSSk5IM1/QhPfyVo19lJ+CvXpqfvk+b2p/8Ng==", "requires": {"expand-tilde": "^2.0.2", "is-plain-object": "^2.0.3", "object.defaults": "^1.1.0", "object.pick": "^1.2.0", "parse-filepath": "^1.0.1"}}, "flagged-respawn": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/flagged-respawn/-/flagged-respawn-1.0.1.tgz", "integrity": "sha512-lNaHNVymajmk0OJMBn8fVUAU1BtDeKIqKoVhk4xAALB57aALg6b4W0MfJ/cUE0g9YBXy5XhSlPIpYIJ7HaY/3Q=="}, "follow-redirects": {"version": "1.12.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/follow-redirects/-/follow-redirects-1.12.1.tgz", "integrity": "sha1-3lSmIFMRuT1gOY68Ac9wFWgjErY="}, "for-in": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/for-in/-/for-in-1.0.2.tgz", "integrity": "sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ=="}, "for-own": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/for-own/-/for-own-1.0.0.tgz", "integrity": "sha512-0OABksIGrxKK8K4kynWkQ7y1zounQxP+CWnyclVwj81KW3vlLlGUx57DKGcP/LH216GzqnstnPocF16Nxs0Ycg==", "requires": {"for-in": "^1.0.1"}}, "forever-agent": {"version": "0.6.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha512-j0K<PERSON><PERSON>hm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw=="}, "form-data": {"version": "2.3.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "forwarded": {"version": "0.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="}, "fresh": {"version": "0.5.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "fs-minipass": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fs-minipass/-/fs-minipass-2.1.0.tgz", "integrity": "sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==", "requires": {"minipass": "^3.0.0"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="}, "function-bind": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="}, "fuxi": {"version": "2.16.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxi/-/fuxi-2.16.2.tgz", "integrity": "sha512-Ac7DB03Nx32raJg6Ql4kyCnhHcpLJg6NT0fZBc/DwxETruatHwK/Ncv1XUiYINIGJ9+3f0/y6Q7GgEE4+lPT4g==", "requires": {"grunt-cli": "^1.3.2", "npm-check-updates": "^4.1.2"}}, "fuxi-nossis": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxi-nossis/-/fuxi-nossis-3.1.0.tgz", "integrity": "sha512-XxERS48iXtmsxCv7Xi/snQKas10OvQJRZoc3yspYUltmU6Mgss7s/KEpxqJqitqTP+8YSpJYqYsCprp4loxpEA=="}, "gauge": {"version": "2.7.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/gauge/-/gauge-2.7.4.tgz", "integrity": "sha512-14x4kjc6lkD3ltw589k0NrPD6cCNTD6CWoVUNpB85+DrtONoZn+Rug6xZU5RvSC4+TZPxA5AnBibQYAvZn41Hg==", "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "get-stdin": {"version": "7.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/get-stdin/-/get-stdin-7.0.0.tgz", "integrity": "sha512-zRKcywvrXlXsA0v0i9Io4KDRaAw7+a1ZpjRwl9Wox8PFlVCCHra7E9c4kqXCoCM9nR5tBkaTTZRBoCm60bFqTQ=="}, "get-stream": {"version": "4.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/get-stream/-/get-stream-4.1.0.tgz", "integrity": "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==", "requires": {"pump": "^3.0.0"}}, "getpass": {"version": "0.1.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "requires": {"assert-plus": "^1.0.0"}}, "glob": {"version": "7.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "global-dirs": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-dirs/-/global-dirs-2.1.0.tgz", "integrity": "sha512-MG6kdOUh/xBnyo9cJFeIKkLEc1AyFq42QTU4XiX51i2NEdxLxLWXIjEjmqKeSuKR7pAZjTqUVoT2b2huxVLgYQ==", "requires": {"ini": "1.3.7"}, "dependencies": {"ini": {"version": "1.3.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ini/-/ini-1.3.7.tgz", "integrity": "sha512-iKpRpXP+CrP2jyrxvg1kMUpXDyRUFDWurxbnVT1vQPx+Wz9uCYsMIqYuSBLV+PAaZG/d7kRLKRFc9oDMsH+mFQ=="}}}, "global-modules": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-modules/-/global-modules-1.0.0.tgz", "integrity": "sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg==", "requires": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}}, "global-prefix": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-prefix/-/global-prefix-1.0.2.tgz", "integrity": "sha512-5lsx1NUDHtSjfg0eHlmYvZKv8/nVqX4ckFbM+FrGcQ+04KWcWFo9P5MxPZYSzUvyzmdTbI7Eix8Q4IbELDqzKg==", "requires": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}}, "got": {"version": "9.6.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/got/-/got-9.6.0.tgz", "integrity": "sha512-R7eWptXuGYxwijs0eV+v3o6+XH1IqVK8dJOEecQfTmkncw9AV4dcw/Dhxi8MdlqPthxxpZyizMzyg8RTmEsG+Q==", "requires": {"@sindresorhus/is": "^0.14.0", "@szmarczak/http-timer": "^1.1.2", "cacheable-request": "^6.0.0", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^4.1.0", "lowercase-keys": "^1.0.1", "mimic-response": "^1.0.1", "p-cancelable": "^1.0.0", "to-readable-stream": "^1.0.0", "url-parse-lax": "^3.0.0"}}, "graceful-fs": {"version": "4.2.11", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="}, "grunt-cli": {"version": "1.5.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/grunt-cli/-/grunt-cli-1.5.0.tgz", "integrity": "sha512-rILKAFoU0dzlf22SUfDtq2R1fosChXXlJM5j7wI6uoW8gwmXDXzbUvirlKZSYCdXl3LXFbR+8xyS+WFo+b6vlA==", "requires": {"grunt-known-options": "~2.0.0", "interpret": "~1.1.0", "liftup": "~3.0.1", "nopt": "~5.0.0", "v8flags": "^4.0.1"}}, "grunt-known-options": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/grunt-known-options/-/grunt-known-options-2.0.0.tgz", "integrity": "sha512-GD7cTz0I4SAede1/+pAbmJRG44zFLPipVtdL9o3vqx9IEyb7b4/Y3s7r6ofI3CchR5GvYJ+8buCSioDv5dQLiA=="}, "har-schema": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q=="}, "har-validator": {"version": "5.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/har-validator/-/har-validator-5.1.5.tgz", "integrity": "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==", "requires": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}}, "has-ansi": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==", "requires": {"ansi-regex": "^2.0.0"}}, "has-flag": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="}, "has-unicode": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ=="}, "has-yarn": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/has-yarn/-/has-yarn-2.1.0.tgz", "integrity": "sha512-UqBRqi4ju7T+TqGNdqAO0PaSVGsDGJUBQvk9eUWNGRY1CFGDzYhLWoM7JQEemnlvVcv/YEmc2wNW8BC24EnUsw=="}, "hasown": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "requires": {"function-bind": "^1.1.2"}}, "he": {"version": "1.2.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/he/-/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="}, "homedir-polyfill": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz", "integrity": "sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA==", "requires": {"parse-passwd": "^1.0.0"}}, "hosted-git-info": {"version": "4.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/hosted-git-info/-/hosted-git-info-4.1.0.tgz", "integrity": "sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==", "requires": {"lru-cache": "^6.0.0"}}, "http-cache-semantics": {"version": "4.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz", "integrity": "sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ=="}, "http-errors": {"version": "1.7.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-errors/-/http-errors-1.7.2.tgz", "integrity": "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}}, "http-proxy": {"version": "1.18.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "requires": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-proxy-agent": {"version": "4.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "integrity": "sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==", "requires": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "dependencies": {"debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "requires": {"ms": "^2.1.3"}}, "ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}}}, "http-server": {"version": "0.12.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-server/-/http-server-0.12.3.tgz", "integrity": "sha1-ugRx0OzEJYhmFss1xPryeRQKDTc=", "requires": {"basic-auth": "^1.0.3", "colors": "^1.4.0", "corser": "^2.0.1", "ecstatic": "^3.3.2", "http-proxy": "^1.18.0", "minimist": "^1.2.5", "opener": "^1.5.1", "portfinder": "^1.0.25", "secure-compare": "3.0.1", "union": "~0.5.0"}}, "http-signature": {"version": "1.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==", "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "https-proxy-agent": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "integrity": "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==", "requires": {"agent-base": "6", "debug": "4"}, "dependencies": {"debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "requires": {"ms": "^2.1.3"}}, "ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}}}, "humanize-ms": {"version": "1.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/humanize-ms/-/humanize-ms-1.2.1.tgz", "integrity": "sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==", "requires": {"ms": "^2.0.0"}}, "iconv-lite": {"version": "0.4.24", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore-walk": {"version": "3.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ignore-walk/-/ignore-walk-3.0.4.tgz", "integrity": "sha512-PY6Ii8o1jMRA1z4F2hRkH/xN59ox43DavKvD3oDpfurRlOJyAHpifIwpbdv1n4jt4ov0jSpw3kQ4GhJnpBL6WQ==", "requires": {"minimatch": "^3.0.4"}}, "import-lazy": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/import-lazy/-/import-lazy-2.1.0.tgz", "integrity": "sha512-m7ZEHgtw69qOGw+jwxXkHlrlIPdTGkyh66zXZ1ajZbxkDBNjSY/LGbmjc7h0s2ELsUDTAhFr55TrPSSqJGPG0A=="}, "imurmurhash": {"version": "0.1.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="}, "indent-string": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/indent-string/-/indent-string-4.0.0.tgz", "integrity": "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg=="}, "infer-owner": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/infer-owner/-/infer-owner-1.0.4.tgz", "integrity": "sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A=="}, "inflight": {"version": "1.0.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "ini": {"version": "1.3.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ini/-/ini-1.3.8.tgz", "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="}, "interpret": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/interpret/-/interpret-1.1.0.tgz", "integrity": "sha512-CLM8SNMDu7C5psFCn6Wg/tgpj/bKAg7hc2gWqcuR9OD5Ft9PhBpIu8PLicPeis+xDd6YX2ncI8MCA64I9tftIA=="}, "ip-address": {"version": "9.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ip-address/-/ip-address-9.0.5.tgz", "integrity": "sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==", "requires": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "dependencies": {"sprintf-js": {"version": "1.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/sprintf-js/-/sprintf-js-1.1.3.tgz", "integrity": "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA=="}}}, "ipaddr.js": {"version": "1.9.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="}, "is-absolute": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-absolute/-/is-absolute-1.0.0.tgz", "integrity": "sha512-dOWoqflvcydARa360Gvv18DZ/gRuHKi2NU/wU5X1ZFzdYfH29nkiNZsF3mp4OJ3H4yo9Mx8A/uAGNzpzPN3yBA==", "requires": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}}, "is-ci": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-ci/-/is-ci-2.0.0.tgz", "integrity": "sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==", "requires": {"ci-info": "^2.0.0"}}, "is-core-module": {"version": "2.16.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "requires": {"hasown": "^2.0.2"}}, "is-extglob": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha512-1pqUqRjkhPJ9miNq9SwMfdvi6lBJcd6eFxvfaivQhaH3SgisfiuudvFntdKOmxuee/77l+FPjKrQjWvmPjWrRw==", "requires": {"number-is-nan": "^1.0.0"}}, "is-glob": {"version": "4.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "requires": {"is-extglob": "^2.1.1"}}, "is-installed-globally": {"version": "0.3.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-installed-globally/-/is-installed-globally-0.3.2.tgz", "integrity": "sha512-wZ8x1js7Ia0kecP/CHM/3ABkAmujX7WPvQk6uu3Fly/Mk44pySulQpnHG46OMjHGXApINnV4QhY3SWnECO2z5g==", "requires": {"global-dirs": "^2.0.1", "is-path-inside": "^3.0.1"}}, "is-lambda": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-lambda/-/is-lambda-1.0.1.tgz", "integrity": "sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ=="}, "is-npm": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-npm/-/is-npm-4.0.0.tgz", "integrity": "sha512-96ECIfh9xtDDlPylNPXhzjsykHsMJZ18ASpaWzQyBr4YRTcVjUvzaHayDAES2oU/3KpljhHUjtSRNiDwi0F0ig=="}, "is-number": {"version": "7.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="}, "is-obj": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-obj/-/is-obj-2.0.0.tgz", "integrity": "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w=="}, "is-path-inside": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-path-inside/-/is-path-inside-3.0.3.tgz", "integrity": "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ=="}, "is-plain-object": {"version": "2.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "requires": {"isobject": "^3.0.1"}}, "is-relative": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-relative/-/is-relative-1.0.0.tgz", "integrity": "sha512-Kw/ReK0iqwKeu0MITLFuj0jbPAmEiOsIwyIXvvbfa6QfmN9pkD1M+8pdk7Rl/dTKbH34/XBFMbgD4iMJhLQbGA==", "requires": {"is-unc-path": "^1.0.0"}}, "is-typedarray": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="}, "is-unc-path": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-unc-path/-/is-unc-path-1.0.0.tgz", "integrity": "sha512-mrGpVd0fs7WWLfVsStvgF6iEJnbjDFZh9/emhRDcGWTduTfNHd9CHeUwH3gYIjdbwo4On6hunkztwOaAw0yllQ==", "requires": {"unc-path-regex": "^0.1.2"}}, "is-windows": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="}, "is-yarn-global": {"version": "0.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-yarn-global/-/is-yarn-global-0.3.0.tgz", "integrity": "sha512-VjSeb/lHmkoyd8ryPVIKvOCn4D1koMqY+vqyjjUfc3xyKtP4dYOxM44sZrnqQSzSds3xyOrUTLTC9LVCVgLngw=="}, "isarray": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="}, "isexe": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="}, "isobject": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isobject/-/isobject-3.0.1.tgz", "integrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg=="}, "isstream": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isstream/-/isstream-0.1.2.tgz", "integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g=="}, "jju": {"version": "1.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jju/-/jju-1.4.0.tgz", "integrity": "sha512-8wb9Yw966OSxApiCt0K3yNJL8pnNeIv+OEq2YMidz4FKP6nonSRoOXc80iXY4JaN2FC11B9qsNmDsm+ZOfMROA=="}, "js-yaml": {"version": "3.14.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/js-yaml/-/js-yaml-3.14.1.tgz", "integrity": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsbn/-/jsbn-1.1.0.tgz", "integrity": "sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A=="}, "json-buffer": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-buffer/-/json-buffer-3.0.0.tgz", "integrity": "sha512-CuUqjv0FUZIdXkHPI8MezCnFCdaTAacej1TZYulLoAg1h/PhwkdXFN4V/gzY4g+fMBCOV2xF+rp7t2XD2ns/NQ=="}, "json-parse-even-better-errors": {"version": "2.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="}, "json-parse-helpfulerror": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-parse-helpfulerror/-/json-parse-helpfulerror-1.0.3.tgz", "integrity": "sha512-XgP0FGR77+QhUxjXkwOMkC94k3WtqEBfcnjWqhRd82qTat4SWKRE+9kUnynz/shm3I4ea2+qISvTIeGTNU7kJg==", "requires": {"jju": "^1.1.0"}}, "json-schema": {"version": "0.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-schema/-/json-schema-0.4.0.tgz", "integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="}, "json-stringify-safe": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA=="}, "json5": {"version": "2.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="}, "jsonparse": {"version": "1.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsonparse/-/jsonparse-1.3.1.tgz", "integrity": "sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg=="}, "jsprim": {"version": "1.4.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsprim/-/jsprim-1.4.2.tgz", "integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}}, "keyv": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/keyv/-/keyv-3.1.0.tgz", "integrity": "sha512-9ykJ/46SN/9KPM/sichzQ7OvXyGDYKGTaDlKMGCAlg2UK8KRy4jb0d8sFc+0Tt0YYnThq8X2RZgCg74RPxgcVA==", "requires": {"json-buffer": "3.0.0"}}, "kind-of": {"version": "6.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="}, "kleur": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/kleur/-/kleur-3.0.3.tgz", "integrity": "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w=="}, "latest-version": {"version": "5.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/latest-version/-/latest-version-5.1.0.tgz", "integrity": "sha512-weT+r0kTkRQdCdYCNtkMwWXQTMEswKrFBkm4ckQOMVhhqhIMI1UT2hMj+1iigIhgSZm5gTmrRXBNoGUgaTY1xA==", "requires": {"package-json": "^6.3.0"}}, "libnpmconfig": {"version": "1.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/libnpmconfig/-/libnpmconfig-1.2.1.tgz", "integrity": "sha512-9esX8rTQAHqarx6qeZqmGQKBNZR5OIbl/Ayr0qQDy3oXja2iFVQQI81R6GZ2a02bSNZ9p3YOGX1O6HHCb1X7kA==", "requires": {"figgy-pudding": "^3.5.1", "find-up": "^3.0.0", "ini": "^1.3.5"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/find-up/-/find-up-3.0.0.tgz", "integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==", "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ=="}}}, "liftup": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/liftup/-/liftup-3.0.1.tgz", "integrity": "sha512-yR<PERSON>aiQDizWSzoXk3APcA71eOI/UuhEkNN9DiW2Tt44mhYzX4joFoCZlxsSOF7RyeLlfqzFLQI1ngFq3ggMPhOw==", "requires": {"extend": "^3.0.2", "findup-sync": "^4.0.0", "fined": "^1.2.0", "flagged-respawn": "^1.0.1", "is-plain-object": "^2.0.4", "object.map": "^1.0.1", "rechoir": "^0.7.0", "resolve": "^1.19.0"}}, "locate-path": {"version": "5.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "requires": {"p-locate": "^4.1.0"}}, "lodash": {"version": "4.17.17", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/lodash/-/lodash-4.17.17.tgz", "integrity": "sha1-2QGLOsxXqVydz0pFxrY7h3tsLUU="}, "lowercase-keys": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/lowercase-keys/-/lowercase-keys-1.0.1.tgz", "integrity": "sha512-G2Lj61tXDnVFFOi8VZds+SoQjtQC3dgokKdDG2mTm1tx4m50NUHBOZSBwQQHyy0V12A0JTG4icfZQH+xPyh8VA=="}, "lru-cache": {"version": "6.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "requires": {"yallist": "^4.0.0"}}, "make-dir": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==", "requires": {"semver": "^6.0.0"}, "dependencies": {"semver": {"version": "6.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="}}}, "make-fetch-happen": {"version": "9.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/make-fetch-happen/-/make-fetch-happen-9.1.0.tgz", "integrity": "sha512-+zopwDy7DNknmwPQplem5lAZX/eCOzSvSNNcSKm5eVwTkOBzoktEfXsa9L23J/GIRhxRsaxzkPEhrJEpE2F4Gg==", "requires": {"agentkeepalive": "^4.1.3", "cacache": "^15.2.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "minipass": "^3.1.3", "minipass-collect": "^1.0.2", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "socks-proxy-agent": "^6.0.0", "ssri": "^8.0.0"}}, "make-iterator": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/make-iterator/-/make-iterator-1.0.1.tgz", "integrity": "sha512-pxiuXh0iVEq7VM7KMIhs5gxsfxCux2URptUQaXo4iZZJxBAzTPOLE2BumO5dbfVYq/hBJFBR/a1mFDmOx5AGmw==", "requires": {"kind-of": "^6.0.2"}}, "map-cache": {"version": "0.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg=="}, "media-typer": {"version": "0.3.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "merge-descriptors": {"version": "1.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "methods": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "micromatch": {"version": "4.0.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "requires": {"braces": "^3.0.3", "picomatch": "^2.3.1"}}, "mime": {"version": "1.6.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="}, "mime-db": {"version": "1.44.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime-db/-/mime-db-1.44.0.tgz", "integrity": "sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I="}, "mime-types": {"version": "2.1.27", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime-types/-/mime-types-2.1.27.tgz", "integrity": "sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=", "requires": {"mime-db": "1.44.0"}}, "mimic-response": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mimic-response/-/mimic-response-1.0.1.tgz", "integrity": "sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ=="}, "minimatch": {"version": "3.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/minimist/-/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="}, "minipass": {"version": "3.3.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "requires": {"yallist": "^4.0.0"}}, "minipass-collect": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-collect/-/minipass-collect-1.0.2.tgz", "integrity": "sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==", "requires": {"minipass": "^3.0.0"}}, "minipass-fetch": {"version": "1.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-fetch/-/minipass-fetch-1.4.1.tgz", "integrity": "sha512-CGH1eblLq26Y15+Azk7ey4xh0J/XfJfrCox5LDJiKqI2Q2iwOLOKrlmIaODiSQS8d18jalF6y2K2ePUm0CmShw==", "requires": {"encoding": "^0.1.12", "minipass": "^3.1.0", "minipass-sized": "^1.0.3", "minizlib": "^2.0.0"}}, "minipass-flush": {"version": "1.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-flush/-/minipass-flush-1.0.5.tgz", "integrity": "sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==", "requires": {"minipass": "^3.0.0"}}, "minipass-json-stream": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-json-stream/-/minipass-json-stream-1.0.2.tgz", "integrity": "sha512-myxeeTm57lYs8pH2nxPzmEEg8DGIgW+9mv6D4JZD2pa81I/OBjeU7PtICXV6c9eRGTA5JMDsuIPUZRCyBMYNhg==", "requires": {"jsonparse": "^1.3.1", "minipass": "^3.0.0"}}, "minipass-pipeline": {"version": "1.2.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz", "integrity": "sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==", "requires": {"minipass": "^3.0.0"}}, "minipass-sized": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass-sized/-/minipass-sized-1.0.3.tgz", "integrity": "sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==", "requires": {"minipass": "^3.0.0"}}, "minizlib": {"version": "2.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minizlib/-/minizlib-2.1.2.tgz", "integrity": "sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==", "requires": {"minipass": "^3.0.0", "yallist": "^4.0.0"}}, "mkdirp": {"version": "0.5.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "requires": {"minimist": "^1.2.5"}}, "ms": {"version": "2.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "negotiator": {"version": "0.6.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="}, "nested-error-stacks": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/nested-error-stacks/-/nested-error-stacks-2.0.1.tgz", "integrity": "sha512-Sr<PERSON>rok4CATudVzBS7coSz26QRSmlK9TzzoFbeKfcPBUFPjcQM9Rqvr/DlJkOrwI/0KcgvMub1n1g5Jt9EgRn4A=="}, "node-alias": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/node-alias/-/node-alias-1.0.4.tgz", "integrity": "sha512-9uG48bfkbG9BlKe8QrlxuiPNaKl3wpQn6tJbrojVqgkJuWIO28ifRKrRDrrK+ee72rJ25EaE//PhSIo8E29lLw==", "requires": {"chalk": "^1.1.1", "lodash": "^4.2.0"}, "dependencies": {"ansi-styles": {"version": "2.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA=="}, "chalk": {"version": "1.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/chalk/-/chalk-1.1.3.tgz", "integrity": "sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==", "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "supports-color": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g=="}}}, "node-gyp": {"version": "7.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/node-gyp/-/node-gyp-7.1.2.tgz", "integrity": "sha512-CbpcIo7C3eMu3dL1c3d0xw449fHIGALIJsRP4DDPHpyiW8vcriNY7ubh9TE4zEKfSxscY7PjeFnshE7h75ynjQ==", "requires": {"env-paths": "^2.2.0", "glob": "^7.1.4", "graceful-fs": "^4.2.3", "nopt": "^5.0.0", "npmlog": "^4.1.2", "request": "^2.88.2", "rimraf": "^3.0.2", "semver": "^7.3.2", "tar": "^6.0.2", "which": "^2.0.2"}, "dependencies": {"which": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "requires": {"isexe": "^2.0.0"}}}}, "nopt": {"version": "5.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/nopt/-/nopt-5.0.0.tgz", "integrity": "sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==", "requires": {"abbrev": "1"}}, "normalize-url": {"version": "4.5.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/normalize-url/-/normalize-url-4.5.1.tgz", "integrity": "sha512-9UZCFRHQdNrfTpGg8+1INIg93B6zE0aXMVFkw1WFwvO4SlZywU6aLg5Of0Ap/PgcbSw4LNxvMWXMeugwMCX0AA=="}, "npm-bundled": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-bundled/-/npm-bundled-1.1.2.tgz", "integrity": "sha512-x5DHup0SuyQcmL3s7Rx/YQ8sbw/Hzg0rj48eN0dV7hf5cmQq5PXIeioroH3raV1QC1yh3uTYuMThvEQF3iKgGQ==", "requires": {"npm-normalize-package-bin": "^1.0.1"}}, "npm-check-updates": {"version": "4.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-check-updates/-/npm-check-updates-4.1.2.tgz", "integrity": "sha512-CRO20Z12fygKL/ow4j4pnpyxevda/PuFbWpsF5E9sFW0B+M3d32A1dD+fTHLDjgderhKXr64W8qQ6M/Gq8OLiw==", "requires": {"chalk": "^3.0.0", "cint": "^8.2.1", "cli-table": "^0.3.1", "commander": "^5.0.0", "fast-diff": "^1.2.0", "find-up": "4.1.0", "get-stdin": "^7.0.0", "json-parse-helpfulerror": "^1.0.3", "libnpmconfig": "^1.2.1", "lodash": "^4.17.15", "node-alias": "^1.0.4", "p-map": "^4.0.0", "pacote": "^11.1.4", "progress": "^2.0.3", "prompts": "^2.3.2", "rc-config-loader": "^3.0.0", "requireg": "^0.2.2", "semver": "^7.2.1", "semver-utils": "^1.1.4", "spawn-please": "^0.3.0", "update-notifier": "^4.1.0"}}, "npm-install-checks": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-install-checks/-/npm-install-checks-4.0.0.tgz", "integrity": "sha512-09OmyDkNLYwqKPOnbI8exiOZU2GVVmQp7tgez2BPi5OZC8M82elDAps7sxC4l//uSUtotWqoEIDwjRvWH4qz8w==", "requires": {"semver": "^7.1.1"}}, "npm-normalize-package-bin": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.1.tgz", "integrity": "sha512-EPfafl6JL5/rU+ot6P3gRSCpPDW5VmIzX959Ob1+ySFUuuYHWHekXpwdUZcKP5C+DS4GEtdJluwBjnsNDl+fSA=="}, "npm-package-arg": {"version": "8.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-package-arg/-/npm-package-arg-8.1.5.tgz", "integrity": "sha512-LhgZrg0n0VgvzVdSm1oiZworPbTxYHUJCgtsJW8mGvlDpxTM1vSJc3m5QZeUkhAHIzbz3VCHd/R4osi1L1Tg/Q==", "requires": {"hosted-git-info": "^4.0.1", "semver": "^7.3.4", "validate-npm-package-name": "^3.0.0"}}, "npm-packlist": {"version": "2.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-packlist/-/npm-packlist-2.2.2.tgz", "integrity": "sha512-Jt01acDvJRhJGthnUJVF/w6gumWOZxO7IkpY/lsX9//zqQgnF7OJaxgQXcerd4uQOLu7W5bkb4mChL9mdfm+Zg==", "requires": {"glob": "^7.1.6", "ignore-walk": "^3.0.3", "npm-bundled": "^1.1.1", "npm-normalize-package-bin": "^1.0.1"}}, "npm-pick-manifest": {"version": "6.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-pick-manifest/-/npm-pick-manifest-6.1.1.tgz", "integrity": "sha512-dBsdBtORT84S8V8UTad1WlUyKIY9iMsAmqxHbLdeEeBNMLQDlDWWra3wYUx9EBEIiG/YwAy0XyNHDd2goAsfuA==", "requires": {"npm-install-checks": "^4.0.0", "npm-normalize-package-bin": "^1.0.1", "npm-package-arg": "^8.1.2", "semver": "^7.3.4"}}, "npm-registry-fetch": {"version": "11.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npm-registry-fetch/-/npm-registry-fetch-11.0.0.tgz", "integrity": "sha512-jmlgSxoDNuhAtxUIG6pVwwtz840i994dL14FoNVZisrmZW5kWd63IUTNv1m/hyRSGSqWjCUp/YZlS1BJyNp9XA==", "requires": {"make-fetch-happen": "^9.0.1", "minipass": "^3.1.3", "minipass-fetch": "^1.3.0", "minipass-json-stream": "^1.0.1", "minizlib": "^2.0.0", "npm-package-arg": "^8.0.0"}}, "npmlog": {"version": "4.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/npmlog/-/npmlog-4.1.2.tgz", "integrity": "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==", "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "number-is-nan": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/number-is-nan/-/number-is-nan-1.0.1.tgz", "integrity": "sha512-4jbtZXNAsfZbAHiiqjLPBiCl16dES1zI4Hpzzxw61Tk+loF+sBDBKx1ICKKKwIqQ7M0mFn1TmkN7euSncWgHiQ=="}, "oauth-sign": {"version": "0.9.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="}, "object-assign": {"version": "4.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object.defaults": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.defaults/-/object.defaults-1.1.0.tgz", "integrity": "sha512-c/K0mw/F11k4dEUBMW8naXUuBuhxRCfG7W+yFy8EcijU/rSmazOUd1XAEEe6bC0OuXY4HUKjTJv7xbxIMqdxrA==", "requires": {"array-each": "^1.0.1", "array-slice": "^1.0.0", "for-own": "^1.0.0", "isobject": "^3.0.0"}}, "object.map": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.map/-/object.map-1.0.1.tgz", "integrity": "sha512-3+mAJu2PLfnSVGHwIWubpOFLscJANBKuB/6A4CxBstc4aqwQY0FWcsppuy4jU5GSB95yES5JHSI+33AWuS4k6w==", "requires": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}}, "object.pick": {"version": "1.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==", "requires": {"isobject": "^3.0.1"}}, "on-finished": {"version": "2.3.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "requires": {"ee-first": "1.1.1"}}, "once": {"version": "1.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "requires": {"wrappy": "1"}}, "opener": {"version": "1.5.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/opener/-/opener-1.5.1.tgz", "integrity": "sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0="}, "p-cancelable": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-cancelable/-/p-cancelable-1.1.0.tgz", "integrity": "sha512-s73XxOZ4zpt1edZYZzvhqFa6uvQc1vwUa0K0BdtIZgQMAJj9IbebH+JkgKZc9h+B05PKHLOTl4ajG1BmNrVZlw=="}, "p-limit": {"version": "2.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "requires": {"p-limit": "^2.2.0"}}, "p-map": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-map/-/p-map-4.0.0.tgz", "integrity": "sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==", "requires": {"aggregate-error": "^3.0.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="}, "package-json": {"version": "6.5.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/package-json/-/package-json-6.5.0.tgz", "integrity": "sha512-k3bdm2n25tkyxcjSKzB5x8kfVxlMdgsbPr0GkZcwHsLpba6cBjqCt1KlcChKEvxHIcTB1FVMuwoijZ26xex5MQ==", "requires": {"got": "^9.6.0", "registry-auth-token": "^4.0.0", "registry-url": "^5.0.0", "semver": "^6.2.0"}, "dependencies": {"semver": {"version": "6.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="}}}, "pacote": {"version": "11.3.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/pacote/-/pacote-11.3.5.tgz", "integrity": "sha512-fT375Yczn4zi+6Hkk2TBe1x1sP8FgFsEIZ2/iWaXY2r/NkhDJfxbcn5paz1+RTFCyNf+dPnaoBDJoAxXSU8Bkg==", "requires": {"@npmcli/git": "^2.1.0", "@npmcli/installed-package-contents": "^1.0.6", "@npmcli/promise-spawn": "^1.2.0", "@npmcli/run-script": "^1.8.2", "cacache": "^15.0.5", "chownr": "^2.0.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass": "^3.1.3", "mkdirp": "^1.0.3", "npm-package-arg": "^8.0.1", "npm-packlist": "^2.1.4", "npm-pick-manifest": "^6.0.0", "npm-registry-fetch": "^11.0.0", "promise-retry": "^2.0.1", "read-package-json-fast": "^2.0.1", "rimraf": "^3.0.2", "ssri": "^8.0.1", "tar": "^6.1.0"}, "dependencies": {"mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw=="}}}, "parse-filepath": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/parse-filepath/-/parse-filepath-1.0.2.tgz", "integrity": "sha512-FwdRXKCohSVeXqwtYonZTXtbGJKrn+HNyWDYVcp5yuJlesTwNH4rsmRZ+GrKAPJ5bLpRxESMeS+Rl0VCHRvB2Q==", "requires": {"is-absolute": "^1.0.0", "map-cache": "^0.2.0", "path-root": "^0.1.1"}}, "parse-passwd": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/parse-passwd/-/parse-passwd-1.0.0.tgz", "integrity": "sha512-1Y1A//QUXEZK7YKz+rD9WydcE1+EuPr6ZBgKecAB8tmoW6UFv0NREVJe1p+jRxtThkcbbKkfwIbWJe/IeE6m2Q=="}, "parseurl": {"version": "1.3.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "path-exists": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="}, "path-parse": {"version": "1.0.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="}, "path-root": {"version": "0.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-root/-/path-root-0.1.1.tgz", "integrity": "sha512-QLcPegTHF11axjfojBIoDygmS2E3Lf+8+jI6wOVmNVenrKSo3mFdSGiIgdSHenczw3wPtlVMQaFVwGmM7BJdtg==", "requires": {"path-root-regex": "^0.1.0"}}, "path-root-regex": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-root-regex/-/path-root-regex-0.1.2.tgz", "integrity": "sha512-4GlJ6rZDhQZFE0DPVKh0e9jmZ5egZfxTkp7bcRDuPlJXbAwhxcl2dINPUAsjLdejqaLsCeg8axcLjIbvBjN4pQ=="}, "path-to-regexp": {"version": "0.1.7", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "performance-now": {"version": "2.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="}, "picomatch": {"version": "2.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="}, "portfinder": {"version": "1.0.26", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/portfinder/-/portfinder-1.0.26.tgz", "integrity": "sha1-R1ZY1WyjC+1yrH8TeO01C9G2TnA=", "requires": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.1"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "prepend-http": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/prepend-http/-/prepend-http-2.0.0.tgz", "integrity": "sha512-ravE6m9Atw9Z/jjttRUZ+clIXogdghyZAuWJ3qEzjT+jI/dL1ifAqhZeC5VHzQp1MSt1+jxKkFNemj/iO7tVUA=="}, "process-nextick-args": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="}, "progress": {"version": "2.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/progress/-/progress-2.0.3.tgz", "integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA=="}, "promise-inflight": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g=="}, "promise-retry": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/promise-retry/-/promise-retry-2.0.1.tgz", "integrity": "sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==", "requires": {"err-code": "^2.0.2", "retry": "^0.12.0"}}, "prompts": {"version": "2.4.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/prompts/-/prompts-2.4.2.tgz", "integrity": "sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==", "requires": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}}, "proxy-addr": {"version": "2.0.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/proxy-addr/-/proxy-addr-2.0.6.tgz", "integrity": "sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8=", "requires": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.1"}}, "psl": {"version": "1.15.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/psl/-/psl-1.15.0.tgz", "integrity": "sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==", "requires": {"punycode": "^2.3.1"}}, "pump": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/pump/-/pump-3.0.3.tgz", "integrity": "sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "punycode": {"version": "2.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="}, "pupa": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/pupa/-/pupa-2.1.1.tgz", "integrity": "sha512-l1jNAspIBSFqbT+y+5FosojNpVpF94nlI+wDUpqP9enwOTfHx9f0gh5nB96vl+6yTpsJsypeNrwfzPrKuHB41A==", "requires": {"escape-goat": "^2.0.0"}}, "qs": {"version": "6.7.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/qs/-/qs-6.7.0.tgz", "integrity": "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="}, "range-parser": {"version": "1.2.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="}, "raw-body": {"version": "2.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/raw-body/-/raw-body-2.4.0.tgz", "integrity": "sha1-oc5vucm8NWylLoklarWQWeE9AzI=", "requires": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}}, "rc": {"version": "1.2.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rc/-/rc-1.2.8.tgz", "integrity": "sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==", "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}}, "rc-config-loader": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rc-config-loader/-/rc-config-loader-3.0.0.tgz", "integrity": "sha512-bwfUSB37TWkHfP+PPjb/x8BUjChFmmBK44JMfVnU7paisWqZl/o5k7ttCH+EQLnrbn2Aq8Fo1LAsyUiz+WF4CQ==", "requires": {"debug": "^4.1.1", "js-yaml": "^3.12.0", "json5": "^2.1.1", "require-from-string": "^2.0.2"}, "dependencies": {"debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "requires": {"ms": "^2.1.3"}}, "ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}}}, "read-package-json-fast": {"version": "2.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/read-package-json-fast/-/read-package-json-fast-2.0.3.tgz", "integrity": "sha512-W/BKtbL+dUjTuRL2vziuYhp76s5HZ9qQhd/dKfWIZveD0O40453QNyZhC0e63lqZrAQ4jiOapVoeJ7JrszenQQ==", "requires": {"json-parse-even-better-errors": "^2.3.0", "npm-normalize-package-bin": "^1.0.1"}}, "readable-stream": {"version": "2.3.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "rechoir": {"version": "0.7.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rechoir/-/rechoir-0.7.1.tgz", "integrity": "sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==", "requires": {"resolve": "^1.9.0"}}, "registry-auth-token": {"version": "4.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/registry-auth-token/-/registry-auth-token-4.2.2.tgz", "integrity": "sha512-PC5ZysNb42zpFME6D/XlIgtNGdTl8bBOCw90xQLVMpzuuubJKYDWFAEuUNc+Cn8Z8724tg2SDhDRrkVEsqfDMg==", "requires": {"rc": "1.2.8"}}, "registry-url": {"version": "5.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/registry-url/-/registry-url-5.1.0.tgz", "integrity": "sha512-8acYXXTI0AkQv6RAOjE3vOaIXZkT9wo4LOFbBKYQEEnnMNBpKqdUrI6S4NT0KPIo/WVvJ5tE/X5LF/TQUf0ekw==", "requires": {"rc": "^1.2.8"}}, "request": {"version": "2.88.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/request/-/request-2.88.2.tgz", "integrity": "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==", "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "dependencies": {"qs": {"version": "6.5.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/qs/-/qs-6.5.3.tgz", "integrity": "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA=="}}}, "require-from-string": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/require-from-string/-/require-from-string-2.0.2.tgz", "integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="}, "requireg": {"version": "0.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/requireg/-/requireg-0.2.2.tgz", "integrity": "sha512-nYzyjnFcPNGR3lx9lwPPPnuQxv6JWEZd2Ci0u9opN7N5zUEPIhY/GbL3vMGOr2UXwEg9WwSyV9X9Y/kLFgPsOg==", "requires": {"nested-error-stacks": "~2.0.1", "rc": "~1.2.7", "resolve": "~1.7.1"}, "dependencies": {"resolve": {"version": "1.7.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve/-/resolve-1.7.1.tgz", "integrity": "sha512-c7rwLofp8g1U+h1KNyHL/jicrKg1Ek4q+Lr33AL65uZTinUZHe30D5HlyN5V9NW0JX1D5dXQ4jqW5l7Sy/kGfw==", "requires": {"path-parse": "^1.0.5"}}}}, "requires-port": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="}, "resolve": {"version": "1.22.10", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve/-/resolve-1.22.10.tgz", "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "requires": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-dir": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve-dir/-/resolve-dir-1.0.1.tgz", "integrity": "sha512-R7uiTjECzvOsWSfdM0QKFNBVFcK27aHOUwdvK53BcW8zqnGdYp0Fbj82cy54+2A4P2tFM22J5kRfe1R+lM/1yg==", "requires": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}}, "responselike": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/responselike/-/responselike-1.0.2.tgz", "integrity": "sha512-/Fpe5guzJk1gPqdJLJR5u7eG/gNY4nImjbRDaVWVMRhne55TCmj2i9Q+54PBRfatRC8v/rIiv9BN0pMd9OV5EQ==", "requires": {"lowercase-keys": "^1.0.0"}}, "retry": {"version": "0.12.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/retry/-/retry-0.12.0.tgz", "integrity": "sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow=="}, "rimraf": {"version": "3.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "requires": {"glob": "^7.1.3"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "safer-buffer": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "secure-compare": {"version": "3.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/secure-compare/-/secure-compare-3.0.1.tgz", "integrity": "sha1-8aAymzCLIh+uN7mXTz1XjQypmeM="}, "semver": {"version": "7.7.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA=="}, "semver-diff": {"version": "3.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver-diff/-/semver-diff-3.1.1.tgz", "integrity": "sha512-GX0Ix/CJcHyB8c4ykpHGIAvLyOwOobtM/8d+TQkAd81/bEjgPHrfba41Vpesr7jX/t8Uh+R3EX9eAS5be+jQYg==", "requires": {"semver": "^6.3.0"}, "dependencies": {"semver": {"version": "6.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="}}}, "semver-utils": {"version": "1.1.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/semver-utils/-/semver-utils-1.1.4.tgz", "integrity": "sha512-EjnoLE5OGmDAVV/8YDoN5KiajNadjzIp9BAHOhYeQHt7j0UWxjmgsx4YD48wp4Ue1Qogq38F1GNUJNqF1kKKxA=="}, "send": {"version": "0.17.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/send/-/send-0.17.1.tgz", "integrity": "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=", "requires": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "dependencies": {"ms": {"version": "2.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="}}}, "serve-static": {"version": "1.14.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/serve-static/-/serve-static-1.14.1.tgz", "integrity": "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=", "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}}, "set-blocking": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw=="}, "setprototypeof": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/setprototypeof/-/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="}, "signal-exit": {"version": "3.0.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="}, "sisteransi": {"version": "1.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/sisteransi/-/sisteransi-1.0.5.tgz", "integrity": "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg=="}, "smart-buffer": {"version": "4.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/smart-buffer/-/smart-buffer-4.2.0.tgz", "integrity": "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg=="}, "socks": {"version": "2.8.6", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/socks/-/socks-2.8.6.tgz", "integrity": "sha512-pe4Y2yzru68lXCb38aAqRf5gvN8YdjP1lok5o0J7BOHljkyCGKVz7H3vpVIXKD27rj2giOJ7DwVyk/GWrPHDWA==", "requires": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}}, "socks-proxy-agent": {"version": "6.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/socks-proxy-agent/-/socks-proxy-agent-6.2.1.tgz", "integrity": "sha512-a6KW9G+6B3nWZ1yB8G7pJwL3ggLy1uTzKAgCb7ttblwqdz9fMGJUuTy3uFzEP48FAs9FLILlmzDlE2JJhVQaXQ==", "requires": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}, "dependencies": {"debug": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "requires": {"ms": "^2.1.3"}}, "ms": {"version": "2.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}}}, "spawn-please": {"version": "0.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/spawn-please/-/spawn-please-0.3.0.tgz", "integrity": "sha512-gf9GJwAWhW0gnQp0dGui+nhIVICx1lGM1Ox95HzfaDBOQTauqlvHFLpo4vtAB3E377SA0YMIyRCh1w0S6R5m2w=="}, "sprintf-js": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g=="}, "sshpk": {"version": "1.18.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/sshpk/-/sshpk-1.18.0.tgz", "integrity": "sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==", "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "dependencies": {"jsbn": {"version": "0.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="}}}, "ssri": {"version": "8.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ssri/-/ssri-8.0.1.tgz", "integrity": "sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==", "requires": {"minipass": "^3.1.1"}}, "statuses": {"version": "1.5.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}, "string_decoder": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "requires": {"safe-buffer": "~5.1.0"}}, "string-width": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string-width/-/string-width-1.0.2.tgz", "integrity": "sha512-0XsVpQLnVCXHJfyEs8tC0zpTVIr5PKKsQtkT29IwupnPTjtPmQ3xT/4yCREF9hYkV/3M3kzcUTSAZT6a6h81tw==", "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==", "requires": {"ansi-regex": "^2.0.0"}}, "strip-json-comments": {"version": "2.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ=="}, "supports-color": {"version": "7.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "requires": {"has-flag": "^4.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="}, "tar": {"version": "6.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/tar/-/tar-6.2.1.tgz", "integrity": "sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==", "requires": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "dependencies": {"minipass": {"version": "5.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/minipass/-/minipass-5.0.0.tgz", "integrity": "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ=="}, "mkdirp": {"version": "1.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw=="}}}, "term-size": {"version": "2.2.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/term-size/-/term-size-2.2.1.tgz", "integrity": "sha512-wK0Ri4fOGjv/XPy8SBHZChl8CM7uMc5VML7SqiQ0zG7+J5Vr+RMQDoHa2CNT6KHUnTGIXH34UDMkPzAUyapBZg=="}, "to-readable-stream": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/to-readable-stream/-/to-readable-stream-1.0.0.tgz", "integrity": "sha512-Iq25XBt6zD5npPhlLVXGFN3/gyR2/qODcKNNyTMd4vbm39HUaOiAM4PMq0eMVC/Tkxz+Zjdsc55g9yyz+Yq00Q=="}, "to-regex-range": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "requires": {"is-number": "^7.0.0"}}, "toidentifier": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/toidentifier/-/toidentifier-1.0.0.tgz", "integrity": "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="}, "tough-cookie": {"version": "2.5.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/tough-cookie/-/tough-cookie-2.5.0.tgz", "integrity": "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==", "requires": {"psl": "^1.1.28", "punycode": "^2.1.1"}}, "tunnel-agent": {"version": "0.6.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==", "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA=="}, "type-fest": {"version": "0.8.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/type-fest/-/type-fest-0.8.1.tgz", "integrity": "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA=="}, "type-is": {"version": "1.6.18", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "typedarray-to-buffer": {"version": "3.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz", "integrity": "sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==", "requires": {"is-typedarray": "^1.0.0"}}, "unc-path-regex": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unc-path-regex/-/unc-path-regex-0.1.2.tgz", "integrity": "sha512-eXL4nmJT7oCpkZsHZUOJo8hcX3GbsiDOa0Qu9F646fi8dT3XuSVopVqAcEiVzSKKH7UoDti23wNX3qGFxcW5Qg=="}, "union": {"version": "0.5.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/union/-/union-0.5.0.tgz", "integrity": "sha1-ssEb6E9gU4U3uEbtuboma6AJAHU=", "requires": {"qs": "^6.4.0"}}, "unique-filename": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unique-filename/-/unique-filename-1.1.1.tgz", "integrity": "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==", "requires": {"unique-slug": "^2.0.0"}}, "unique-slug": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unique-slug/-/unique-slug-2.0.2.tgz", "integrity": "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==", "requires": {"imurmurhash": "^0.1.4"}}, "unique-string": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unique-string/-/unique-string-2.0.0.tgz", "integrity": "sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg==", "requires": {"crypto-random-string": "^2.0.0"}}, "unpipe": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "update-notifier": {"version": "4.1.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/update-notifier/-/update-notifier-4.1.3.tgz", "integrity": "sha512-Yld6Z0RyCYGB6ckIjffGOSOmHXj1gMeE7aROz4MG+XMkmixBX4jUngrGXNYz7wPKBmtoD4MnBa2Anu7RSKht/A==", "requires": {"boxen": "^4.2.0", "chalk": "^3.0.0", "configstore": "^5.0.1", "has-yarn": "^2.1.0", "import-lazy": "^2.1.0", "is-ci": "^2.0.0", "is-installed-globally": "^0.3.1", "is-npm": "^4.0.0", "is-yarn-global": "^0.3.0", "latest-version": "^5.0.0", "pupa": "^2.0.1", "semver-diff": "^3.1.1", "xdg-basedir": "^4.0.0"}}, "uri-js": {"version": "4.4.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "requires": {"punycode": "^2.1.0"}}, "url-join": {"version": "2.0.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/url-join/-/url-join-2.0.5.tgz", "integrity": "sha1-WvIvGMBSoACkjXuCxenC4v7tpyg="}, "url-parse-lax": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/url-parse-lax/-/url-parse-lax-3.0.0.tgz", "integrity": "sha512-NjFKA0DidqPa5ciFcSrXnAltTtzz84ogy+NebPvfEgAck0+TNg4UJ4IN+fB7zRZfbgUf0syOo9MDxFkDSMuFaQ==", "requires": {"prepend-http": "^2.0.0"}}, "util-deprecate": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="}, "utils-merge": {"version": "1.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="}, "uuid": {"version": "3.4.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/uuid/-/uuid-3.4.0.tgz", "integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A=="}, "v8flags": {"version": "4.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/v8flags/-/v8flags-4.0.1.tgz", "integrity": "sha512-fcRLaS4H/hrZk9hYwbdRM35D0U8IYMfEClhXxCivOojl+yTRAZH3Zy2sSy6qVCiGbV9YAtPssP6jaChqC9vPCg=="}, "validate-npm-package-name": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/validate-npm-package-name/-/validate-npm-package-name-3.0.0.tgz", "integrity": "sha512-M6w37eVCMMouJ9V/sdPGnC5H4uDr73/+xdq0FBLO3TFFX1+7wiUY6Es328NN+y43tmY+doUdN9g9J21vqB7iLw==", "requires": {"builtins": "^1.0.3"}}, "vary": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "verror": {"version": "1.10.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/verror/-/verror-1.10.0.tgz", "integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "dependencies": {"core-util-is": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ=="}}}, "which": {"version": "1.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "requires": {"isexe": "^2.0.0"}}, "wide-align": {"version": "1.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/wide-align/-/wide-align-1.1.5.tgz", "integrity": "sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==", "requires": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "widest-line": {"version": "3.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/widest-line/-/widest-line-3.1.0.tgz", "integrity": "sha512-NsmoXalsWVDMGupxZ5R08ka9flZjjiLvHVAWYOKtiKM8ujtZWr9cRffak+uSE48+Ob8ObalXpwyeUiyDD6QFgg==", "requires": {"string-width": "^4.0.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="}, "string-width": {"version": "4.2.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "requires": {"ansi-regex": "^5.0.1"}}}}, "wrappy": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="}, "write-file-atomic": {"version": "3.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/write-file-atomic/-/write-file-atomic-3.0.3.tgz", "integrity": "sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==", "requires": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "xdg-basedir": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/xdg-basedir/-/xdg-basedir-4.0.0.tgz", "integrity": "sha512-PSNhEJDejZYV7h50BohL09Er9VaIefr2LMAf3OEmpCkjOi34eYyQYAXUTjEQtZJTKcF0E2UKTh+osDLsgNim9Q=="}, "yallist": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}}}