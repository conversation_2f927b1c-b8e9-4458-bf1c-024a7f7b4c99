const express = require('express')
const proxy = require('express-http-proxy')
const cors = require('cors')
const path = require('path')
const { config } = require('../package.json')
const port = process.env.PORT || config.server_port_dev_server || 9000

const app = express()

const hosts = {
  portal: 'localhost:9041',
  opercat: 'localhost:9042',
  referencedata: 'localhost:9043',
  monitoring: 'localhost:9044',
  mpt: 'localhost:9045',
  go: 'localhost:9046',
  nadm: 'localhost:9047',
  tsc: 'naportal-ci.c.ptin.corppt.com:29001',
  diagnostics: 'localhost:9048'

}

const staticPathRoutes = {
  '/fuxi/': 'node_modules/fuxi/app',
  '/fuxi/fuxi-nossis/': 'node_modules/fuxi-nossis/app',
  '/basepack/': 'node_modules/basepack/lib',
  '/webpack-dev/': '../build/webpack',
  '/na-ext/images/top-bar-logo-brand.svg': './top-bar-logo-brand.svg'
}

const modulesProxyRoutes = {
  '/na/operationscatalog/': hosts.opercat,
  '/na/referencedata/': hosts.referencedata,
  '/na/monitoring/': hosts.monitoring,
  '/na/mpt/': hosts.mpt,
  '/na/go/': hosts.go,
  '/na/veacs/': hosts.nadm,
  '/na/nadm/': hosts.nadm,
  '/na/entities-catalog/': hosts.tsc,
  '/na/diagnostics/': hosts.diagnostics
}

app.use(cors())
Object.entries(staticPathRoutes).forEach(([route, folderPath]) => {
  console.log([route, path.join(__dirname, folderPath)])
  app.use(route, express.static(path.join(__dirname, folderPath)))
})

Object.entries(modulesProxyRoutes).forEach(([route, host]) => {
  app.use(route, proxy(host, {
    limit: '400mb',
    proxyReqPathResolver: req => {
      const url = new URL(req.originalUrl, 'http://localhost')
      return url.pathname + url.search
    },
    skipToNextHandlerFilter: (incomingMessage) => {
      console.log(incomingMessage.req.path)
      const skip = Object.keys(staticPathRoutes).some(route => incomingMessage.req.path.startsWith(route))
      console.log(`skip: ${skip}`)
      return skip
    }
  }))
})

app.use('/', function checkUser(req, res, next) {
  if (req.path === '/') {
    return res.redirect('/na/login')
  }
  next()
})

app.use('/', proxy(hosts.portal, {
  limit: '400mb',
  filter: function (req) {
    return req.path !== '/' &&
      !Object.keys(staticPathRoutes).some(route => req.path.startsWith(route)) &&
      !Object.keys(modulesProxyRoutes).some(route => req.path.startsWith(route))
  }
}))

app.listen(port, () => {
  console.log(`Development server is running on Port ${port}`)
})
