= NA Portal Frontend Web
:source-highlighter: rouge
:imagesdir: src/docs/assets
:data-uri:
:toc:

[subs="quotes, attributes+"]
NOTE: This documentation applies for revision *{svn_revision}* of branch *{svn_branch}*

== Dependencies
* Requires installation
** NodeJS 22 'Jod' ( the latest LTS version )
** Maven 3.6.3
** Subversion
* Other
** npm 9 ( should be included in NodeJS )
** Gradle 6.0 ( should be installed by Gradle Wrapper )


== Getting started

First, it is needed to install the whole project using maven on
root project to set up and install all required packages

IMPORTANT: Make sure you have all necessary certificates
imported on JDK before running it. Also, in case of remote
work, you have to be connected to the VPN

.First script
[source, bash, subs="verbatim,quotes, attributes+"]
----
svn co *{svn_url}*
cd {svn_branch}
mvn clean install
----

== Usage in dev mode

To work in dev mode you have to run the following scripts
in separate terminal

.Web script
[source, bash, subs="verbatim,quotes, attributes+"]
----
# Current working directory is project root: {svn_branch}
cd src/frontend/web
npm run dev
----

.Play Scripts
[source, bash, subs="verbatim,quotes, attributes+"]
----
# Current working directory is project root: {svn_branch}
cd src/frontend/play
./gradlew run
----

== Ports used in development

.Ports table
[%autowidth]
|===
|Ports |Project|Description

3+^| *Development ports ( 904x )*
| *9040* | Web  | Development web balancer
| *9041* | Play | Play Portal server
| *9042* | Play | Play Operations Catalog module server
| *9043* | Play | Play Reference Data module server
| *9044* | Play | Play Monitoring module server
| *9045* | Play | Play MPT module server
| *9046* | Play | Play GO module server
| *9047* | Play | Play NADM module server
| *9048* | Play | Play Diagnostics module server
3+^| *Debug ports ( 913x )*
| *9140* |      | Reserved for remote debug of tests (play and selenium)
| *9141* | Play | Remote debug Play Portal server
| *9142* | Play | Remote debug Play Operations Catalog module server
| *9143* | Play | Remote debug Play Reference Data
| *9144* | Play | Remote debug Play Monitoring
| *9145* | Play | Remote debug Play MPT
| *9146* | Play | Remote debug Play GO
| *9147* | Play | Remote debug Play NADM
| *9148* | Play | Remote debug Play Diagnostics
3+^| *Miscelaneous ports ( 923x )*
| *9240* | Web  | Web demos and documentation server
| *9241* | Web  | Webpack assets server
| *9242* | Web  | UI Tests playground server
| *9243* |      | reserved for Play Swagger documentation
| *9244* | Web  | NOWC documentation server
|===
