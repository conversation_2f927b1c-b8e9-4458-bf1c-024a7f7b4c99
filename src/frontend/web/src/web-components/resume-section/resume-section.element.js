import html from './resume-section.element.html'
import i18n from '~utils/i18n'
import { isProvided, onProvide } from '~utils/i18n/provider'
import { goToModule } from '~utils/module-navigation'
import './resume-subsection.element'

import bootstrapStylesheet from '~base-styles/bootstrap.stylesheet'
import fuxiStylesheet from '~base-styles/fuxi.stylesheet'
import { CssStyleSheetLoader } from '~utils/stylesheet-utils.util'

const cssLoad = CssStyleSheetLoader(() => import(/* webpackChunkName: "web-components/resume-style" */ './resume-section.element.inline.css'))

const template = document.createElement('template')
template.innerHTML = html

const providerObserver = new WeakMap()

const observeI18nProvideOnElement = (element) => {
  unobserveI18nProvideOnElement(element)
  providerObserver.set(element, onProvide(() => element.updateContent()))
}

const unobserveI18nProvideOnElement = (element) => {
  if (providerObserver.has(element)) {
    providerObserver.get(element).unregister()
    providerObserver.delete(element)
  }
}

export class ResumeSection extends HTMLElement {
  constructor () {
    super()
    this.attachShadow({ mode: 'open' })
    const { shadowRoot } = this
    const clone = document.importNode(template.content, true)
    cssLoad.then(style => {
      shadowRoot.adoptedStyleSheets = [bootstrapStylesheet, fuxiStylesheet, style]
      const { module } = this
      if (module) {
        updateContent(clone, module)
      }
      shadowRoot.append(clone)
    })
    shadowRoot.addEventListener('click', (ev) => {
      const buttonTarget = ev.target.closest('.button--access-module')
      if (buttonTarget != null) {
        const moduleId = this.getAttribute('button-action-go-to-module')
        goToModule({ moduleId })
      }
    })
  }

  connectedCallback () {
    observeI18nProvideOnElement(this)
  }

  disconnectedCallback () {
    unobserveI18nProvideOnElement(this)
  }

  get module() {
    return this.getAttribute('data-module')
  }

  updateContent () {
    this.module && updateContent(this.shadowRoot, this.module)
  }
}

function updateContent(root, moduleKey) {
  if (root.children.length <= 0) { return }
  const title = root.querySelector('.resume-section__title')
  const button = root.querySelector('.button--access-module')
  const defaultSubSection = root.querySelector('.resume__default-subsection')

  title.textContent = getI18n(moduleKey, '.information.title')
  button.textContent = getI18n(moduleKey, '.operationalview.accessmodule')
  defaultSubSection.setAttribute('key', moduleKey + '.module.title')
  defaultSubSection.setAttribute('content', moduleKey + '.module.description')
}

function getI18n(moduleKey, suffix) {
  const cacheKey = `resume-i18n-cache:${moduleKey}${suffix}`
  if (isProvided() && i18n(moduleKey)) {
    const parts = moduleKey.split('.')
    let currentKey = moduleKey + suffix
    let result = i18n(currentKey)
    for (let endIndex = parts.length - 1; endIndex > 0 && result === currentKey; endIndex--) {
      currentKey = parts.slice(0, endIndex).join('.') + suffix
      result = i18n(currentKey)
    }
    sessionStorage.setItem(cacheKey, result)
    return result
  } else if (sessionStorage.getItem(cacheKey) != null) {
    return sessionStorage.getItem(cacheKey)
  } else {
    return ''
  }
}

customElements.define('x-resume-section', ResumeSection)
