import html from './resume-subsection.element.html'
import i18n from '~utils/i18n'
import { isProvided, onProvide } from '~utils/i18n/provider'

const template = document.createElement('template')
template.innerHTML = html

const providerObserver = new WeakMap()

const observeI18nProvideOnElement = (element) => {
  unobserveI18nProvideOnElement(element)
  providerObserver.set(element, onProvide(() => element.updateContent()))
}

const unobserveI18nProvideOnElement = (element) => {
  if (providerObserver.has(element)) {
    providerObserver.get(element).unregister()
    providerObserver.delete(element)
  }
}

export class ResumeSubSection extends HTMLElement {
  constructor () {
    super()
    this.attachShadow({ mode: 'open' })
    const { shadowRoot, key, content } = this
    const clone = document.importNode(template.content, true)
    if (key && content) {
      updateContent(clone, key, content)
    }
    shadowRoot.append(clone)
  }

  connectedCallback () {
    observeI18nProvideOnElement(this)
  }

  disconnectedCallback () {
    unobserveI18nProvideOnElement(this)
  }

  static get observedAttributes () { return ['key', 'content'] }

  attributeChangedCallback (name, oldValue, newValue) {
    if (!isProvided()) {
      return
    }
    switch (name) {
      case 'key':
        this.shadowRoot.querySelector('.section-title').textContent = getI18n(newValue)
        return
      case 'content': {
        /**
         * This part is specific to Play Framework. Since Play i18n has no standard newline as it uses
         * java.text.MessageFormat to format the i18n, the newline is platform specific. To still prevent DOM based
         * script attacks while having line break, we maintained the <br> as newline char and escaped the rest
         */
        const sectionContent = this.shadowRoot.querySelector('.section-content')
        sectionContent.textContent = getI18n(newValue)
        sectionContent.innerHTML = sectionContent.textContent.replaceAll('&lt;br&gt;', '<br>')
      }
    }
  }

  get key() {
    return this.getAttribute('module')
  }

  get content() {
    return this.getAttribute('module')
  }

  updateContent () {
    const { key, content } = this
    key && content && updateContent(this.shadowRoot, key, content)
  }
}

function updateContent(root, key, content) {
  root.querySelector('.section-title').textContent = getI18n(key)
  root.querySelector('.section-content').textContent = getI18n(content)
}

function getI18n(i18nKey) {
  const cacheKey = `resume-i18n-cache:${i18nKey}`
  if (isProvided() && i18n(i18nKey)) {
    const result = i18n(i18nKey)
    sessionStorage.setItem(cacheKey, result)
    return result
  } else if (sessionStorage.getItem(cacheKey) != null) {
    return sessionStorage.getItem(cacheKey)
  } else {
    return ''
  }
}

customElements.define('x-resume-subsection', ResumeSubSection)
