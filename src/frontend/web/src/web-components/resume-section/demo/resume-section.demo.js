import { provide as provideI18n } from '~utils/i18n/provider'
import { provide as provideModuleNavigation } from '~utils/module-navigation/provider'
import '../resume-section.element'
import './resume-section.demo.css'

document.querySelectorAll('.code--import').forEach(el => { el.textContent = 'import "~components/resume-section/resume-section.element"' })

const i18nMap = {
  'nossis.tsccatalog.information.title': 'Informação',
  'nossis.tsccatalog.module.title': 'Catálogo',
  'nossis.tsccatalog.module.description': 'O módulo de catálogos permite a gestão da informação base do sistema que suporta os processos de ativação tal como informação de acesso a equipamentos, mapeamentos de dados entre sistemas e rede, perfis de equipamentos, etc.',
  'nossis.tsccatalog.operationalview.accessmodule': 'Aceder ao módulo'
}

provideI18n({
  translate (key) {
    return i18nMap[key] || key
  }
})

provideModuleNavigation({
  goToModule: ({ moduleId }) => {
    alert(`will navigate to module ${moduleId}`)
  }
})
