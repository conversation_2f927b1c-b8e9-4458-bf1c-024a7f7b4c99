<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= title %></title>
    <%= css %></head>
<body>
<%= navbar %>
<div class='container app'>
    <h1><%= title %></h1>

    <h3>Dependencies</h3>
    <%= dependencyTable %>

    <h3>Imports</h3>
    <pre><code class="code--import lang-js"></code></pre>

    <h3>Resume section component</h3>

    <div class="demo-container">
        <x-resume-section button-action-go-to-module="entities-catalog" data-module="nossis.tsccatalog"></x-resume-section>
    </div>

    <br>
    <pre><code class="xml">&lt;x-resume-section button-action-go-to-module="entities-catalog" module="nossis.tsccatalog"&gt;&lt;/x-resume-section&gt;</code></pre>

    <h4>Explanation on component attributes:</h4>
    <p>The <code><b class="text-info">button-action-go-to-module</b></code> attribute is self-explanatory.
        It is used to tell the component which module to navigate to when the module access button is clicked.<br>
        The <code><b class="text-info">module</b></code> attribute is used to tell the component what is the basis of the i18n tags (used in this component)
        for the module in question.
    </p>

</div>
<%= js %></body>
</html>