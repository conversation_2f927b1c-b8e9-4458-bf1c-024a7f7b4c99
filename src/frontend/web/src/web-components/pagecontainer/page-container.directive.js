import pageContainerModule from './page-container.angular-module'
import { providers } from '~basemodule/app/basemodule-providers'
import { addLoadPageMiddleware, addNavigationHandler, loadPage } from '../../utils/navigation'

pageContainerModule.directive(providers.pageContainer.directives.pageContainer, ['$compile', pageContainerDirective])

function pageContainerDirective ($compile) {
  return {
    restrict: 'A',
    scope: {},
    compile: function (telem) {
      const initialHtml = telem.html()
      telem.empty()

      return function (scope, element) {
        let targetScope = scope.$new()

        function loadHtml (html) {
          element.html(html)
          element.children().each(function () {
            $compile(this)(targetScope)
          })
        }

        loadHtml(initialHtml)

        const middleware = addLoadPageMiddleware(function (data, next) {
          const parser = new DOMParser()
          const doc = parser.parseFromString(data.html, 'text/html')
          const titleElement = doc.querySelector('head > title')
          const targetElement = doc.querySelector('#' + element[0].id)

          if (titleElement != null) {
            document.title = titleElement.innerHTML
          }

          if (targetElement != null) {
            targetScope.$destroy()
            targetScope = scope.$new()
            loadHtml(targetElement.innerHTML)
            next(data)
          } else if (data.isError === true) {
            targetScope.$destroy()
            loadHtml(doc.body.innerHTML)
            next(data)
          }
        })

        const backNavigationHandler = addNavigationHandler(function (event) {
          if (event && event.state && typeof event.state.html === 'string') {
            loadPage(event.state)
          }
        })

        scope.$on('$destroy', function () {
          middleware.remove()
          backNavigationHandler.remove()
        })
      }
    }
  }
}
