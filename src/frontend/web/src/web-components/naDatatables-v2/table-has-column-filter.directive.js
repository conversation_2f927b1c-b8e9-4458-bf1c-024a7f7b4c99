import datatableModule from './datatable.angular-module'
import { providers } from '~basemodule/app/basemodule-providers'
import template from './table-has-column-filter.directive.html'
import i18n from '~utils/i18n'
import { providerName as tableDatatableProviderName } from './datatable.directive'

export const providerName = providers.datatables.directives.TableHasColumnFilter
datatableModule.directive(providerName, [
  TableHasColumnFilter
])

function TableHasColumnFilter () {
  return {
    restrict: 'A',
    require: '^^' + tableDatatableProviderName,
    terminal: false,
    link: function (scope, element, attrs, ctrl) {
      const $container = $(template)
      const $confirmButton = $container.find('.dropdown-menu__confirm-button')
      const $cancelButton = $container.find('.dropdown-menu__cancel-button')
      const $menuFieldsFilter = $container.find('.dropdown-menu__fields-filter')
      const $menuFields = $container.find('.dropdown-menu__fields')

      $confirmButton.text(i18n('na.button.apply'))
      $cancelButton.text(i18n('na.button.label.cancel'))
      $menuFieldsFilter.attr('placeholder', i18n('na.datatables.columnFilter.search.placeholder'))

      $container.on('click', '.dropdown-menu', function (e) {
        e.stopPropagation()
      })

      ctrl.moveElementToSection(element).then(() => $container.appendTo(element))

      ctrl.getStoreInfo().then(({ table, store }) => {
        const feature = 'column-filter'
        const getState = (store) => store.getters.featuresState[table][feature]
        const featureStateParams = (state) => ({ table, feature, state })
        const updateState = (state) => store.setters.updateFeatureState(featureStateParams(state(getState(store))))
        const stateChanged = (snapshot1, snapshot2) => {
          const featuresState1 = snapshot1.getters.featuresState
          const featuresState2 = snapshot2.getters.featuresState
          return featuresState1[table][feature] !== featuresState2[table][feature]
        }

        $menuFieldsFilter.on('input', resetView)
        $menuFieldsFilter.on('change', () => {
          updateState((oldState) => ({
            ...oldState,
            columnsFilter: $menuFieldsFilter.val()
          }))
        })

        updateState(() => ({
          open: false,
          columnsToToggle: [],
          columnsFilter: ''
        }))

        store.listenStateChange((newSnapshot, oldSnapshot) => {
          if (
            newSnapshot.getters.visibilitySwitchableColumnNames[table] !==
                        oldSnapshot.getters.visibilitySwitchableColumnNames[table]
          ) {
            resetView()
            return
          }
          if (
            stateChanged(newSnapshot, oldSnapshot) ||
                        newSnapshot.getters.columns[table] !== oldSnapshot.getters.columns[table]
          ) {
            updateView(newSnapshot)
          }

          if (getState(newSnapshot).columnsFilter !== getState(oldSnapshot).columnsFilter) {
            $menuFieldsFilter.val(getState(newSnapshot).columnsFilter)
            resetView()
          }
        }).until(({ getters }) => !getters.definedTableSet.has(table))

        $container.on('click', '.dropdown-menu__cancel-button', function (e) {
          updateState((oldState) => {
            return Object.assign({}, oldState, {
              open: false,
              columnsToToggle: [],
              columnsFilter: ''
            })
          })
          e.stopPropagation()
        })

        $container.on('click', '.dropdown-menu__confirm-button', function (e) {
          const { columnsToToggle } = getState(store)
          updateState((oldState) => {
            return Object.assign({}, oldState, {
              open: false,
              columnsToToggle: []
            })
          })
          store.setters.toggleVisibilityOnColumns({
            table,
            columnNames: columnsToToggle
          })
          e.stopPropagation()
        })

        $container.on('click', '.btn--table-column-filter', function (e) {
          updateState((oldState) => {
            return Object.assign({}, oldState, {
              open: !oldState.open
            })
          })
          e.stopPropagation()
        })

        $container.on('change', '.input--table-column-filter-checkbox', function (e) {
          const $target = $(this)
          const columnName = $target.attr('value')
          updateState((oldState) => {
            const newColumnsToToggle = oldState.columnsToToggle.includes(columnName)
              ? oldState.columnsToToggle.filter(colName => columnName !== colName)
              : oldState.columnsToToggle.concat(columnName)
            return Object.assign({}, oldState, {
              columnsToToggle: newColumnsToToggle
            })
          })
          e.stopPropagation()
        })

        $container.on('hide.bs.dropdown', function () {
          if (!getState(store).open) {
            return
          }
          updateState((oldState) => {
            return Object.assign({}, oldState, {
              open: false,
              columnsToToggle: [],
              columnsFilter: ''
            })
          })
        })

        function updateView () {
          const { open } = getState(store)
          $container.toggleClass('open', open)
          updateCheckboxes()
        }

        function resetView () {
          const columns = store.getters.visibilitySwitchableColumns[table]
          const { columnsToToggle } = getState(store)

          $menuFields.empty()

          const fieldNameFilter = $menuFieldsFilter.val()?.trim().toLowerCase() || ''
          columns
            .filter(column => !fieldNameFilter || column.title.toLowerCase().includes(fieldNameFilter))
            .map((column) => {
              const checked = column.visible !== columnsToToggle.includes(column.name)
              const checkedAttr = checked ? 'checked' : ''
              const checkedClass = checked ? 'input--checkbox-checked' : ''
              return `
<div class="searchable checkbox" title="${column.title}">
<label>
    <input class="input--table-column-filter-checkbox ${checkedClass}" type="checkbox" name="fields" value="${column.name}" ${checkedAttr} /> ${column.title}
</label>
</div>
`.trim()
            }).forEach(template => $(template).appendTo($menuFields))
        }

        function updateCheckboxes () {
          const columns = store.getters.visibilitySwitchableColumns[table]
          const { columnsToToggle } = getState(store)
          columns.forEach(column => {
            const checked = column.visible !== columnsToToggle.includes(column.name)
            $menuFields
              .find(`.input--table-column-filter-checkbox[value="${column.name}"]`)
              .prop('checked', checked)
              .toggleClass('input--checkbox-checked', checked)
          }
          )
        }

        resetView()
      })
    }
  }
}
