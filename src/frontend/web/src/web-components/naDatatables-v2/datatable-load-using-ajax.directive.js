/* eslint-disable no-empty */
import datatableModule from './datatable.angular-module'
import { providers } from '~basemodule/app/basemodule-providers'
import { providerName as datatableDirective } from './datatable.directive'
import * as proxy from '~utils/proxy'

datatableModule.directive(providers.datatables.directives.TableLoadUsingAjax, [
  datatableLoadUsingAjaxDirective
])

export const emptyResult = Object.freeze({
  data: Object.freeze([]),
  recordsTotal: 0,
  recordsFiltered: 0
})

/**
 * Normalizes server response data to camel case format, as to make the whole api work
 * with a single model
 * @param response - data in camelcase (v1.10 and above ) or hungarian notation (v 1.9 and below)
 * @return {{data}}
 */
export function normalizeResponse (response) {
  const recordsTotal = typeof response.count === 'number' ? response.count
    : typeof response.recordsTotal === 'number' ? response.recordsTotal
      : response.iTotalRecords

  const recordsFiltered = typeof response.count === 'number' ? response.count
    : typeof response.recordsFiltered === 'number' ? response.recordsFiltered
      : response.iTotalDisplayRecords

  const echo = typeof response.version === 'number' ? response.version
    : typeof response.echo === 'number' ? response.echo
      : response.sEcho

  return {
    data: response.data !== undefined ? response.data : response.aaData,
    recordsTotal,
    recordsFiltered,
    echo
  }
}

export function middlewareCallback (ctrl, tableQuery, callback) {
  const ajaxInfo = ctrl.ajaxInfo
  if (ajaxInfo == null || ajaxInfo.url == null) {
    console.error('ajax info is required to load using ajax')
    return
  }

  const url = ajaxInfo.url
  const method = ajaxInfo.type
  let paramsPost
  if (method === 'POST') {
    if (ctrl.paramsPost == null) {
      paramsPost = {}
    } else if (typeof ctrl.paramsPost === 'object') {
      paramsPost = ctrl.paramsPost
    } else if (typeof ctrl.paramsPost === 'function') {
      paramsPost = ctrl.paramsPost()
    } else {
      paramsPost = {}
    }
  } else {
  }

  const tableQueryWithParameters = Object.assign({}, tableQuery, {
    parameters: paramsPost || {}
  })

  ctrl.getStoreInfo().then(({ store, table }) => {
    store.setters.setTableReloading({ table, reloading: true })

    proxy.request({
      method,
      url,
      data: tableQueryWithParameters,
      onSuccess (serverData) {
        callback(normalizeResponse(serverData))
      },
      onError () {
        callback(emptyResult)
        return true
      }
    })
  })
}

export function datatableLoadUsingAjaxDirective () {
  return {
    restrict: 'A',
    require: datatableDirective,
    terminal: false,
    link: function (scope, element, attrs, ctrl) {
      ctrl.addReloadMiddleware({
        priority: 2000,
        callback (tableQuery, callback) {
          middlewareCallback(ctrl, tableQuery, callback)
        }
      })
    }
  }
}
