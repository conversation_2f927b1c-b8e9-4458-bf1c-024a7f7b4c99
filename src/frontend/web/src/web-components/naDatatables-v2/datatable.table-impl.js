import defaultI18n from '~utils/i18n'
import { loadRenderer } from './datatable-render-store'
import { htmlEscape } from '~utils/string.util'

function tableI18n (i18n) {
  i18n = typeof i18n === 'function' ? i18n : defaultI18n

  const oLanguage = {}

  const oPaginate = {}
  oPaginate.first = i18n('datatables.navigation.label.first')
  oPaginate.previous = i18n('datatables.navigation.label.previous')
  oPaginate.next = i18n('datatables.navigation.label.next')
  oPaginate.last = i18n('datatables.navigation.label.last')
  oPaginate.info = i18n('datatables.pagination.info')
  oLanguage.paginate = oPaginate

  const aria = {}
  aria.sortAscending = 'asc'
  aria.sortDescending = 'desc'
  oLanguage.aria = aria

  oLanguage.emptyTable = i18n('datatables.info.label.emptytable')
  oLanguage.infoFiltered = i18n('datatables.info.label.infofiltered')
  oLanguage.infoPostFix = i18n('datatables.info.label.infopostfix')
  oLanguage.thousands = i18n('datatables.info.label.infothousands')
  oLanguage.lengthMenu = `_MENU_ <span>${i18n('datatables.info.label.lengthmenu')}</span>`
  oLanguage.loadingRecords = i18n('datatables.info.label.loadingrecords')
  oLanguage.processing = i18n('datatables.info.label.processing')
  oLanguage.search = i18n('datatables.info.label.search')
  oLanguage.zeroRecords = i18n('datatables.info.label.zerorecords')

  return oLanguage
}

export function adaptDatatableInfo (datatableInfo) {
  const sorts = {
    asc: 'ascendant',
    desc: 'descendant'
  }
  const result = {}
  result.top = datatableInfo.length
  result.skip = datatableInfo.start
  result.sorts = datatableInfo.order.reduce(function (accumulator, orderObj) {
    const data = datatableInfo.columns[orderObj.column].data
    const sort = sorts[orderObj.dir]
    if (data && sort) {
      accumulator[data] = sort
    }
    return accumulator
  }, {})
  result.version = datatableInfo.draw
  return result
}

export function translateDatatableQueryToTableQuery (datatableQuery) {
  const result = adaptDatatableInfo(datatableQuery.tableData)
  result.parameters = datatableQuery.paramsPost
  return result
}

export function translateConfigToDataTables (tableConfig, i18n) {
  const result = {
    // extra default configs
    bAutoWidth: tableConfig.autoWidth,
    bInfo: true,
    bFilter: true,
    processing: true,
    serverSide: true,
    displayStart: 0,
    pagingType: 'input',
    dom: 't<"dataTable-table-info"li><"dataTable-pagination-wrapper"p>',
    language: tableI18n(i18n)
  }

  if (tableConfig.ajax) {
    result.ajax = {
      url: tableConfig.ajax.url,
      type: tableConfig.ajax.method
    }
  }

  if (tableConfig.rowGroup) {
    const { rowGroup } = tableConfig
    const property = tableConfig.columns.find(({ name }) => name === rowGroup.groupByColumn)?.property
    if (property) {
      result.rowGroup = {
        ...rowGroup,
        dataSrc: property
      }
    }
  }

  result.paging = tableConfig.pagination.enabled
  result.pageLength = tableConfig.pagination.pageLength
  result.lengthChange = tableConfig.pagination.pageLengthMenu.enabled
  result.lengthMenu = tableConfig.pagination.pageLengthMenu.options
  if (tableConfig.pagination.pageNumber > 1) {
    result.displayStart = tableConfig.pagination.pageLength * (tableConfig.pagination.pageNumber - 1)
  }
  result.order = tableConfig.columns.map(function (column, index) {
    switch (column.initialSortDirection) {
      case 'ascendant':
        return [index, 'asc']
      case 'descendant':
        return [index, 'desc']
      default:
        return null
    }
  }).filter(function (column) {
    return column !== null
  })
  result.columns = tableConfig.columns.map(function (column) {
    const title = column.title ? `<div class="fx-th-container"><span class="fx-th-label">${column.title}</span></div>` : column.title
    const columnResult = {
      title,
      name: column.name,
      className: column.name,
      data: column.property == null ? null : column.property,
      visible: column.visible !== false,
      orderable: column.sortable
    }
    if (column.columnWidth != null) {
      columnResult.width = column.columnWidth
    }
    return columnResult
  })

  return result
}

export function applyInfoCallback (i18n, element) {
  return function (oSettings, iStart, iEnd, iMax, iTotal) {
    const tableInstance = oSettings.oInstance
    styleLengthMenu(element[0])
    let showPageLengthEntries = false

    if (iTotal > 0) {
      const hasPaginationWithOtherPageLength = Math.min.apply(Math, oSettings.aLengthMenu) < iTotal
      const hasPagination = oSettings._iDisplayLength < iTotal
      showPageLengthEntries = hasPagination || hasPaginationWithOtherPageLength
    }

    $('.dataTables_length', $(tableInstance).parent()).toggle(showPageLengthEntries)

    /*
           It would be best with "Total records </ b>", but the nossis-ui messages brings everything and conflicts
           with how nossis-inv internationalizes data paging.
         */
    return iTotal > 0 ? i18n('datatables.info.label.entriestoshow').replace('_TOTAL_', iTotal) : ''
  }
}

export function applyRenderCallback (columns, configColumns, i18n) {
  return columns.map((column, index) => {
    const configColumn = configColumns[index]
    const renderName = configColumn.cellTemplate

    if (renderName) {
      const callback = loadRenderer(renderName)

      column.render = (data, type, row, meta) => {
        const rowNumber = meta.row
        const columnNumber = meta.col
        const context = {
          rowNumber,
          columnName: meta.settings.aoColumns[columnNumber].name,
          cellData: data,
          rowData: row,
          i18n
        }
        return callback(context)
      }

      column.createdCell = (td, cellData, rowData, row, col) => {
        const $td = $(td)
        $td.attr('data-row-number', row)
        $td.attr('data-column-number', col)

        if (configColumn.expandable) {
          $td.addClass('expandable-column-cell')
        }
      }
    } else {
      column.render = data => data == null || data === '' ? '--' : htmlEscape(data)
      column.createdCell = function (td, cellData, rowData, row, col) {
        const $td = $(td)
        if (cellData != null && cellData !== '') {
          $td.attr('title', cellData)
        }
        $td.attr('data-row-number', row)
        $td.attr('data-column-number', col)
        if (configColumn.expandable) {
          $td.addClass('expandable-column-cell')
        }
      }
    }
    return column
  })
}

function styleLengthMenu(element) {
  element.parentElement.querySelectorAll('.dataTables_length select').forEach(select => {
    select.classList.add('input-sm', 'form-control')
  })
}
