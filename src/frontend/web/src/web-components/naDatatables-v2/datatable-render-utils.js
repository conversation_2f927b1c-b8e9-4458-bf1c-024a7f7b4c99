import { htmlEscape } from '~utils/string.util'
import { i18n } from '~utils/i18n'

export function dataAttributes (parameters) {
  const dataAttrs = Object.entries(parameters).filter(([key, val]) => key.startsWith('data-') && val !== '')
  if (dataAttrs.length <= 0) { return '' }
  return dataAttrs.map(([key, val]) => `${key}="${htmlEscape(String(val))}"`).join(' ')
}

export function link (parameters = {}) {
  const { icon, action: rawAction, title: rawTitle, context, enabled, visible } = parameters
  if (visible === false) { return '' }
  const title = htmlEscape(rawTitle)
  const action = htmlEscape(rawAction)
  const classes = `btn fx-btn-action  ${(enabled === false) ? 'disabled' : ''}`
  const ariaDisabledAttribute = (enabled === false) ? 'aria-disabled="true"' : ''
  return `<a ${dataAttributes(parameters)} ${ariaDisabledAttribute} data-row="${context.rowNumber}" title="${title}" data-action="${action}" class="${classes}"><i class="${icon}"></i></a>`
}

link.withContext = (context) => (parameters) => link({ ...parameters, context })

/**
 * Renders the action menu button
 * @param {object} params - menu button params
 * @param {string} params.icon - button icon
 * @param {string} params.title - button tooltip text
 * @param {boolean} [params.enabled] - button enable status
 * @param {string | string[]} dropdown - dropdown content, if empty the button is not rendered
 * @returns {string} - menu button html content
 */
export function menuButton ({ icon, title, enabled } = {}, dropdown = '') {
  const dropdownHtml = Array.isArray(dropdown) ? dropdown.join('') : dropdown
  if (dropdownHtml === '') {
    return ''
  }
  const classes = 'btn fx-btn-action dropdown-toggle' + (enabled === false ? ' disabled' : '')
  const buttonHtml = `<button class="${classes}" data-toggle="dropdown" aria-haspopup="true" title="${htmlEscape(title)}">` +
    `<i class="${htmlEscape(icon)}"></i>` +
    '</button>'
  return '<div class="btn-group">' +
    buttonHtml +
    `<ul class="dropdown-menu fx-dropdown-open-to-left" role="menu">${dropdownHtml}</ul>` +
    '</div>'
}

export function menuLink (parameters = {}) {
  const { icon, action, title, i18nTitle, context, enabled, label, i18nLabel, visible } = parameters
  const titleHtml = (() => {
    if (title) { return htmlEscape(title) }
    if (i18nTitle) { return htmlEscape(i18n(i18nTitle)) }
    return ''
  })()
  const labelHtml = (() => {
    if (label) { return htmlEscape(label) }
    if (i18nLabel) { return `<x-i18n key="${htmlEscape(i18nLabel)}"></x-i18n>` }
    return ''
  })()

  if (visible === false) { return '' }
  const iconHtml = icon ? `<i class="${icon}"></i> ` : ''
  if (enabled === false) {
    return '<li class="disabled">' +
      `<a ${dataAttributes(parameters)} class="disabled" data-action="${action}" title="${titleHtml}" style="pointer-events: none">` +
      iconHtml + labelHtml +
      '</a>' +
      '</li>'
  } else {
    return '<li>' +
      `<a ${dataAttributes(parameters)} data-row="${context.rowNumber}" data-action="${action}" title="${titleHtml}">` +
      iconHtml + labelHtml +
      '</a>' +
      '</li>'
  }
}

menuLink.withContext = (context) => (parameters) => menuLink({ ...parameters, context })

export function menuHeader ({ icon, label, i18nLabel, visible } = {}) {
  if (visible === false) {
    return ''
  }
  const labelHtml = (() => {
    if (label) { return htmlEscape(label) }
    if (i18nLabel) { return `<x-i18n key="${htmlEscape(i18nLabel)}"></x-i18n>` }
    return ''
  })()
  if (icon) {
    return '<li class="dropdown-header"><b><i class="' + icon + ' fx-icon"></i> ' + labelHtml + '</b></li>'
  }
  return '<li class="dropdown-header"><b>' + labelHtml + '</b></li>'
}

export function menuDivider ({ visible } = {}) {
  if (visible === false) {
    return ''
  }
  return '<li class="divider"></li>'
}

export const defaultIconMap = Object.freeze({
  view: 'fuxicons fuxicons-eye',
  edit: 'glyphicon glyphicon-pencil',
  clone: 'fuxicons fuxicons-clone',
  delete: 'glyphicon glyphicon-remove',
  moreOptions: 'fa fa-ellipsis-h fx-icon'
})
