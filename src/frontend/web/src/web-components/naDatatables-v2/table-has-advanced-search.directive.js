import datatableModule from './datatable.angular-module'
import { providers } from '~basemodule/app/basemodule-providers'
import template from './table-has-advanced-search.directive.html'
import i18n from '~utils/i18n'
import { providerName as tableDatatableProviderName } from './datatable.directive'

export const providerName = providers.datatables.directives.TableHasAdvancedSearch
datatableModule.directive(providerName, [
  tableHasAdvancedSearchDirective
])

function tableHasAdvancedSearchDirective () {
  return {
    restrict: 'A',
    require: '^^' + tableDatatableProviderName,
    priority: 20,
    terminal: false,
    link: function (scope, element, attrs, ctrl) {
      const $template = $(template)

      ctrl.moveElementToSection(element).then(() => {
        $template.appendTo(element)
        init(element)
      })

      function init (element) {
        ctrl.getStoreInfo().then(({ table, store }) => {
          const $toolbar = $(element)
          const $input = $toolbar.find('.tool-bar__input')

          initTemplate($toolbar)

          const feature = 'advanced-search'
          const featureStateParams = (state) => ({ table, feature, state })
          const getState = (store) => store.getters.featuresState[table][feature]
          const updateState = (state) => store.setters.updateFeatureState(featureStateParams(state(getState(store))))
          const stateChanged = (snapshot1, snapshot2) => {
            const featuresState1 = snapshot1.getters.featuresState
            const featuresState2 = snapshot2.getters.featuresState
            return featuresState1[table][feature] !== featuresState2[table][feature]
          }

          updateState(() => ({
            searchText: '',
          }))

          const updateView = (newSnapshot) => {
            const { searchText } = getState(newSnapshot)
            $input.val(searchText)
          }

          store.listenStateChange((newSnapshot, oldSnapshot) => {
            if (!stateChanged(newSnapshot, oldSnapshot)) {
              return
            }
            updateView(newSnapshot)
            filterRowsBySearch()
          }).until(({ getters }) => !getters.definedTableSet.has(table))

          updateView(store)

          function filterRowsBySearch () {
            const { searchText } = getState(store)
            if (searchText.length <= 0) {
              return ctrl.showAllRows()
            }
            ctrl.getRowsData().then(function (rowsData) {
              const pattern = new RegExp(searchText, 'gi')
              const tableElement = ctrl.tableElement
              const indexesToHide = new Set([...Array(rowsData.length).keys()])
              store.getters.visibleColumns[table].forEach(function (column) {
                tableElement.find('td.' + column.name).toArray().forEach(function (element) {
                  if (element.innerText.match(pattern)) {
                    indexesToHide.delete(+element.getAttribute('data-row-number'))
                  }
                })
              })

              store.setters.setHiddenRowNumbers({
                table,
                rowNumbers: Array.from(indexesToHide)
              })
            })
          }

          function initTemplate ($toolbar) {
            $input.attr('placeholder', i18n('datatables.action.label.search'))

            const updateStateToInputVal = () => {
              updateState((oldState) => {
                const inputVal = $input.val()
                if (oldState.searchText === inputVal) {
                  return oldState
                }
                return {
                  ...oldState,
                  searchText: inputVal,
                }
              })
            }

            $toolbar.on('input', '.tool-bar__input', updateStateToInputVal)
          }
        })
      }
    }
  }
}
