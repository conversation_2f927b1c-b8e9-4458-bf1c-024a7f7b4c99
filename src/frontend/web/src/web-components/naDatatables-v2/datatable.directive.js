import datatableModule from './datatable.angular-module'
import { providers } from '~basemodule/app/basemodule-providers'
import { DatatableController } from '~components/naDatatables-v2/datatable.controller'
import i18n from '~utils/i18n'
import {
  applyInfoCallback,
  applyRenderCallback,
  translateConfigToDataTables
} from '~components/naDatatables-v2/datatable.table-impl'
import { translateDatatableQueryToTableQuery } from './datatable.table-impl'
import { showNotificationOn, hideNotificationOn } from '~utils/element-notification'
import { arrayEquals } from '~utils/array-utils.util'
import * as proxy from '~utils/proxy'
import './renders/checkbox-selection-column.datatable-renderer'
import './renders/radio-selection-column.datatable-renderer'

import 'datatables.net'
import 'basepack/lib/datatables/1.10/extensions/ColReorder/js/dataTables.colReorder.min.js'
import 'basepack/lib/datatables/1.10/extensions/RowGroup/js/dataTables.rowGroup.min.js'
import 'datatables.net-plugins/pagination/input'

import { CssStyleSheetLoader } from '~utils/stylesheet-utils.util'

const loader = CssStyleSheetLoader(() => import(/* webpackChunkName: "web-components/components-styles" */ './datatable.directive.css'))
let loadStyleSheet = () => {
  loader.then(styleSheet => document.adoptedStyleSheets.push(styleSheet))
  loadStyleSheet = () => {}
}

export const providerName = providers.datatables.directives.TableDatatable
datatableModule.directive(providerName, [
  datatableDirective
])

function datatableDirective () {
  return {
    restrict: 'A',
    priority: 1,
    controller: DatatableController,
    scope: {},
    terminal: false,
    link: function (scope, element, attrs, ctrl) {
      loadStyleSheet()
      const tableElement = (() => {
        if (element.is('table')) {
          return element
        } else {
          const tableElement = $("<table class='table table-striped table-hover'></table>")
          tableElement.appendTo(element)
          tableElement.attr('id', element.attr('id'))
          element.removeAttr('id')
          return tableElement
        }
      })()
      const url = (() => {
        const result = element.attr('data-na-portal-table-config-url')
        if (result) { return result }
        const deprecatedResult = element.attr('data-config-url')
        if (deprecatedResult) {
          console.warn('deprecated attribute "data-config-url", replace with "data-na-portal-table-config-url"')
        }
        return deprecatedResult
      })()

      let currentConfig = null
      let currentDatatableConfig = null
      const getDataTableAPI = () => tableElement.dataTable().api()

      element.removeAttr('data-config-url')
      element.on('click', 'td.expandable-column-cell', event => {
        const $target = $(event.target)
        const columnNumber = $target.attr('data-column-number')
        element
          .find('td.expandable-column-cell.focus')
          .each((index, element) => $(element).removeClass('focus'))
        element
          .find('td.expandable-column-cell[data-column-number="' + columnNumber + '"]')
          .each((index, element) => $(element).addClass('focus'))
        $(this).addClass('focus')
      })

      function cleanTableConfig (data) {
        return {
          ...data,
          columns: data.columns.map(column => ({
            ...column,
            visible: column.visible !== false,
            visibilityToggle: column.visibilityToggle !== false
          }))
        }
      }

      function initTable (newConfig) {
        const reducedServerData = ctrl.reduceConfig(newConfig)
        const cleanConfig = cleanTableConfig(reducedServerData)
        initDataTable(cleanConfig)
        const { table, store } = ctrl.initTableStore(scope, tableElement, cleanConfig)
        initTableListeners({ table, store })
      }

      function destroyTable () {
        return ctrl.getDatatableApi().then(api => {
          api.clear()
          api.destroy()
          tableElement.empty()
          return api
        })
      }

      function reInitTable () {
        return destroyTable().then(() => {
          return ctrl.getStoreInfo().then(({ table, store }) => {
            const config = store.getters.config[table]
            initDataTable(config)
          })
        })
      }

      /*
                   *   This methods serves with the purpose to guarantee that if the table is wrapped, the wrapper
                   * will have a constant class that is familiar as well as independent of an external library;
                   * Unfortunately "container" is not a good name because bootstrap uses that class on the css.
                   */
      function addTableContainerClass () {
        const classes = $.fn.DataTable.ext.classes
        if (!classes.sWrapper.split(' ').includes('table-container')) {
          classes.sWrapper = classes.sWrapper + ' table-container'
        }
      }

      ctrl.addReloadMiddleware({
        priority: Number.MIN_SAFE_INTEGER,
        callback: function preventLoadingOnDestroyTable (data, next) {
          if (!scope.$$destroyed) {
            next(data)
          }
        }
      })

      let currentDrawVersion = 0

      ctrl.addReloadMiddleware({
        priority: 3000,
        callback: function (data, next) {
          const tableQuery = translateDatatableQueryToTableQuery({ tableData: data, paramsPost: {} })
          currentDrawVersion = tableQuery.version
          next(tableQuery)
        }
      })

      function initDataTable (newConfig) {
        addTableContainerClass()
        const data = translateConfigToDataTables(newConfig)
        data.ajax = ctrl.applyReloadCallback(data.ajax)
        data.columns = applyRenderCallback(data.columns, newConfig.columns, i18n)
        data.fnInfoCallback = applyInfoCallback(i18n, element)
        data.createdRow = ctrl.applyCreatedRowCallback()
        // data.drawCallback = ctrl.applyDrawCallback();
        data.drawCallback = function (settings) {
          ctrl.getStoreInfo()
            .then(({ table, store }) => {
              const rowsData = Array.from(dataTableAPI.data())
              store.setters.updateData({ table, rowsData })
              const pageNumber = dataTableAPI.page.info().page + 1
              element.attr('data-na-portal-table-page-number', pageNumber)
              element.attr('data-na-portal-table-draw-version', currentDrawVersion)
              ctrl.triggerDrawCallback(settings)
            })
        }
        const dataTableAPI = tableElement.DataTable(data)
        $(tableElement).find('thead').addClass('fx-thead')
        currentDatatableConfig = data
        ctrl.initDatatableApiFromElement(tableElement)
      }

      ctrl.getInitialTableConfig().then(initialTableConfig => {
        currentConfig = currentConfig || initialTableConfig
        initTable(currentConfig)
      })

      if (url) {
        proxy.get({
          url,
          onSuccess (serverData) {
            ctrl.setInitialTableConfig(serverData)
          }
        })
      }

      function initTableListeners ({ table, store }) {
        function isSameColumn (col1, col2) {
          return col1.name === col2.name &&
                      col1.property === col2.property &&
                      col1.title === col2.title
        }

        store.reflect(
          snapshot => snapshot.getters.columns[table],
          (newColumns, oldColumns) => {
            if (!arrayEquals(newColumns, oldColumns, isSameColumn)) {
              reInitTable()
            }
          }).until(({ getters }) => !getters.definedTableSet.has(table))

        store.reflect(
          snapshot => snapshot.getters.visibleColumnNames[table],
          visibleColumnNames => {
            const dataTableAPI = getDataTableAPI()
            const columnNames = new Set(visibleColumnNames)
            dataTableAPI.settings()[0].aoColumns.forEach(function (aoColumn) {
              dataTableAPI.column(aoColumn.idx).visible(columnNames.has(aoColumn.name))
            })
          }
        ).until(({ getters }) => !getters.definedTableSet.has(table))

        let reloadNotification
        store.reflect(
          snapshot => snapshot.getters.isTableReloading[table],
          isTableReloading => {
            if (isTableReloading) {
              reloadNotification = showNotificationOn(tableElement)
                .withMessage(i18n('na.loading'))
            } else if (reloadNotification != null) {
              reloadNotification.hide()
            }
          }
        ).until(({ getters }) => !getters.definedTableSet.has(table))

        store.reflect(
          snapshot => snapshot.getters.hiddenRowNumbers[table],
          hiddenRowNumbers => {
            const dataTableAPI = getDataTableAPI()
            dataTableAPI.rows()[0].forEach(function (rowIndex) {
              $(dataTableAPI.row(rowIndex).node()).toggle(!hiddenRowNumbers.includes(rowIndex))
            })
          }
        ).until(({ getters }) => !getters.definedTableSet.has(table))

        store.reflect(
          snapshot => snapshot.getters.loadingRows[table],
          (loadingRows, oldValue, snapshot) => {
            const dataTableAPI = getDataTableAPI()
            const hiddenRows = snapshot.getters.hiddenRowNumbers[table]
            dataTableAPI.rows()[0].forEach(function (rowIndex) {
              const isIncluded = loadingRows[rowIndex] != null
              const wasIncluded = oldValue[rowIndex] != null
              if (wasIncluded && !isIncluded) {
                const $row = $(dataTableAPI.row(rowIndex).node())
                const $rowLoadingDiv = $row.find('.rowLoading')
                if ($rowLoadingDiv.length <= 0) {
                  return
                }
                hideNotificationOn($row).then(() => {
                  $rowLoadingDiv.remove()
                })
              }
              if (isIncluded && !wasIncluded && !hiddenRows.includes(rowIndex)) {
                const $row = $(getDataTableAPI().row(rowIndex).node())
                const $rowLoadingDiv = $('<div class="rowLoading"></div>')
                const rowHeight = $row.height()
                $rowLoadingDiv.css('height', rowHeight)
                $rowLoadingDiv.css('position', 'absolute')
                $rowLoadingDiv.css('left', '0')
                $rowLoadingDiv.css('width', '100%')
                $rowLoadingDiv.appendTo($row)

                showNotificationOn($row)
                  .withMessage(loadingRows[rowIndex].message)
              }
            })
          }
        ).until(({ getters }) => !getters.definedTableSet.has(table))

        store.reflect([
          snapshot => snapshot.getters.rowsData[table],
          snapshot => snapshot.getters.isTableReloading[table]
        ], ([rowsData], [lastRowsData, wasTableReloading], newSnapshot) => {
          if (!wasTableReloading && rowsData.length === lastRowsData.length) {
            rowsData.forEach((rowData, i) => {
              if (rowData !== lastRowsData[i]) {
                const dataTableAPI = getDataTableAPI()
                dataTableAPI.rows(i).data(rowsData[i])
                tableElement.find(`td[data-row-number="${i}"]`).each(function () {
                  const $td = $(this)
                  const columnNumber = parseInt($td.attr('data-column-number'))
                  const datatableColumn = currentDatatableConfig.columns[columnNumber]
                  const configColumn = newSnapshot.getters.columns[table][columnNumber]
                  const newHtml = datatableColumn.render(
                    configColumn.property ? rowData[configColumn.property] : null,
                    null,
                    rowData,
                    { row: i, col: columnNumber, settings: dataTableAPI.settings()[0] }
                  )
                  $td.html(newHtml)
                })
              }
            })
          }
        }).until(({ getters }) => !getters.definedTableSet.has(table))
      }

      scope.$on('$destroy', destroyTable)
    }
  }
}
