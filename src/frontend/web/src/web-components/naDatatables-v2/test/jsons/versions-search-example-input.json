{"ajax": {"url": "/operationscatalog/versions/search", "method": "POST"}, "columns": [{"name": "id", "title": "ID", "property": "id", "sortable": true, "initialSortDirection": "ascendant"}, {"name": "name", "title": "Nome", "property": "name", "sortable": true}, {"name": "actions", "title": "<PERSON><PERSON>ç<PERSON><PERSON>", "sortable": false, "cellTemplate": "renderName"}], "pagination": {"enabled": true, "pageLengthMenu": {"enabled": true, "options": [15, 30, 45]}, "pageLength": 15}, "autoWidth": false}