{"bAutoWidth": false, "bInfo": true, "bFilter": true, "processing": true, "serverSide": true, "displayStart": 0, "pagingType": "input", "dom": "t<\"dataTable-table-info\"li><\"dataTable-pagination-wrapper\"p>", "language": {"paginate": {"first": "", "previous": "anterior", "next": "próximo", "last": "", "info": "datatables.pagination.info"}, "aria": {"sortAscending": "asc", "sortDescending": "desc"}, "emptyTable": "Não existem registos a apresentar", "infoFiltered": "- a filtrar de _MAX_ resultados.", "infoPostFix": "", "thousands": ".", "lengthMenu": "_MENU_ <span>registos por página</span>", "loadingRecords": "A carregar...", "processing": "A processar", "search": "Filtrar resultados", "zeroRecords": "Não existem registos a apresentar"}, "ajax": {"url": "/operationscatalog/versions/search", "type": "POST"}, "paging": true, "pageLength": 15, "lengthChange": true, "lengthMenu": [15, 30, 45], "order": [[0, "asc"]], "columns": [{"title": "<div class=\"fx-th-container\"><span class=\"fx-th-label\">ID</span></div>", "name": "id", "className": "id", "data": "id", "visible": true, "orderable": true}, {"title": "<div class=\"fx-th-container\"><span class=\"fx-th-label\">Nome</span></div>", "name": "name", "className": "name", "data": "name", "visible": true, "orderable": true}, {"title": "<div class=\"fx-th-container\"><span class=\"fx-th-label\">Acç<PERSON>es</span></div>", "name": "actions", "className": "actions", "data": null, "visible": true, "orderable": false}]}