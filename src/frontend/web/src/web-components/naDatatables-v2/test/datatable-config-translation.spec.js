import tape from 'tape'
import { translateConfigToDataTables } from '../datatable.table-impl'

const jsons = {
  versionSearchExampleInput: require('./jsons/versions-search-example-input.json'),
  versionSearchExampleOutput: require('./jsons/versions-search-example-output.json')
}

const i18nKeys = {
  'datatables.navigation.label.first': '',
  'datatables.navigation.label.previous': 'anterior',
  'datatables.navigation.label.next': 'próximo',
  'datatables.navigation.label.last': '',
  'datatables.info.label.emptytable': 'Não existem registos a apresentar',
  'datatables.info.label.infofiltered': '- a filtrar de _MAX_ resultados.',
  'datatables.info.label.infopostfix': '',
  'datatables.info.label.infothousands': '.',
  'datatables.info.label.lengthmenu': 'registos por página',
  'datatables.info.label.loadingrecords': 'A carregar...',
  'datatables.info.label.processing': 'A processar',
  'datatables.info.label.search': 'Filtrar resultados',
  'datatables.info.label.zerorecords': 'Não existem registos a apresentar'
}

const i18n = (key) => i18nKeys[key] != null ? i18nKeys[key] : key

tape.test('Table Config Translation to Datatable compatible config - translate contents of "versions-search-example-input.json" to "versions-search-example-output.json"', (t) => {
  const datatableConfig = translateConfigToDataTables(jsons.versionSearchExampleInput, i18n)
  // since column
  datatableConfig.columns.forEach(function (column, index) {
    if (column.render) {
      column.render = jsons.versionSearchExampleInput.columns[index].cellTemplate
    }
  })
  t.deepEqual(datatableConfig, jsons.versionSearchExampleOutput)
  t.end()
})
