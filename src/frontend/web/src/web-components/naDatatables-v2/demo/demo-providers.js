import { provide as provideI18n } from '~utils/i18n/provider'
import { provide as provideToast } from '~utils/toast-notification/provider'
import { provide as provideProxy } from '~utils/proxy/provider'

export const searchConfig = {
  ajax: {
    url: '/mock/table/search',
    method: 'POST'
  },
  columns: [
    {
      name: 'name',
      title: 'Operação',
      property: 'name',
      sortable: true,
      visible: true,
      visibilityToggle: true
    },
    {
      name: 'description',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      property: 'description',
      sortable: true,
      visible: true
    },
    {
      name: 'entity',
      title: 'Entidade',
      property: 'entityName',
      sortable: true,
      visible: true
    },
    {
      name: 'state',
      title: 'Estado',
      property: 'state',
      sortable: false,
      columnWidth: '125px',
      visible: true,
      visibilityToggle: true
    },
    {
      name: 'dynamic',
      title: 'Coluna dinâmica',
      property: 'dynamic',
      sortable: false,
      visible: false,
      visibilityToggle: true
    },
    {
      name: 'tags',
      title: 'Tags',
      property: 'tags',
      sortable: false,
      visible: true,
      visibilityToggle: false
    }
  ],
  pagination: {
    enabled: true,
    pageLengthMenu: {
      enabled: true,
      options: [15, 30, 45]
    },
    pageLength: 15
  },
  autoWidth: false
}

const i18nMap = {
  'na.modal.button.ok': 'OK',
  'na.modal.button.cancel': 'Cancel',
  'datatables.info.label.entriestoshow': 'Total records <b> _TOTAL_ </b>',
  'datatables.info.label.infofiltered': '- filtering from _MAX_ results.',
  'datatables.action.label.advancedsearch': 'Advanced Search',
  'datatables.action.label.search': 'Search',
  'datatables.search.advancedsearch.minimum.length': 'Minimum 1 char',
  'na.button.apply': 'Apply',
  'na.button.label.cancel': 'Cancel',
  'na.datatables.columnFilter.search.placeholder': 'search'
}

export const mockData = [{
  name: 'aaa',
  entityName: 'aaa',
  state: 'ccc',
  description: 'aaa',
  dynamic: 'test val 1',
  tags: 'aaa'
}, {
  name: 'bbb',
  entityName: 'bbb',
  state: 'bbb',
  description: 'bbb',
  tags: 'bbb',
  dynamic: 'test val 2'
}, {
  dynamic: 'test val 3',
  name: 'ddd',
  entityName: 'rrr',
  state: 'abc',
  description: 'aaa',
  tags: 'pqr'
}]

provideI18n({
  translate (key, args) {
    return i18nMap[key] || key
  }
})

provideToast({
  hideToast: () => console.log('hide toast notification called')
})

const mockProxy = {
  request (parameters) {
    const { route } = parameters
    const url = parameters.url || route.url
    const method = parameters.method || route.method
    const data = parameters.data || {}

    const onSuccess = typeof parameters.onSuccess === 'function' ? parameters.onSuccess : () => {}

    if (method === 'GET' && /table\/config\/search$/.test(url)) {
      onSuccess(searchConfig)
    }

    if (method === 'POST' && /mock\/table\/search$/.test(url)) {
      const sorts = data.sorts

      const dbData = mockData

      const tableData = Object.keys(sorts).reduce((acc, key) => {
        const sortOrder = sorts[key]
        const ascendantComparator = (a, b) => a[key] < b[key] ? -1 : a[key] > b[key] ? 1 : 0
        const comparator = sortOrder === 'ascendant' ? ascendantComparator : (a, b) => ascendantComparator(b, a)
        const newList = dbData.slice()
        newList.sort(comparator)
        return newList
      }, dbData)

      onSuccess({
        data: tableData,
        recordsTotal: tableData.length,
        recordsFiltered: tableData.length
      })
    }
  }
}

provideProxy(mockProxy)
