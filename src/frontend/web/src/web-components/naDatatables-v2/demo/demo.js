import { searchConfig, mockData } from '~components/naDatatables-v2/demo/demo-providers'
import $ from 'jquery'
import { bootstrap } from 'angular'
import '~utils/element-notification/implementation'
import { moduleName } from '~components/naDatatables-v2/datatable.angular-module'
import '~components/naDatatables-v2/datatable.directive'
import '~components/naDatatables-v2/datatable-load-using-ajax.directive'
import '~components/naDatatables-v2/table-has-column-filter.directive'
import '~components/naDatatables-v2/table-has-advanced-search.directive.js'

$('.code--import').text(`import "~components/naDatatables-v2/datatable.directive";
import "~components/naDatatables-v2/datatable-load-using-ajax.directive";
import "~components/naDatatables-v2/table-has-column-filter.directive";
import "~components/naDatatables-v2/table-has-advanced-search.directive";`)

$('.code-config--table-1').text(JSON.stringify(searchConfig, null, 2))
$('.code-mock-data--table-1').text(JSON.stringify({
  data: mockData,
  recordsTotal: mockData.length,
  recordsFiltered: mockData.length
}, null, 2))

$(document.body).css('overflow', 'auto')
bootstrap($('.app'), [moduleName])
