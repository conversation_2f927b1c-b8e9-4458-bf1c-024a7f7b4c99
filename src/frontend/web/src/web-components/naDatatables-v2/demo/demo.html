<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= title %></title>
    <%= css %></head>
<body>
<%= navbar %>
<div class='container app'>
    <h1><%= title %></h1>

    <h3>Dependencies</h3>
    <%= dependencyTable %>

    <h3>Imports</h3>
    <pre><code class="code--import lang-js"></code></pre>

    <h3>Basic Example</h3>

    <div data-na-portal-table-datatable
         data-na-portal-table-load-using-ajax
         data-config-url="/table/config/search"></div>
    <pre><code class="code--table-1 xml">&lt;div data-na-portal-table-datatable
    data-na-portal-table-load-using-ajax
    data-config-url=&quot;/table/config/search&quot;&gt;&lt;/div&gt;</code></pre>

    <h4><code>/table/config/search</code> response</h4>
    <pre><code class="code-config--table-1"></code></pre>


    <h4><code>/mock/table/search</code> response</h4>
    <pre><code class="code-mock-data--table-1"></code></pre>


    <h3>With column filter</h3>


    <div data-na-portal-table-datatable
         data-na-portal-table-load-using-ajax
         data-config-url="/table/config/search">
        <div data-na-portal-table-has-column-filter></div>
    </div>
    <pre><code class="code--table-2 xml">&lt;div data-na-portal-table-datatable
     data-na-portal-table-load-using-ajax
     data-config-url=&quot;/table/config/search&quot;&gt;
  &lt;div data-na-portal-table-has-column-filter&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>

    <h3>With advanced search</h3>

    <div data-na-portal-table-datatable
         data-na-portal-table-load-using-ajax
         data-config-url="/table/config/search">
        <div data-na-portal-table-has-advanced-search></div>
    </div>
    <pre><code class="code--table-3 xml">&lt;div data-na-portal-table-datatable
     data-na-portal-table-load-using-ajax
     data-config-url=&quot;/table/config/search&quot;&gt;
  &lt;div data-na-portal-table-has-advanced-search&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>

    <h3>With both advanced search and column filter</h3>

    <div data-na-portal-table-datatable
         data-na-portal-table-load-using-ajax
         data-config-url="/table/config/search">
        <div data-na-portal-table-has-advanced-search></div>
        <div data-na-portal-table-has-column-filter></div>
    </div>
    <pre><code class="code--table-4 xml">&lt;div data-na-portal-table-datatable
     data-na-portal-table-load-using-ajax
     data-config-url=&quot;/table/config/search&quot;&gt;
  &lt;div data-na-portal-table-has-advanced-search&gt;&lt;/div&gt;
  &lt;div data-na-portal-table-has-column-filter&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>

    <h3>With both advanced search and column filter on separate sides</h3>

    <div data-na-portal-table-datatable
         data-na-portal-table-load-using-ajax
         data-config-url="/table/config/search">
        <div data-na-portal-table-has-column-filter></div>
        <div data-na-portal-table-has-advanced-search data-table-section="left-toolbar"></div>
    </div>
    <pre><code class="code--table-5 xml">&lt;div data-na-portal-table-datatable
     data-na-portal-table-load-using-ajax
     data-config-url=&quot;/table/config/search&quot;&gt;
  &lt;div data-na-portal-table-has-column-filter&gt;&lt;/div&gt;
  &lt;div data-na-portal-table-has-advanced-search data-table-section=&quot;left-toolbar&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>

    <h3>With both advanced search and column filter on the left side</h3>

    <div data-na-portal-table-datatable
         data-na-portal-table-load-using-ajax
         data-config-url="/table/config/search">
        <div data-na-portal-table-has-advanced-search data-table-section=left-toolbar></div>
        <div data-na-portal-table-has-column-filter data-table-section=left-toolbar></div>
    </div>
    <pre><code class="code--table-4 xml">&lt;div data-na-portal-table-datatable
     data-na-portal-table-load-using-ajax
     data-config-url=&quot;/table/config/search&quot;&gt;
  &lt;div data-na-portal-table-has-advanced-search data-table-section=&quot;left-toolbar&quot;&gt;&lt;/div&gt;
  &lt;div data-na-portal-table-has-column-filter data-table-section=&quot;left-toolbar&quot;&gt;&lt;/div&gt;
&lt;/div&gt;</code></pre>


</div>
<%= js %></body>
</html>