/**
 *  table.render-store.js
 *
 *  Stores the named renderers to be used
 */
import { htmlEscape } from '~utils/string.util'

const map = {}

function defaultRenderer (context) {
  return htmlEscape(context.cellData)
}

const errorUsage = 'saveRenderer must accept a parameter with the following type: ' +
  '{ key: string, renderer: (context: object) => string }'

export function saveRenderer (parameters) {
  if (parameters == null) {
    console.error(errorUsage)
    return
  }

  const { key, renderer } = parameters

  if (typeof key !== 'string') {
    console.error(`key parameter must be a string \n${errorUsage}`)
  } else if (typeof renderer !== 'function') {
    console.error(`renderer parameter must be a function \n${errorUsage}`)
  } else if (map[key] != null) {
    console.error(`renderer with key "${key}" already exists`)
  } else {
    map[key] = renderer
  }
}

export function loadRenderer (key) {
  if (map[key] != null) {
    return map[key]
  } else {
    console.error(`renderer with key "${key}" not saved, retuning default renderer`)
    return defaultRenderer
  }
}
