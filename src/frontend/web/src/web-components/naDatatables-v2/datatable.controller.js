import { store } from './tables.store'

const toolbarTemplate = (element) => `<div data-toolbar-of-table="${element.attr('id')}" class="fx-bulk-actions toolbar--table-toolbar clearfix">
<div class="toolbar__left-actions pull-left"></div>
<div class="toolbar__right-actions pull-right"></div>
</div>`

export function DatatableController () {
  const ctrl = this

  let dataTableAPI = null
  let tableName = null
  let tableElement = null
  let initialTableConfig = null
  let toolbar = null
  const tableStoreResolves = []
  const dataTableAPIHandlers = []
  const tableToolbarResolves = []
  const configReducers = []
  const reloadMiddlewares = []
  let drawEventHandlers = []
  const initialTableConfigResolves = []

  const enabledFeatures = {
    colReorder: true
  }

  ctrl.paramsPost = {}

  ctrl.ajaxInfo = {}

  Object.defineProperty(ctrl, 'isTableInitialized', {
    get: () => dataTableAPI != null
  })

  Object.defineProperty(ctrl, 'tableElement', {
    get: () => tableElement
  })

  ctrl.moveElementToSection = (elem) => {
    let section = $(elem).attr('data-table-section')
    if (section == null) {
      section = 'right-toolbar'
    }
    switch (section) {
      case 'right-toolbar':
        return ctrl.getToolbar().then(toolbar => {
          toolbar.find('.toolbar__right-actions').append(document.createTextNode(' ')).append(elem)
        })
      case 'left-toolbar':
        return ctrl.getToolbar().then(toolbar => {
          toolbar.find('.toolbar__left-actions').append(document.createTextNode(' ')).append(elem)
        })

      default:
        return new Promise((resolve, reject) => {
          const message = `invalid data-table-section value "${section}"`
          console.warn(message)
          reject(message)
        })
    }
  }

  ctrl.getToolbar = function () {
    return new Promise((resolve) => {
      if (toolbar != null) {
        resolve(toolbar)
      } else if (tableElement != null) {
        toolbar = $(toolbarTemplate(tableElement))
        toolbar.insertBefore(tableElement)
        resolve(toolbar)
      } else {
        tableToolbarResolves.push(resolve)
      }
    })
  }

  ctrl.getStoreInfo = function () {
    return new Promise(function (resolve) {
      if (tableName != null) {
        resolve({
          table: tableName,
          store
        })
      } else {
        tableStoreResolves.push(resolve)
      }
    })
  }

  ctrl.getInitialTableConfig = function () {
    return new Promise(function (resolve) {
      if (initialTableConfig != null) {
        resolve(initialTableConfig)
      } else {
        initialTableConfigResolves.push(resolve)
      }
    })
  }

  ctrl.setInitialTableConfig = function (config) {
    initialTableConfig = config
    if (initialTableConfig != null) {
      initialTableConfigResolves.forEach(function (resolve) {
        resolve(initialTableConfig)
      })
      initialTableConfigResolves.length = 0
    }
  }

  ctrl.getSortInfo = function () {
    return ctrl.getDatatableApi().then(function (api) {
      const order = api.order()
      const sorts = {
        asc: 'ascendant',
        desc: 'descendant'
      }
      const { aoColumns } = api.settings()[0]
      return order.map(function (orderArray) {
        const index = orderArray[0]
        const order = sorts[orderArray[1]]
        return {
          index,
          name: aoColumns[index].name,
          property: aoColumns[index].data,
          order
        }
      })
    })
  }

  ctrl.reloadTable = ({ resetPagination = false } = {}) => new Promise((resolve) => {
    ctrl.getDatatableApi().then(api => api.ajax.reload(resolve, resetPagination))
  })

  ctrl.getRowsData = () => ctrl.getDatatableApi()
    .then(api => api.data())
    .then(data => Array.from(data))

  ctrl.hideRowByIndex = (rowIndex) => ctrl.toggleRowByIndex(rowIndex, false)

  ctrl.showRowByIndex = (rowIndex) => ctrl.toggleRowByIndex(rowIndex, true)

  ctrl.toggleRowByIndex = (rowIndex, showOrHide) => ctrl.getDatatableApi()
    .then(api => api.row(rowIndex).node())
    .then(node => $(node).toggle(showOrHide))

  ctrl.showAllRows = () => ctrl.getStoreInfo()
    .then(({ table, store }) => store.setters.clearHiddenRows({ table }))

  ctrl.disableColumnReorder = () => {
    enabledFeatures.colReorder = false
  }

  ctrl.getDom = () => {
    const colReorder = enabledFeatures.colReorder ? 'R' : ''
    return `${colReorder}t<"dataTable-table-info"l><"dataTable-pagination-wrapper"ip>`
  }

  ctrl.isReloaded = () => dataTableAPI.settings()._bInitComplete !== undefined

  ctrl.drawCallback = (settings) => {
    handleDrawCallback(settings)
  }

  ctrl.addDrawHandler = function (handler) {
    drawEventHandlers.add(handler)
  }

  ctrl.addDrawHandler = function (handler) {
    drawEventHandlers.push(handler)
    return {
      handler,
      remove: () => {
        drawEventHandlers = drawEventHandlers.filter(function (item) {
          return item !== handler
        })
      }
    }
  }

  function handleDrawCallback (settings) {
    drawEventHandlers.forEach((handler) => handler(settings))
  }
  ctrl.triggerDrawCallback = handleDrawCallback

  ctrl.createdRowCallback = function () {
    // on default do nothing;
  }

  ctrl.applyCreatedRowCallback = function () {
    return function (row, data, dataIndex) {
      ctrl.createdRowCallback(row, data, dataIndex)
    }
  }

  ctrl.initDatatableApiFromElement = (element) => {
    dataTableAPI = element.dataTable().api()
    if (dataTableAPI != null) {
      dataTableAPIHandlers.forEach(handle => handle(dataTableAPI))
      dataTableAPIHandlers.length = 0
    }
  }

  ctrl.getDatatableApi = () => {
    return new Promise(resolve => {
      if (dataTableAPI != null) {
        resolve(dataTableAPI)
      } else {
        dataTableAPIHandlers.push(resolve)
      }
    })
  }

  ctrl.getRowsData = () => {
    return ctrl.getDatatableApi().then(api => Array.from(api.rows().data()))
  }

  ctrl.getPageInfo = () => {
    return ctrl.getDatatableApi().then(api => {
      const pageInfo = api.page.info()
      return {
        start: pageInfo.start,
        end: pageInfo.end,
        pageLength: pageInfo.length,
        pageIndex: pageInfo.page,
        pageNumber: pageInfo.page + 1,
        numberOfPages: pageInfo.pages,
        recordsTotal: pageInfo.recordsTotal
      }
    })
  }

  /** the reload callback should be defined on the directive, otherwise shows an empty table and a warning in the console */
  ctrl.reloadDataCallback = function defaultReloadCallback (data, callback) {
    console.warn('datatable reload callback must be defined, returning empty data')
    callback({
      data: [],
      recordsTotal: 0,
      recordsFiltered: 0
    })
  }

  ctrl.addReloadMiddleware = (parameters) => {
    const sanitizedParameters = (typeof parameters === 'function')
      ? { priority: 1, callback: parameters }
      : { ...parameters }
    reloadMiddlewares.push(sanitizedParameters)
    reloadMiddlewares.sort((a, b) => b.priority - a.priority)
  }

  ctrl.applyReloadCallback = (ajaxInfo) => {
    ctrl.ajaxInfo = ajaxInfo
    return (data, callback) => {
      if (reloadMiddlewares.length <= 0) {
        ctrl.reloadDataCallback(data, callback)
      } else {
        reduceMiddleWare(data, callback)
      }
    }
  }

  ctrl.initTableStore = function (scope, directiveElement, config) {
    tableElement = directiveElement
    const tableNamePrefix = tableElement.attr('id') || 'unnamed-table'
    tableName = tableNamePrefix
    let index = 1
    while (store.getters.definedTableSet.has(tableName)) {
      tableName = tableNamePrefix + index++
    }
    store.setters.initTable({ name: tableName, config })
    scope.$on('$destroy', () => store.setters.destroyTable({ name: tableName }))

    tableStoreResolves.forEach(function (resolve) {
      resolve({
        table: tableName,
        store
      })
    })
    tableStoreResolves.length = 0

    if (tableToolbarResolves.length > 0) {
      toolbar = $(toolbarTemplate(tableElement))
      toolbar.insertBefore(tableElement)
      tableToolbarResolves.forEach(resolve => resolve(toolbar))
      tableToolbarResolves.length = 0
    }

    return {
      table: tableName,
      store
    }
  }

  function reduceMiddleWare (data, done) {
    function applyReducer (reducers, index) {
      return function (acc) {
        if (index < reducers.length) {
          reducers[index](acc, applyReducer(reducers, index + 1))
        } else {
          done(acc)
        }
      }
    }

    applyReducer(reloadMiddlewares.map(reducer => reducer.callback), 0)(data)
  }

  ctrl.applyDrawCallback = () => (settings) => ctrl.triggerDrawCallback(settings)

  ctrl.addConfigReducer = (reducer) => {
    if (typeof reducer === 'function') {
      configReducers.push(reducer)
    } else {
      console.warn('reducer must be a function, ignoring addition')
    }
  }

  ctrl.reduceConfig = config => configReducers.reduce((acc, reduce) => reduce(acc), config)
  ctrl.reinitializeTable = () => console.warn('datatable reinitializeTable callback must be defined, ignoring...')
}
