import { selector, defaultEqualityCheck, defaultMemoization } from '~utils/selector.util'
import { isPlainObject, hasSameKeysObjects, isEmptyObject, areEqualObjects, omitKeys } from '~utils/object-utils.util'
import { arrayEquals } from '~utils/array-utils.util'
import { deepClone } from '~utils/deep-clone.util'
import { deepEquals } from '~utils/deep-equals.util'

const emptyObject = Object.freeze({})
const emptyArray = Object.freeze([])

const initialStateObj = {
  definedTables: emptyArray,
  config: emptyObject,
  hiddenRowNumbers: emptyObject,
  loadingRows: emptyObject,
  isTableReloading: emptyObject,
  rowsData: emptyObject,
  featuresState: emptyObject
}

export const initialState = function () {
  return initialStateObj
}

function objectResultMemoization (equalityCheck = defaultEqualityCheck) {
  return (memoizeFunctions, finalComputation) => {
    const applyDefaultMemoization = defaultMemoization(memoizeFunctions, finalComputation)
    let savedResult = {}
    return function (...args) {
      const result = applyDefaultMemoization(...args)
      if (result === savedResult) {
        return result
      }
      let equalsSavedResult = hasSameKeysObjects(savedResult, result)
      const generatedResult = {}
      for (const key of Object.keys(result)) {
        const isEqualEntry = equalityCheck(savedResult[key], result[key])
        generatedResult[key] = isEqualEntry ? savedResult[key] : result[key]
        equalsSavedResult = equalsSavedResult && isEqualEntry
      }
      savedResult = equalsSavedResult ? savedResult : generatedResult
      return savedResult
    }
  }
}

const columnsSelector = selector.withCustomMemoization(objectResultMemoization())
const columnNamesSelector = selector.withCustomMemoization(objectResultMemoization(arrayEquals))

const featuresState = (state) => state.featuresState
const config = (state) => state.config
const definedTables = (state) => state.definedTables
const hiddenRowNumbers = (state) => state.hiddenRowNumbers
const isTableReloading = (state) => state.isTableReloading
const rowsData = (state) => state.rowsData
const loadingRows = (state) => state.loadingRows
const stateByTable = (state) => state.definedTables.reduce((acc, table) => {
  return Object.assign(acc, {
    [table]: {
      config: state.config[table],
      hiddenRowNumbers: state.hiddenRowNumbers[table],
      loadingRows: state.loadingRows[table],
      isTableReloading: state.isTableReloading[table],
      rowsData: state.rowsData[table],
      featuresState: state.featuresState[table]
    }
  })
}, {})
const columns = columnsSelector(
  [config],
  (config) => Object.keys(config).reduce((acc, key) => Object.assign(acc, { [key]: config[key].columns }), {})
)

const visibilitySwitchableColumns = selector(
  [columns],
  (columns) => Object.keys(columns).reduce((acc, key) => Object.assign(acc, {
    [key]: columns[key].filter((column) => column.visibilityToggle === true)
  }), {})
)

const visibilitySwitchableColumnNames = columnNamesSelector(
  [visibilitySwitchableColumns],
  (visibilitySwitchableColumns) => Object.keys(visibilitySwitchableColumns)
    .reduce((acc, key) => Object.assign(acc, {
      [key]: visibilitySwitchableColumns[key].map(column => column.name)
    }), {})
)

const visibleColumns = selector(
  [columns],
  (columns) => Object.keys(columns).reduce((acc, key) => Object.assign(acc, {
    [key]: columns[key].filter((column) => column.visible === true)
  }), {})
)

const visibleColumnNames = columnNamesSelector(
  [visibleColumns],
  (visibleColumns) => Object.keys(visibleColumns)
    .reduce((acc, key) => Object.assign(acc, {
      [key]: visibleColumns[key].map(column => column.name)
    }), {})
)

const definedTableSet = selector(
  [definedTables],
  function (definedTables) {
    return new Set(definedTables)
  })

export const getters = {
  columns,
  visibilitySwitchableColumns,
  visibilitySwitchableColumnNames,
  visibleColumns,
  visibleColumnNames,
  hiddenRowNumbers,
  isTableReloading,
  rowsData,
  loadingRows,
  featuresState,
  config,
  definedTableSet,
  stateByTable
}

function initTable (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const name = tableInfo.name
  const config = tableInfo.config
  if (typeof name !== 'string') {
    return state
  }

  if (!isPlainObject(config)) {
    return state
  }

  if (definedTableSet(state).has(name)) {
    return state
  }

  return Object.assign({}, state, {
    definedTables: [...state.definedTables, name],
    config: Object.assign({}, state.config, { [name]: deepClone(config) }),
    hiddenRowNumbers: Object.assign({}, state.hiddenRowNumbers, { [name]: emptyArray }),
    isTableReloading: Object.assign({}, state.isTableReloading, { [name]: false }),
    rowsData: Object.assign({}, state.rowsData, { [name]: emptyArray }),
    loadingRows: Object.assign({}, state.loadingRows, { [name]: emptyObject }),
    featuresState: Object.assign({}, state.featuresState, { [name]: emptyObject })
  })
}

function destroyTable (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const name = tableInfo.name

  if (typeof name !== 'string') {
    return state
  }

  if (!definedTableSet(state).has(name)) {
    return state
  }

  const withoutParam = function (obj, param) {
    const newObj = Object.assign({}, obj)
    delete newObj[param]
    return newObj
  }

  return Object.assign({}, state, {
    definedTables: state.definedTables.filter(tableName => name !== tableName),
    config: withoutParam(state.config, name),
    hiddenRowNumbers: withoutParam(state.hiddenRowNumbers, name),
    loadingRows: withoutParam(state.loadingRows, name),
    featuresState: withoutParam(state.featuresState, name)
  })
}

function updateConfig (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const table = tableInfo.table
  const config = tableInfo.config

  if (typeof table !== 'string' || !isPlainObject(config)) {
    return state
  }

  const newConfig = Object.assign({}, state.config)
  newConfig[table] = deepClone(config)
  return Object.assign({}, state, {
    config: newConfig
  })
}

function updateFeatureState (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const table = tableInfo.table
  const feature = tableInfo.feature
  const featureState = tableInfo.state

  if (typeof table !== 'string' ||
        typeof feature !== 'string' ||
        featureState === state.featuresState[table][feature]
  ) {
    return state
  }

  return Object.assign({}, state, {
    featuresState: Object.assign({}, state.featuresState, {
      [table]: Object.assign({}, state.featuresState[table], {
        [feature]: featureState
      })
    })
  })
}

function toggleVisibilityOnColumns (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const { table, columnNames } = tableInfo

  if (typeof table !== 'string' ||
        !Array.isArray(columnNames) ||
        columnNames.length <= 0
  ) {
    return state
  }

  return Object.assign({}, state, {
    config: Object.assign({}, state.config, {
      [table]: Object.assign({}, state.config[table], {
        columns: state.config[table].columns.map((column) => {
          if (columnNames.includes(column.name)) {
            return Object.assign({}, column, { visible: !column.visible })
          } else {
            return column
          }
        })
      })
    })
  })
}

function setHiddenRowNumbers (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const { table, rowNumbers } = tableInfo

  if (typeof table !== 'string' ||
        !Array.isArray(rowNumbers) ||
        rowNumbers.some(rowNumber => typeof rowNumber !== 'number' || rowNumber < 0)
  ) {
    return state
  }

  const newRowNumbers = rowNumbers.length > 0 ? rowNumbers.slice().sort() : emptyArray

  if (arrayEquals(state.hiddenRowNumbers[table], newRowNumbers)) {
    return state
  }

  return Object.assign({}, state, {
    hiddenRowNumbers: Object.assign({}, state.hiddenRowNumbers, {
      [table]: newRowNumbers
    })
  })
}

function clearHiddenRows (state, tableInfo) {
  return setHiddenRowNumbers(state, Object.assign({}, tableInfo, {
    rowNumbers: emptyArray
  }))
}

function setLoadingRows (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const { table, rows } = tableInfo

  const onlyDigits = /^\d+$/
  const isRowInfo = (rowInfo) => {
    if (!isPlainObject(rowInfo)) {
      return false
    }
    return Object.keys(rowInfo).every((key) => {
      const value = rowInfo[key]
      return onlyDigits.test(key) &&
                isPlainObject(value) &&
                typeof value.message === 'string'
    })
  }

  if (typeof table !== 'string' || !isRowInfo(rows)) {
    return state
  }

  const oldRows = state.loadingRows[table]
  const newRows = isEmptyObject(rows) ? emptyObject : Object.keys(rows).reduce((acc, rowNumber) => {
    const { message } = rows[rowNumber]
    if (oldRows[rowNumber] != null && oldRows[rowNumber].message === message) {
      return Object.assign(acc, { [rowNumber]: oldRows[rowNumber] })
    } else {
      return Object.assign(acc, { [rowNumber]: { message } })
    }
  }, {})

  if (areEqualObjects(oldRows, newRows)) {
    return state
  }

  return Object.assign({}, state, {
    loadingRows: Object.assign({}, state.loadingRows, {
      [table]: newRows
    })
  })
}

function applyLoadingOnRow (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const { table, rowNumber, message } = tableInfo

  if (
    typeof table !== 'string' ||
        typeof message !== 'string' ||
        typeof rowNumber !== 'number' ||
        rowNumber < 0
  ) {
    return state
  }
  return setLoadingRows(state, {
    table,
    rows: Object.assign({}, state.loadingRows[table], {
      [rowNumber]: { message }
    })
  })
}

function unapplyLoadingOnRow (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const { table, rowNumber } = tableInfo

  if (
    typeof table !== 'string' ||
        typeof rowNumber !== 'number' ||
        state.loadingRows[table][rowNumber] == null
  ) {
    return state
  }

  return Object.assign({}, state, {
    loadingRows: Object.assign({}, state.loadingRows, {
      [table]: omitKeys(state.loadingRows[table], rowNumber)
    })
  })
}

function clearLoadingRows (state, tableInfo) {
  return setLoadingRows(state, Object.assign({}, tableInfo, {
    rowNumbers: emptyObject
  }))
}

function setTableReloading (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const { table, reloading } = tableInfo

  if (typeof table !== 'string' || typeof reloading !== 'boolean') {
    return state
  }

  return Object.assign({}, state, {
    isTableReloading: Object.assign({}, state.isTableReloading, {
      [table]: reloading
    })
  })
}

function updateData (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const { table, rowsData } = tableInfo

  if (!Array.isArray(rowsData)) {
    return state
  }

  const stateChanges = {}
  if (state.loadingRows[table] !== emptyObject) {
    stateChanges.loadingRows = Object.assign({}, state.loadingRows, { [table]: emptyObject })
  }

  if (state.isTableReloading[table] !== false) {
    stateChanges.isTableReloading = Object.assign({}, state.isTableReloading, { [table]: false })
  }
  if (state.hiddenRowNumbers[table] !== emptyArray) {
    stateChanges.hiddenRowNumbers = Object.assign({}, state.hiddenRowNumbers, { [table]: emptyArray })
  }

  const oldRowsData = state.rowsData[table]
  const oldRowsDataLength = oldRowsData.length
  const newRowsData = rowsData.map((rowData, index) => {
    if (oldRowsDataLength > index && deepEquals(oldRowsData[index], rowData)) {
      return oldRowsData[index]
    }
    return deepClone(rowData)
  })
  if (!arrayEquals(oldRowsData, newRowsData)) {
    stateChanges.rowsData = Object.assign({}, state.rowsData, { [table]: newRowsData })
  }

  if (isEmptyObject(stateChanges)) {
    return state
  }

  return Object.assign({}, state, stateChanges)
}

function updateRow (state, tableInfo) {
  if (!isPlainObject(tableInfo)) {
    return state
  }

  const { table, rowNumber, rowData, resetLoading, resetVisibility } = tableInfo

  const stateChanges = {}
  if (state.loadingRows[table][rowNumber] != null && resetLoading !== false) {
    stateChanges.loadingRows = Object.assign({}, state.loadingRows, {
      [table]: omitKeys(state.loadingRows[table], rowNumber)
    })
  }

  if (state.hiddenRowNumbers[table].includes(rowNumber) && resetVisibility !== false) {
    stateChanges.hiddenRowNumbers = Object.assign({}, state.hiddenRowNumbers, {
      [table]: stateChanges.hiddenRowNumbers[table].filter(row => row !== rowNumber)
    })
  }

  const oldRowsData = state.rowsData[table]
  if (!deepEquals(oldRowsData[rowNumber], rowData)) {
    stateChanges.rowsData = Object.assign({}, state.rowsData, {
      [table]: oldRowsData.map((oldRowData, index) => {
        if (index === rowNumber) { return deepClone(rowData) }
        return oldRowData
      })
    })
  }
  if (isEmptyObject(stateChanges)) {
    return state
  }
  return Object.assign({}, state, stateChanges)
}

export const setters = {
  initTable,
  destroyTable,
  updateFeatureState,
  toggleVisibilityOnColumns,
  updateConfig,
  setHiddenRowNumbers,
  setLoadingRows,
  applyLoadingOnRow,
  unapplyLoadingOnRow,
  clearHiddenRows,
  setTableReloading,
  clearLoadingRows,
  updateData,
  updateRow
}
