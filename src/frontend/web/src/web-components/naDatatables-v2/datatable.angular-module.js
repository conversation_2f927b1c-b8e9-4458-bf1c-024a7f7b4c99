import { module } from 'angular'
import { modules } from '~basemodule/app/basemodule-providers'
export const moduleName = modules.datatables
module(moduleName, [])
export default module(moduleName)

/*
module(moduleName, [
        modules.nossis.proxy
    ]).run([
        na.datatables.services.Datatable,
        function (naDatatablesService) {
            naDatatablesService.loadApplicationExportLimit();
        }
    ]);

 */
