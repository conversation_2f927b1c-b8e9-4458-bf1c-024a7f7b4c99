import { htmlEscape } from '~utils/string.util'
import { saveRenderer } from '~components/naDatatables-v2/datatable-render-store'

function TagsColumnRender(tableSelector, columWidth) {
  const EMPTY_TAG_WIDTH = 10
  const AVERAGE_CHAR_WIDTH = 7
  const columnSize = parseColumnWidth(columWidth)

  $(tableSelector).popover({
    container: tableSelector,
    selector: '.column-tags-button'
  })

  this.render = function(tagsArray) {
    if (!tagsArray || tagsArray.length === 0) {
      return
    }

    let freeWidthSpace = columnSize
    let contentHtml = ''

    for (let i = 0; i < tagsArray.length; i++) {
      freeWidthSpace -= estimatedTagSize(tagsArray[i])
      if (freeWidthSpace >= 0) {
        contentHtml += tagRender(tagsArray[i])
      } else {
        contentHtml += buttonRender(tagsArray.slice(i))
        break
      }
    }
    return contentHtml
  }

  function parseColumnWidth(columWidth) {
    let size
    if (columWidth && columWidth.toString().indexOf('px') > 0) {
      size = parseInt(columWidth, 10)
    }
    return size || Number(columWidth + '') || 0
  }

  function estimatedTagSize(value) {
    return value.length * AVERAGE_CHAR_WIDTH + EMPTY_TAG_WIDTH
  }

  function tagRender(value) {
    return '<span class="column-tags-label">' + htmlEscape(value) + '</span>'
  }

  function buttonRender(tags) {
    let tagsContent = ''
    tags.forEach(function(tagValue) {
      tagsContent += tagRender(tagValue)
    })

    return '<button class="column-tags-tag column-tags-button" data-toggle="dropdown">&#x2b;' + tags.length + '</button>' +
                '   <div class="dropdown-menu fx-dropdown-open-to-left column-tags-popover">' + tagsContent + '</div>'
  }
}

saveRenderer({ key: 'na.basemodule.common.TagsColumnRender', renderer: TagsColumnRender })
