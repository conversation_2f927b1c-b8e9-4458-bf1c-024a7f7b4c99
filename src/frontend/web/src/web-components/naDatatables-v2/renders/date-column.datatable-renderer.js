import { saveRenderer } from '~components/naDatatables-v2/datatable-render-store'
import * as moment from 'moment'
import { converter, dateTimeFormats } from '~utils/time-deprecated'

function millisToDateFormat(millis, formatName) {
  return moment(millis).format(converter.jDFToMomentFormatString(dateTimeFormats[formatName].getPatternByLanguage()))
}

function dateInFormatRenderer(formatName) {
  return function (context) {
    const cellData = context.cellData
    if (cellData == null) {
      return '--'
    }
    const date = Number(cellData)
    return isNaN(date) ? '--' : millisToDateFormat(date, formatName)
  }
}

const renderDateColumn = dateInFormatRenderer('DATE_SHORT')
const renderTimeColumn = dateInFormatRenderer('TIME_SHORT')
const renderDateTimeColumn = dateInFormatRenderer('DATETIME_SHORT')
const renderDateTimeSecondsColumn = dateInFormatRenderer('DATETIME_SECONDS')
const renderDateTimeMillisecondsColumn = dateInFormatRenderer('DATETIME_MILLI_SECONDS')

saveRenderer({ key: 'na.datatableTemplates.basemodule.date', renderer: renderDateColumn })
saveRenderer({ key: 'na.datatableTemplates.basemodule.time', renderer: renderTimeColumn })
saveRenderer({ key: 'na.datatableTemplates.basemodule.datetime', renderer: renderDateTimeColumn })
saveRenderer({ key: 'na.datatableTemplates.basemodule.datetimeSeconds', renderer: renderDateTimeSecondsColumn })
saveRenderer({ key: 'na.datatableTemplates.basemodule.datetimeMilliseconds', renderer: renderDateTimeMillisecondsColumn })
