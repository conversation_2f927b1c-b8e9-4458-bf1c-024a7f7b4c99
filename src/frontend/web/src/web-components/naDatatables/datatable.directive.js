import { i18n } from '~utils/i18n'
import { providers } from '~basemodule/app/basemodule-providers'
import { hideNotificationOn, showNotificationOn } from '~utils/element-notification'
import { DatatableCatalog as datatableCatalog } from '~components/naDatatables/datatable-catalog.constant'
import { formatCell } from '~components/naDatatables/datatable-column-formatter.service'
import { getDatatable } from '~components/naDatatables/datatables.service'
import basemoduleAngularModule from '~basemodule/app/moduleDeclarations/basemodule.angular-module'
import { htmlEscape } from '~utils/string.util'

basemoduleAngularModule
  .directive(providers.datatables.directives.Datatable, [
    '$compile',
    DatatableDirective
  ])

function DatatableDirective($compile) {
  return {
    restrict: 'A',
    priority: 0,
    link: function (scope, element) {
      let isWaiting = false

      function showWaitingNotification() {
        if (!isWaiting) {
          isWaiting = true
          showNotificationOn(element).withMessage(i18n('datatables.info.label.loadingrecords'))
        }
      }

      function hideWaitingNotification() {
        hideNotificationOn(element)
        isWaiting = false
      }

      if (scope.datatableFeatures[datatableCatalog.features.serverSide]) {
        $(element).on('page.dt', showWaitingNotification)
          .on('length.dt', showWaitingNotification)
          .on('preXhr.dt', showWaitingNotification)
          .on('draw.dt', hideWaitingNotification)
        showWaitingNotification()
      }

      scope.$on('Datatables::Directive::ResetColumnTitle', function(event, params) {
        params.forEach(function(column) {
          element.find('thead').first('tr').find('th').each(function() {
            const _$this = $(this)
            if (_$this.attr('data-column-index') === column.index) {
              _$this.text(`<div class="fx-th-container"><span class="fx-th-label">${column.name}</span></div>`)
            }
          })
        })
      })

      if (typeof scope.dtOptions[datatableCatalog.features.expandableColumns] !== 'undefined') {
        scope.$on('Datatables::Directive::CheckColumnsSize', function(event, params) {
          const datatable = getDatatable(params.tableId)
          function processCell(value) {
            const $cell = $(value)
            const datatableCell = datatable.datatableApi.cell(value)
            let data = datatableCell.data()
            const columnIndex = datatableCell.index().column
            const column = scope.dtColumns[columnIndex]
            if (data == null) {
              data = column.defaultContent
            } else {
              data = formatCell(data, column)
            }
            $cell.attr('data-na-portal-expandable-column', '')
            $cell.attr('data-na-portal-table-tooltip', '')
            $cell.attr('data-table-id', datatable.id)
            $compile($cell)(scope)
            $cell.attr('data-toggle', 'tooltip')
            $cell.attr('title', data)
            $cell.attr('data-is-dirty', true)
            $cell.html('<span class="table-ellipsis">' + htmlEscape(data) + '</span>')
            needsAdjust = true
          }

          if (typeof params.excludedColumns === 'undefined') params.excludedColumns = []
          for (let i = 0; i < datatable.datatableApi.columns()[0].length; i++) {
            if (datatable.datatableApi.column(i).visible() && datatable.datatableApi.column(i).dataSrc() !== null && params.excludedColumns.indexOf(i) === -1) {
              var needsAdjust = false
              datatable.datatableApi.column(i).nodes().each(processCell)
              if (needsAdjust) {
                datatable.datatableApi.columns.adjust()
              }
            }
            if (params.excludedColumns.indexOf(i) !== -1) {
              const $header = $(datatable.datatableApi.column(i).header())
              $header.css('width', '1px')
              const addClass = function(value) {
                const $cell = $(value)
                $cell.css('white-space', 'nowrap')
              }
              datatable.datatableApi.column(i).nodes().each(addClass)
            }
          }
        })
      }
    }
  }
}

basemoduleAngularModule
  .directive(providers.datatables.directives.Datatable, function() {
    return {
      restrict: 'A',
      scope: {
        dtOptions: '=',
        dtColumns: '=',
        dtColumnDefs: '=',
        datatable: '@'
      },
      compile: function(tElm) {
        const _staticHTML = tElm[0].innerHTML
        return function postLink($scope, $elem, iAttrs, ctrl) {
          ctrl.render($elem, ctrl.buildOptionsPromise(), _staticHTML)
        }
      },
      controller: ['$scope', function($scope) {
        this.buildOptionsPromise = function() {
          const dtOptions = $scope.dtOptions
          const dtColumns = $scope.dtColumns
          const dtColumnDefs = $scope.dtColumnDefs

          let options
          if (angular.isDefined($scope.dtOptions)) {
            options = {}
            angular.extend(options, dtOptions)
            // Set the columns
            if (angular.isArray(dtColumns)) {
              options.aoColumns = dtColumns
            }

            // Set the column defs
            if (angular.isArray(dtColumnDefs)) {
              options.aoColumnDefs = dtColumnDefs
            }
          }
          return options
        }
        this.render = function($elem, options/*, staticHTML */) {
        // Render dataTable
          if (angular.isDefined(options)) {
            if ((angular.isDefined(options.sAjaxSource) && options.sAjaxSource !== null) ||
                              (angular.isDefined(options.ajax) && options.ajax !== null)) {
              this.ajaxRenderer(options, $elem)
            } else {
              this.defaultRenderer(options, $elem)
            }
          }
        }
        this.ajaxRenderer = function (options, $elem) {
          let oTable
          const _setOptionsAndRender = function(options, sAjaxSource, $elem, $scope) {
            if (angular.isDefined(sAjaxSource)) {
              options.sAjaxSource = sAjaxSource
              if (angular.isDefined(options.ajax)) {
                if (angular.isObject(options.ajax)) {
                  options.ajax.url = sAjaxSource
                } else {
                  options.ajax = { url: sAjaxSource }
                }
              }
            }
            _render(options, $elem, $scope)
          }
          const _render = function (options, $elem, $scope) {
          // Set it to true in order to be able to redraw the dataTable
            options.bDestroy = true
            // Condition to refresh the dataTable
            if (oTable) {
              const ajaxUrl = options.sAjaxSource || options.ajax.url || options.ajax
              oTable.ajax.url(ajaxUrl).load()
            } else {
              const dtId = '#' + $elem.attr('id')
              if ($.fn.dataTable.isDataTable(dtId)) {
                options.destroy = true
              }
              jQuery.extend(options, { fixedHeader: true })
              oTable = $elem.DataTable(options)
              // See http://datatables.net/manual/api#Accessing-the-API to understand the difference between DataTable and dataTable
              $scope.$emit('event:dataTableLoaded', { id: $elem.attr('id'), DataTable: oTable, dataTable: $elem.dataTable() })
            }

            $scope.$on('$destroy', () => {
              oTable.destroy()
            })
          }

          // Define default values in case it is an ajax datatables
          if (angular.isUndefined(options.sAjaxDataProp)) {
            options.sAjaxDataProp = ''
          }
          if (angular.isUndefined(options.aoColumns)) {
            options.aoColumns = []
          }
          _setOptionsAndRender(options, options.sAjaxSource, $elem, $scope)
        }

        this.defaultRenderer = function (options, $elem) {
          const dtId = '#' + $elem.attr('id')
          if ($.fn.dataTable.isDataTable(dtId)) {
            options.destroy = true
          }
          jQuery.extend(options, { fixedHeader: true })
          const oTable = $elem.DataTable(options)
          // See http://datatables.net/manual/api#Accessing-the-API to understand the difference between DataTable and dataTable
          $scope.$emit('event:dataTableLoaded', { id: $elem.attr('id'), DataTable: oTable, dataTable: $elem.dataTable() })

          $scope.$on('$destroy', () => {
            oTable.destroy()
          })
        }
      }]
    }
  })
