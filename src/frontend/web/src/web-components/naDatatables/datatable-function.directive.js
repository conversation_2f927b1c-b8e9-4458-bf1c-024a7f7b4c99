import { getDatatable } from '~components/naDatatables/datatables.service'
import { providers } from '~basemodule/app/basemodule-providers'
import basemoduleAngularModule from '~basemodule/app/moduleDeclarations/basemodule.angular-module'

basemoduleAngularModule
  .directive(providers.datatables.directives.DatatableFunction, [
    '$parse',
    DatatableFunctionDirective
  ])

function DatatableFunctionDirective($parse) {
  return {
    restrict: 'A',
    compile: function compile() {
      return {
        post: function postLink(scope, element, attributes) {
          scope.$on('$destroy', () => {
            // console.info('memory leak avoided!');
          })

          element.on('remove', () => {
            // datatables removes DOM elements internally, however their associated scopes will become zombies
            // in order to avoid memory leaks, and because this directive is compiled via $compile we should manually destroy the scope!
            // scope.$destroy();
          })

          element.on('click na.click', () => {
            const $row = element.closest('tr')
            const $cell = element.closest('td')
            const datatable = getDatatable(attributes.tableId)
            const jsonData = datatable.datatableApi.rows($row).data()[0]
            const rowId = datatable.datatableApi.row($row).index()
            const columnId = datatable.datatableApi.cell($cell).index().column
            const meta = { row: rowId, col: columnId }
            $parse(attributes.actionFunction)(scope)(jsonData, meta)
            scope.$apply()
          })
        }
      }
    }
  }
}
