export const DatatableCatalog = {
  /**
   * Features. The are related to the jquery-datatables API
   * autoWidth - Feature control DataTables' smart column width handling. Type: Boolean - Default: true
   * info - Feature control table information display field. Type: Boolean - Default: True
   * lengthChange - Feature control the end user's ability to change the paging display length of the table. Type: Boolean - Default: True
   * lengthMenu - Change the options in the page length select list. Type: Array of integers. Eg: [ 10, 25, 50, 75, 100 ]. -1 Disables
   * pageLength - Change the initial page length (number of rows per page). Type: Integer
   * ordering - Feature control ordering (sorting) abilities in DataTables. Type: Boolean - Default: true
   * paging - Enable or disable table pagination. Type: Boolean - Default: true
   * displayStart - Change the stating initial record cursor position. Type: Integer - Default: 0
   * processing - Feature control the processing indicator. Type: Boolean - Default: false
   * scrollX - Horizontal scrolling. Type: Boolean - Default Value: false
   * scrollY - Vertical scrolling. Type: String - Value: any CSS unit, or a number (in which case it will be treated as a pixel measurement)
   * searching - Feature control search (filtering) abilities. Type: Boolean - Default Value: true - API Methods: search()/column.search()
   * serverSide - Feature control DataTables' server-side processing mode. Type: Boolean  - Default Value: false
   * stateSave - State saving - restore table state on page reload. Type: Boolean - Default Value: false
   * stateDuration - Saved state validity duration. Type: Integer . This option is also used to indicate to DataTables if localStorage or sessionStorage should be used for storing the table's state. When set to -1 sessionStorage will be used, while for 0 or greater localStorage will be used.
   * deferLoading - Delay the loading of server-side data until second draw. Type:
   *                                                                                  - Integer: When given as an integer, this enables deferred loading, and instructs DataTables has to how many items are in the full data set.
   *                                                                                  - Array: As an array, this also enables deferred loading, while the first data index tells DataTables how many rows are in the filtered result set, and the second how many in the full data set without filtering applied.
   *                                                                                  - Boolean - Default value: false
   * dom - Define the table control elements to appear on the page and in what order. https://datatables.net/reference/option/dom
   *                                l - length changing input control
   *                                f - filtering input
   *                                t - The table!
   *                                i - Table information summary
   *                                p - pagination control
   *                                r - processing display element
   * orderClasses - Highlight the columns being ordered in the table's body. Type: Boolean - Default Value: true
   * order - Initial order (sort) to apply to the table. Type: Array of Arrays. Each array with two entries:     - Default Value: [[0, 'asc']]. Note: order[] - no initial order
   - Column index to order upon
   *                                                                                    - Direction so order to apply (asc or desc)
   * orderFixed - Ordering to always be applied to the table. https://datatables.net/reference/option/orderFixed for examples of usage
   * orderMulti - Multiple column ordering ability control. Type: Boolean - Default Value: true
   * pagingType - Set the pagination type of the datatables. Type: String - Values: ['two_buttons','full_numbers','full','simple','simple_numbers','input']
   * withColReorder - Add ColReorder compatibility. Type: Boolean - Default Value: false
   */
  features: {
    autoWidth: 'autoWidth',
    info: 'info',
    lengthChange: 'lengthChange',
    lengthMenu: 'lengthMenu',
    pageLength: 'pageLength',
    ordering: 'ordering',
    paging: 'paging',
    displayStart: 'displayStart',
    processing: 'processing',
    scrollX: 'scrollX',
    scrollY: 'scrollY',
    searching: 'searching',
    serverSide: 'serverSide',
    stateSave: 'stateSave',
    stateDuration: 'stateDuration',
    deferLoading: 'deferLoading',
    dom: 'sDom',
    orderClasses: 'orderClasses',
    order: 'order',
    orderFixed: 'orderFixed',
    orderMulti: 'orderMulti',
    pagingType: 'pagingType',
    withColReorder: 'withColReorder',
    data: 'data',
    expandableColumns: 'expandableColumns' // NA Custom property
  },

  /**
   * Features. The are related to row grouping plugin
   * TODO: documentar
   */
  rowGrouping: {
    groupingColumnIndex: 'iGroupingColumnIndex',
    groupBy: 'sGroupBy',
    groupingColumnSortDirection: 'sGroupingColumnSortDirection',
    expandableGrouping: 'bExpandableGrouping'
  },

  /**
   * url - Source to get the data from. Type: String . Directly related
   * dataProp - By default DataTables will look for the property aaData when obtaining data from an Ajax source or for server-side processing - this parameter allows that property to be changed. E.g: .withPropData('data') || .withPropData('data.myinnerdata')
   * withPaginationType -
   * withLanguage - Set the language of the datatables. If not defined, it uses the default language set by datables, ie english. Currently i'm using the Play's message internationalization module to set the datatables messages.
   * withDisplayLength - Set the number of items per page to display. Type: Integer
   */
  ajaxInfo: {
    url: 'url',
    type: 'type',
    dataProp: 'dataProp'
  },
  /**
   * className - Class to assign to each cell in the column. Type: String
   * data - Set the data source for the column from the rows data object / array. Type: Integer,String,null,Object
   * defaultContent - Set default, static, content for a column. Type: String
   * name - Set a descriptive name for a column. Type: String
   * orderable - Enable or disable ordering on this column. Type: Boolean
   * orderData - Define multiple column ordering as the default order for a column. Type: Integer, Array. A single/multiple column index to order upon.
   * orderSequence - Order direction application sequence. Type: Array - Default Value: ['asc','desc'].
   * render - Render (process) the data for use in the table. Types:
   *                                                              - Integer - Treated as an array index for the data source. This is the default that DataTables uses (incrementally increased for each column).
   *                                                              - String - Read an object property from the data source.
   *                                                              - Function - function render( data, type, row, meta ) If a function is given, it will be executed whenever DataTables needs to get the data for a cell in the column. Note that this function might be called multiple times, as DataTables will call it for the different data types that it needs - sorting, filtering and display.
   * searchable - Enable or disable filtering on the data in this column. Type: Boolean - Default Value: true.
   * title - Set the column title. Type: String - Default Value: Value read from the column's header cell.
   * type - Set the column type - used for filtering and sorting string processing. Type: String - Default Value: Auto detected from raw data. Possible Types: date, num, num-fmt, html-num, string
   * visible - Enable or disable the display of this column. Type: Boolean - Default Value: true
   * width - Column width assignment. Type: String - any CSS value (3em, 20px etc).
   *
   */
  columns: {
    className: 'className',
    data: 'data',
    defaultContent: 'defaultContent',
    name: 'name',
    orderable: 'orderable',
    orderData: 'orderData',
    orderSequence: 'orderSequence',
    searchable: 'searchable',
    title: 'sTitle',
    type: 'type',
    dateFormat: 'dateFormat',
    visible: 'visible',
    width: 'width',
    actions: 'actions', // custom option in order to process column actions
    createdCell: 'createdCell',
    render: 'render',
    showInTree: 'showInTree'
  },
  actions: {
    actionName: 'actionName',
    actionUrl: 'actionUrl',
    actionIcon: 'actionIcon',
    actionFunction: 'actionFunction',
    actionEvent: 'actionEvent',
    additionalClasses: 'additionalClasses',
    title: 'title',
    iconClasses: 'iconClasses',
    render: 'render'
  },
  toolbar: {
    rightActions: {
      detailedExport:
        {
          elementId: 'export',
          htmlElementType: 'button',
          icon: 'glyphicon glyphicon-export',
          htmlClass: 'btn btn-default btn-xs dropdown-toggle fx-dropdown-toggle',
          elementLabel: '',
          title: 'export',
          type: 'detailedExport',
          exportLimit: 1000, // TODO: Verificar que este valor vem do ficheiro de configuração ou possivelmente de uma variavel que se estiver definida faz override a este valor default
          action: [
            {
              action: 'exportPDF',
              label: 'exportPDF',
              tooltip: 'exportPDF',
              type: 'PDF',
              // TODO: validar url
              targetUrl: '', // jshint ignore:line
              event: 'emitExport',
              htmlElementType: 'radio'
            },
            {
              action: 'exportXLS',
              label: 'exportXLS',
              tooltip: 'exportXLS',
              type: 'XLS',
              targetUrl: '', // jshint ignore:line
              event: 'emitExport',
              htmlElementType: 'radio'
            },
            {
              action: 'exportCSV',
              label: 'exportCSV',
              tooltip: 'exportCSV',
              type: 'CSV',
              targetUrl: '', // jshint ignore:line
              event: 'emitExport',
              htmlElementType: 'radio'
            }
          ]
        },
      columnFilter: {
        elementId: 'columnfilter',
        htmlElementType: 'button',
        icon: 'fuxicons fuxicons-manage-columns',
        htmlClass: 'fx-dropdown-toggle btn btn-default btn-xs dropdown-toggle table-column-filter',
        elementLabel: '',
        title: 'columnfilter',
        type: 'columnsConf'
      },

      tableView: {
        elementId: 'tableView',
        htmlElementType: 'button',
        icon: 'fa fa-table',
        htmlClass: 'btn btn-default btn-xs',
        elementLabel: '',
        title: 'rowfiltering',
        type: 'rowfiltering'
      },

      codeView: {
        elementId: 'codeView',
        htmlElementType: 'button',
        icon: 'fa fa-code',
        htmlClass: 'btn btn-default btn-xs',
        elementLabel: '',
        title: 'rowfiltering',
        type: 'rowfiltering'
      },

      advancedSearch: {
        elementId: 'rowfilter',
        htmlElementType: 'button',
        icon: 'glyphicon glyphicon-search',
        htmlClass: 'btn btn-default btn-xs btn-advanced-search collapsed',
        elementLabel: '',
        title: 'advancedsearch',
        type: 'rowfiltering',
        targetElement: 'table-advanced-search'
      },
      button: { // Empty values should be overwritten
        elementId: 'tableToolbarButton', // Override
        htmlElementType: 'button',
        icon: '',
        htmlClass: '',
        elementLabel: '',
        title: '',
        messagesObjectName: '',
        extraAttributes: '',
        action: [
          {
            onClick: '', // needs to be on custom controller scope! The same as the actions. If defined type|action|event|targetUrl are not used
            action: '',
            label: '',
            tooltip: '',
            type: '',
            targetUrl: '',
            event: ''
          }
        ]
      }
    }
  }
}
