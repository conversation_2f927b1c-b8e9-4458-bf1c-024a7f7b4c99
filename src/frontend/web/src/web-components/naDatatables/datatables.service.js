import { request } from '~utils/proxy'
import { routes } from '~basemodule/app/src/common/routes'

const datatables = []
let requestDone = false
let applicationExportLimit = 5000 // default value in case config initialization fails

export const generateInitialTreeInfoFromJSONData = function (data) {
  const result = []
  let tempObj
  data.forEach(function (value) {
    tempObj = {
      index: value.id,
      checked: !((value.state && value.state.selected === false))
    }
    result.push(tempObj)
  })
  return result
}

export const setNewDataTable = function (id, DataTable, dataTable, hiddenColumnsToShowInTree) {
  let exist = false
  datatables.forEach(function (value) {
    if (id === value.id) {
      value.datatableApi = DataTable
      value.jQueryDatatable = dataTable
      if (hiddenColumnsToShowInTree) {
        value.showInTree = hiddenColumnsToShowInTree
      }
      exist = true
    }
  })
  if (!exist) {
    datatables.push({
      id,
      datatableApi: DataTable,
      jQueryDatatable: dataTable,
      showInTree: hiddenColumnsToShowInTree || undefined
    })
  }
}

export const getDatatable = function (id) {
  let i
  for (i = 0; i < datatables.length; i++) {
    if (id === datatables[i].id) {
      return datatables[i]
    }
  }
  return undefined
}

export const getResquestDone = function() {
  return requestDone
}

export const setRequestDone = function(status) {
  requestDone = status
}

export const loadApplicationExportLimit = function() {
  const route = routes.na.naportalbase.controllers.Application.getApplicationExportLimit()
  request({
    route,
    data: {},
    onSuccess: data => {
      applicationExportLimit = data
    },
    onError: () => {
      console.error('error getting application export limit. Using default value!')
    }
  })
}

export const getDatatableList = () => datatables

export const getApplicationExportLimit = function() {
  return applicationExportLimit
}
