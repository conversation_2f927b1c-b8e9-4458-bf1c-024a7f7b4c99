import moment from 'moment'
import { dateTimeService } from '~utils/time-deprecated'
import { DatatableCatalog as datatableCatalog } from '~components/naDatatables/datatable-catalog.constant'

export function formatDateCell(cellData, dateFormat) {
  if (dateFormat == null) {
    dateFormat = dateTimeService.getDateFormat(dateTimeService.dateTimeFormats.DATETIME_SECONDS)
  }
  let date = cellData
  if (date != null) {
    const momentDate = moment(Number(date))
    if (momentDate.isValid()) {
      date = momentDate.format(dateFormat)
    }
  } else {
    date = ''
  }
  return date
}

export function formatCell(cellData, columnConfig) {
  if (columnConfig[datatableCatalog.columns.type] === 'date') {
    return formatDateCell(cellData, columnConfig[datatableCatalog.columns.dateFormat])
  }
  return cellData
}
