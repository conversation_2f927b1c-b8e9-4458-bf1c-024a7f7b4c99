import { hideNotificationOn, showNotificationOn } from '~utils/element-notification'
import { DatatableCatalog as datatableCatalog } from '~components/naDatatables/datatable-catalog.constant'
import { i18n } from '~utils/i18n'
import { parseError } from '~utils/proxy'
import { formatCell } from '~components/naDatatables/datatable-column-formatter.service'
import { setNewDataTable } from '~components/naDatatables/datatables.service'
import basemoduleAngularModule from '~basemodule/app/moduleDeclarations/basemodule.angular-module'
import { providers } from '~basemodule/app/basemodule-providers'

basemoduleAngularModule
  .controller(providers.datatables.controllers.Datatable, [
    '$rootScope',
    '$scope',
    '$compile',
    '$timeout',
    function($rootScope, $scope, $compile, $timeout) {
      const ctrl = this
      ctrl.cleanLoadingNotification = false
      ctrl.paramsPost = undefined
      ctrl.dtOptions = {}
      ctrl.dtColumns = []
      ctrl.datatable = {}
      ctrl.initialDatatableData = {}

      // Get data from the custom controller
      ctrl.datatableFeatures = angular.copy($scope.datatableFeatures)
      ctrl.rowGroupingFeatures = angular.copy($scope.rowGroupingFeatures)
      ctrl.datatableAjaxInfo = angular.copy($scope.datatableAjaxInfo)
      ctrl.tableColumns = angular.copy($scope.datatableColumns)
      ctrl.paramsPost = angular.copy($scope.paramsPost)

      // set i18n
      ctrl.datatableFeatures.language = tablei18n()

      let url, type, dataProp
      let colReorder = false

      if (typeof ctrl.datatableAjaxInfo !== 'undefined') {
        url = ctrl.datatableAjaxInfo[datatableCatalog.ajaxInfo.url]
        type = ctrl.datatableAjaxInfo[datatableCatalog.ajaxInfo.type]
        dataProp = ctrl.datatableAjaxInfo[datatableCatalog.ajaxInfo.dataProp]
      }

      if (ctrl.datatableFeatures[datatableCatalog.features.withColReorder]) {
        colReorder = true
      }

      ctrl.datatableFeatures = defineCustomValues(ctrl.datatableFeatures, colReorder)
      $scope.dtOptions = angular.extend(ctrl.datatableFeatures, setDatatablesCallbacks(url, type, dataProp))
      $scope.dtColumns = defineColumns(ctrl.tableColumns)
      $scope.isTableExpanded = false

      $scope.$on('Datatables::Controller::TableExpanded', function(event, params) {
        $scope.isTableExpanded = params
      })

      $scope.$on('Datatables::Controller::SetDataToSend', function(event, params) {
        ctrl.paramsPost = params
      })

      $scope.$on('Datatables::Controller::UpdateColumns', function(event, params) {
        params.forEach(function(element) {
          $scope.datatable.datatableApi.column(element.index).visible(true)
          $($scope.datatable.datatableApi.column(element.index).header()).text(element.name)
          $scope.datatable.datatableApi.settings()[0].aoColumns[element.index].sTitle = element.name
        })
      })

      $scope.$on('Datatables::Controller::HideColumns', function(event, params) {
        params.forEach(function(element) {
          $($scope.datatable.datatableApi.column(element.index).header()).text(element.name)
          $scope.datatable.datatableApi.settings()[0].aoColumns[element.index].sTitle = element.name
          $scope.datatable.datatableApi.column(element.index).visible(false)
        })
      })

      $scope.$on('Datatables::Controller::SetCleanLoadingNotification', function(event, params) {
        ctrl.cleanLoadingNotification = params
      })

      $scope.$on('Datatables::Controller::SetInitialData', function(event, params) {
        addInitialData(params)
      })

      $scope.$on('Datatables::Controller::AddRows', function(event, params) {
        addInitialData(params)
      })

      // External API specific event
      $scope.$on('event:dataTableLoaded', function(event, loadedDT) {
        if (ctrl.rowGroupingFeatures) {
          loadedDT.DataTable.rowGroup(ctrl.rowGroupingFeatures)
        }

        ctrl.datatable.id = loadedDT.id
        ctrl.datatable.datatableApi = loadedDT.DataTable
        ctrl.datatable.jQueryDatatable = loadedDT.dataTable
        setNewDataTable(loadedDT.id, loadedDT.DataTable, loadedDT.dataTable, angular.isDefined($scope.showInTree) ? $scope.showInTree : undefined)
        $scope.$emit('CustomDatatables::Controller::SetNewDatatable', ctrl.datatable)
      })

      $scope.$on('Datatables::Controller::ReloadDatatable', function (event, params) {
        ctrl.cleanLoadingNotification = true
        showNotificationOn(ctrl.datatable.jQueryDatatable).withMessage(i18n('datatables.info.label.loadingrecords'))

        if (typeof params !== 'undefined') {
          if (typeof params.paramsPost !== 'undefined') {
            ctrl.paramsPost = params.paramsPost
          }

          ctrl.cleanLoadingNotification = true
          showNotificationOn(ctrl.datatable.jQueryDatatable).withMessage(i18n('datatables.info.label.loadingrecords'))
          let callback = params.onDone
          if (typeof callback !== 'function') {
            callback = null
          }
          const resetPaging = params.resetPaging
          ctrl.datatable.datatableApi.ajax.reload(callback, resetPaging)
        } else {
          ctrl.datatable.datatableApi.ajax.reload()
        }
      })

      /**
       * CALLBACKS.
       * Currently im not letting any1 override the callback functions!
       * TODO: Possibilitar override das funções default
       *
       * function createdRow( row, data, dataIndex )
       * function drawCallback( settings ) executes on every draw() ! Use API method in the callback to get the data for the rows in the draw: - var api = this.api(); console.log( api.rows( {page:'current'} ).data() );
       * function footerCallback( tfoot, data, start, end, display ) . Identical to headerCallbackDT but for the table footer this function allows you to modify the table footer on every 'draw' event.
       * function infoCallback( settings, start, end, max, total, pre ) . Returns: stringJS The string to be displayed in the information element.
       * function initComplete( settings, json )
       * function preDrawCallback( settings )
       * function rowCallback( row, data )
       */
      function setDatatablesCallbacks(url, type, dataProp) {
        const dtOptions = {}

        dtOptions.drawCallback = function (settings) {
          if (ctrl.cleanLoadingNotification === true) {
            ctrl.cleanLoadingNotification = false
            hideNotificationOn(ctrl.datatable.jQueryDatatable)
          }
          $scope.$emit('CustomDatatables::Controller::TableComplete', {
            datatable: {
              id: settings.sTableId
            }
          })
        }

        dtOptions.fnInfoCallback = function(oSettings, iStart, iEnd, iMax, iTotal) {
          const tableInstance = oSettings.oInstance
          let showPageLengthEntries = false

          if (iTotal > 0) {
            const hasPaginationWithOtherPageLength = Math.min.apply(Math, oSettings.aLengthMenu) < iTotal
            const hasPagination = oSettings._iDisplayLength < iTotal
            showPageLengthEntries = hasPagination || hasPaginationWithOtherPageLength
          }

          $('.dataTables_length', $(tableInstance).parent()).toggle(showPageLengthEntries)

          return iTotal > 0 ? i18n('datatables.info.label.entriestoshow').replace('_TOTAL_', iTotal) : ''
        }

        dtOptions.initComplete = function (/* settings, json */) {}
        dtOptions.createdRow = function (row, data/*, dataIndex */) {
          if (data.dt_RowId) {
            $(row).attr('data-na-portal-add-row-loading-css', '')
            $timeout(function() {
              $(row).attr('id', data.dt_RowId)
            })
          }
        }
        dtOptions.footerCallback = function (/* tfoot, data, start, end, display */) {}
        dtOptions.headerCallback = function (/* theader, data, start, end, display */) {}
        dtOptions.rowCallback = function (row/*, data */) {
        // Unbind first in order to avoid any duplicate handler
          $('td a', row).off()
          $compile($(row))($scope)
        }
        dtOptions.oColReorder = {
          fnReorderCallback: function() {
            $scope.datatable.datatableApi.rows().nodes().each(function(row) {
              $compile($(row))($scope)
            })
          }
        }

        // Ajax data property
        dtOptions.sAjaxDataProp = dataProp

        if (typeof ctrl.datatableAjaxInfo !== 'undefined') {
          dtOptions.ajax = function (data, callback) {
            let serverInfo
            if (type === 'POST') {
              let paramsPost = ctrl.paramsPost
              if (paramsPost != null && typeof paramsPost.dataToSubmit !== 'undefined') {
                paramsPost = paramsPost.dataToSubmit()
              }
              serverInfo = { tableData: data, paramsPost }
            } else {
              serverInfo = { tableData: data }
            }
            $scope.$emit('Datatables::Controller::BeforeAjax', {
              data: serverInfo,
              setData: function setData(data) {
                serverInfo = data
              }
            })

            serverInfo = JSON.stringify(serverInfo)

            $.ajax({
              dataType: 'json',
              contentType: 'application/json; charset=utf-8',
              type,
              url,
              data: serverInfo,
              success: function (serverData) {
                $rootScope.$broadcast('Datatables::Controller::RequestComplete')
                $scope.$emit('Datatables::Controller::PreprocessingResponse', {
                  process: function(callback) {
                    serverData = callback(serverData, data) || serverData
                  }
                })
                callback(serverData)
              },
              error: function (error) {
                parseError(error, () => {
                  if (ctrl.cleanLoadingNotification === true) {
                    ctrl.cleanLoadingNotification = false
                    hideNotificationOn(ctrl.datatable.jQueryDatatable)
                  }

                  callback({
                    data: [],
                    recordsTotal: 0,
                    recordsFiltered: 0
                  })
                })
              }
            })
          }
        }
        return dtOptions
      }

      // Build Columns
      function defineColumns(columns) {
        const datatableColumns = []

        columns.forEach(function(value) {
          const column = value
          column.mData = column[datatableCatalog.columns.data]

          for (const property in value) {
            if (value[property] !== null) {
              if (property === datatableCatalog.columns.type && column[datatableCatalog.columns.type] === 'date') {
                defineColumnDate(column)
              } else if (property === datatableCatalog.columns.actions && typeof column[datatableCatalog.columns.render] === 'undefined') {
                defineColumnActions(column, value[property])
              }
            }
          }
          datatableColumns.push(column)
        })
        return datatableColumns
      }

      function defineColumnDate(column) {
        column.mRender = function(data) {
          return formatCell(data, column)
        }
      }

      // Column Actions
      function defineColumnActions(column, actions) {
        const actionFunctions = function (action, data/*, jsonData, meta */) {
          if (data.actions !== false) {
            return [
              "<a class='btn fx-btn-action ", action[datatableCatalog.actions.additionalClasses], "' ",
              "data-na-portal-datatable-function data-action-function='" + action[datatableCatalog.actions.actionFunction],
              "' data-toggle='tooltip' data-table-id='" + ctrl.datatable.id,
              "'title='", i18n(action[datatableCatalog.actions.title]), "'>",
              "<i class='", action[datatableCatalog.actions.iconClasses], "'></i>",
              '</a>'
            ].join('')
          } else {
            return '-'
          }
        }

        column.mRender = function(data, type, full/*, meta */) {
          let actionsHtml = ''
          // For every action
          const jsonData = JSON.stringify(full)
          actions.forEach(function(action) {
            actionsHtml += actionFunctions(action, data, jsonData)
          })

          return actionsHtml
        }
      }

      function tablei18n() {
        const oLanguage = []

        const oPaginate = {}
        oPaginate.first = i18n('datatables.navigation.label.first')
        oPaginate.last = i18n('datatables.navigation.label.last')
        oPaginate.next = i18n('datatables.navigation.label.next')
        oPaginate.previous = i18n('datatables.navigation.label.previous')

        oLanguage.paginate = oPaginate
        oLanguage.emptyTable = i18n('datatables.info.label.emptytable')
        oLanguage.infoFiltered = i18n('datatables.info.label.infofiltered')
        oLanguage.infoPostFix = i18n('datatables.info.label.infopostfix')
        oLanguage.thousands = i18n('datatables.info.label.infothousands')
        oLanguage.lengthMenu = `_MENU_ <span>${i18n('datatables.info.label.lengthmenu')}</span>`
        oLanguage.loadingRecords = i18n('datatables.info.label.loadingrecords')
        oLanguage.processing = i18n('datatables.info.label.processing')
        oLanguage.search = i18n('datatables.info.label.search')
        oLanguage.zeroRecords = i18n('datatables.info.label.zerorecords')
        oLanguage.aria = {
          sortAscending: 'asc',
          sortDescending: 'desc'
        }

        return oLanguage
      }

      function defineCustomValues(tableFeatures, colReorder) {
        const defaultsDomVal = 't<"dataTable-table-info"l><"dataTable-pagination-wrapper"ip>'

        if (!tableFeatures[datatableCatalog.features.dom] || tableFeatures[datatableCatalog.features.dom] === '') {
          tableFeatures.hasOverrideDom = true
        }

        if (colReorder) {
          const colReorderPrefix = 'R'
          tableFeatures[datatableCatalog.features.dom] = colReorderPrefix + (!tableFeatures[datatableCatalog.features.dom] ? defaultsDomVal : tableFeatures[datatableCatalog.features.dom])
          tableFeatures.hasColReorder = true
        }

        return tableFeatures
      }

      function addInitialData(data) {
        ctrl.datatable.datatableApi.rows.add(data).draw()
      }
    }])
