import { providers } from '~basemodule/app/basemodule-providers'
import { getDatatable } from '~components/naDatatables/datatables.service'
import { formatCell } from '~components/naDatatables/datatable-column-formatter.service'
import basemoduleAngularModule from '~basemodule/app/moduleDeclarations/basemodule.angular-module'

basemoduleAngularModule
  .directive(providers.datatables.directives.ExpandableColumn, [
    ExpandableColumnDirective
  ])

function ExpandableColumnDirective() {
  return {
    restrict: 'A',
    priority: 0,
    // scope: true,
    link: (scope, element, attrs) => {
      const datatable = getDatatable(attrs.tableId)

      function isTextSelected() {
        const selectedText = window.getSelection().toString()
        return selectedText != null && selectedText !== ''
      }

      function toggleExpand(cellElement) {
        const columnId = datatable.datatableApi.cell(cellElement).index().column
        let needsRedraw = false
        datatable.datatableApi.column(columnId).nodes().each(value => {
          const $cell = $(value)
          const datatableCell = datatable.datatableApi.cell(value)
          let data = datatableCell.data()
          const columnIndex = datatableCell.index().column
          const column = scope.dtColumns[columnIndex]
          if (data == null) {
            data = column.defaultContent
          } else {
            data = formatCell(data, column)
          }
          if (scope.isTableExpanded === false) { // Note: This scope.isTableExpanded comes from the parent controller -> naDatatablesController
            if ($cell.attr('data-is-dirty')) {
              const $html = $($cell.find('span'))
              $html.removeClass('table-ellipsis')
              $html.addClass('table-ellipsis-closed')
              needsRedraw = true
            }
          } else {
            if ($cell.attr('data-is-dirty')) {
              $cell.html('<span class="table-ellipsis">' + data + '</span>')
            }
          }
        })
        scope.$emit('Datatables::Controller::TableExpanded', needsRedraw)
        datatable.datatableApi.columns.adjust()
      }

      element.off('click')
      element.on('click', function() {
        if (!isTextSelected()) {
          toggleExpand(this)
        }
      })
    }
  }
}
