import { providers } from '~basemodule/app/basemodule-providers'
import basemoduleAngularModule from '~basemodule/app/moduleDeclarations/basemodule.angular-module'

basemoduleAngularModule
  .directive(providers.datatables.directives.AddRowLoadingCss, [
    '$timeout',
    AddRowLoadingCssDirective
  ])

function AddRowLoadingCssDirective($timeout) {
  return {
    restrict: 'A',
    link: function (scope, element) {
      let timer
      const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            $timeout.cancel(timer)
            element.find('.rowLoading').css('position', 'absolute')
          }
          if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
            timer = $timeout(function () {
              element.find('.rowLoading').remove()
            }, 500)
          }
        })
      })
      scope.$on('Datatables::Directive::AddLoadingRowCss', function (event, params) {
        if (params.rowId.toString() !== element[0].id) {
          return
        }
        if (element.find('.rowLoading').size() <= 0) {
          const rowHeight = element.height()
          const rowLoadingElem = $('<div class="rowLoading"></div>')
          rowLoadingElem.css('height', rowHeight)
          rowLoadingElem.css('position', 'absolute')
          rowLoadingElem.css('left', '0')
          rowLoadingElem.css('width', '100%')
          rowLoadingElem.css('pointer-events', 'none')
          rowLoadingElem.appendTo(element)
          observer.observe(rowLoadingElem[0], {
            childList: true
          })
        }
      })
    }
  }
}
