/**
   *
   *  DATATABLES RIGHT SIDE OF THE TOOLBAR
   *
   */
import { i18n } from '~utils/i18n'
import { modules, providers } from '~basemodule/app/basemodule-providers'

angular.module(modules.datatables).controller(providers.datatables.controllers.ToolbarRightAction, [
  '$scope',
  ToolbarRightActionController
])

function ToolbarRightActionController($scope) {
  const ctrl = this

  // deriva do controlador customizado de cada tabela
  ctrl.components = angular.copy($scope.toolBar.rightActions)

  ctrl.components.forEach(function (val) {
    val.title = i18n('datatables.action.label.' + val.title)

    if (val.action) {
      val.action.forEach(function (action) {
        action.labeli18n = i18n('datatables.action.label.' + action.label)
      })
    }
  })
  ctrl.searchPlaceholder = i18n('datatables.columnFilter.search.placeholder')
  ctrl.i18nApply = i18n('na.button.apply')
  ctrl.i18nCancel = i18n('na.button.label.cancel')

  ctrl.applyChanges = function () {} // overwritten in directive
  ctrl.cancelChanges = function () {} // overwritten in directive
}
