import { existsTree, storeNewTree, updateTree, getTree } from '~components/jstree/js-tree.service'
import { getDatatable, generateInitialTreeInfoFromJSONData, getDatatableList } from '~components/naDatatables/datatables.service'
import { providers } from '~basemodule/app/basemodule-providers'
import basemoduleAngularModule from '~basemodule/app/moduleDeclarations/basemodule.angular-module'
import { providerName as JsTreeDirectiveName } from '~components/jstree/js-tree.directive'

basemoduleAngularModule
  .directive(providers.datatables.directives.ToolbarTreeColumnFilter, [
    ToolbarTreeColumnFilterDirective
  ])

function ToolbarTreeColumnFilterDirective() {
  return {
    restrict: 'A',
    require: [JsTreeDirectiveName, 'ngController'],
    scope: true,
    priority: 1,
    link: function (scope, element, attrs, ctrls) {
      ctrls[0].treeAfterLoad = function (tree) {
        tree.jstree('check_all')
      }

      const jsTCtrl = ctrls[0]
      const selfCtrl = ctrls[1]

      selfCtrl.datatableId = attrs.tableId
      const datatable = getDatatable(selfCtrl.datatableId)
      selfCtrl.datatableApi = datatable.datatableApi
      selfCtrl.jqueryDatatable = datatable.jQueryDatatable
      selfCtrl.showInTree = datatable.showInTree
      selfCtrl.loadTree = function (columns) {
        if (!existsTree(element.attr('id'))) {
          selfCtrl.updateData(columns, selfCtrl.showInTree)
          const additionalPlugins = ['checkbox', 'search', 'adv_search']
          const additionalConfs = []
          let tempObj = {
            key: 'search',
            value: {
              show_only_matches: true
            }
          }
          additionalConfs.push(tempObj)
          tempObj = {
            key: 'themes',
            value: {
              theme: 'fuxi',
              icons: false
            }
          }
          additionalConfs.push(tempObj)
          const tree = jsTCtrl.callInitializeJsTreeWithJSON(element, selfCtrl.columnsData, additionalPlugins, additionalConfs)
          storeNewTree(attrs.id, tree, generateInitialTreeInfoFromJSONData(selfCtrl.columnsData))
          tree.on('changed.jstree', function handler(event, data) {
            const modelData = data.instance._model.data
            const newTreeInfo = Object.keys(modelData)
              .map(function(id) { return modelData[id] })
              .filter(function(obj) { return obj.parent != null })
              .map(function(obj) { return { index: obj.id, checked: obj.state.selected } })
            updateTree(attrs.id, tree, newTreeInfo)
            tree.off('changed.jstree', handler)
          })
        }
      }

      scope.$on('Toolbar::Directive::UpdateTree', function() {
        selfCtrl.updateData(selfCtrl.datatableApi.settings()[0].aoColumns)
        const tree = getTree(selfCtrl.datatableId + '_columnFilterTree')
        tree.tree.jstree(true).settings.core.data = selfCtrl.columnsData
        tree.tree.jstree(true).refresh(true, true)
        setTimeout(function() { tree.tree.jstree(true).check_all() }, 1000)
        const treeData = generateInitialTreeInfoFromJSONData(selfCtrl.columnsData)
        updateTree(selfCtrl.datatableId + '_columnFilterTree', tree.tree, treeData)
      })

      if (selfCtrl.datatableApi) {
        selfCtrl.loadTree(selfCtrl.datatableApi.settings()[0].aoColumns)
      } else {
        scope.$watchCollection(function () { return getDatatableList() }, function (newValue, oldValue) {
          if (newValue !== oldValue) {
            const datatableApi = getDatatable(selfCtrl.datatableId).datatableApi
            if (datatableApi) {
              selfCtrl.loadTree(datatableApi.settings()[0].aoColumns)
            }
          }
        })
      }
    }
  }
}
