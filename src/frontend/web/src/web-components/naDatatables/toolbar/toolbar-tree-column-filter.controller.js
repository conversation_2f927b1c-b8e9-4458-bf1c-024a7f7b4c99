import { modules, providers } from '~basemodule/app/basemodule-providers'

angular.module(modules.datatables).controller(providers.datatables.controllers.ToolbarTreeColumnFilter, [
  ToolbarTreeColumnFilterController
])

function ToolbarTreeColumnFilterController() {
  const ctrl = this

  ctrl.updateData = function (columns, showInTree) {
    let tempObj,
      j
    ctrl.columnsData = []
    for (j = 0; j < columns.length; j++) {
      if (columns[j].mData !== null && columns[j].mData !== 'checkbox' && columns[j].bVisible === true) {
        tempObj = {}
        tempObj.text = columns[j].sTitle
        tempObj.id = j
        tempObj.data = {
          columnId: j
        }
        tempObj.state = {
          selected: true
        }
        tempObj.a_attr = {
          'data-column-class': columns[j].className
        }
        ctrl.columnsData.push(tempObj)
      } else if (columns[j].bVisible === false && showInTree) {
        if (showInTree.indexOf(columns[j].sTitle) !== -1) {
          tempObj = {}
          tempObj.text = columns[j].sTitle
          tempObj.id = j
          tempObj.state = {
            selected: false
          }
          tempObj.data = {
            columnId: j
          }
          tempObj.a_attr = {
            'data-column-class': columns[j].className
          }
          ctrl.columnsData.push(tempObj)
        }
      }
    }
    return ctrl.columnsData
  }
}
