import { i18n } from '~utils/i18n'
import { modules, providers } from '~basemodule/app/basemodule-providers'

angular.module(modules.datatables).controller(providers.datatables.controllers.ToolbarAdvancedSearch, [
  '$scope',
  ToolbarAdvancedSearchController
])

function ToolbarAdvancedSearchController($scope) {
  const ctrl = this
  ctrl.toolBar = $scope.toolBar // Vem do controlador customizado para cada tabela
  ctrl.datatable = undefined
  ctrl.datatableApi = undefined
  ctrl.jqueryDatatable = undefined
  ctrl.searchField = ''
  ctrl.i18nTitle = i18n('datatables.action.label.advancedsearch')
  ctrl.i18nPlaceHolder = i18n('datatables.action.label.search')
  ctrl.i18nHelpContent = i18n('datatables.search.advancedsearch.minimum.length')
  ctrl.advancedSearch = function () {} // overwritten in directive
  ctrl.onChange = function() {} // overwritten in directive
}
