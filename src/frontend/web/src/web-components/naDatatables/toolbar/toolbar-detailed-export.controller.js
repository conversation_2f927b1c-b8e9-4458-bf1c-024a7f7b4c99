import { i18n } from '~utils/i18n'
import { modules, providers } from '~basemodule/app/basemodule-providers'

angular.module(modules.datatables).controller(providers.datatables.controllers.ToolbarDetailedExport, [
  ToolbarDetailedExportController
])

function ToolbarDetailedExportController() {
  const ctrl = this

  ctrl.tableId = ''
  ctrl.totalRecords = 0
  ctrl.selectedFormat = 'PDF'
  ctrl.infoToExport = 'one'
  ctrl.sendExportByEmail = false

  ctrl.i18nExport = i18n('na.button.label.export')
  ctrl.i18nAllPages = i18n('datatables.info.export.label.allpages')
  ctrl.i18nCurrentPage = i18n('datatables.info.export.label.currentpage')
  ctrl.i18nResults = i18n('na.general.results')
  ctrl.i18nExportingResults = i18n('datatables.info.export.label.info')
  ctrl.i18nExportingFormats = i18n('datatables.info.export.label.types')
  ctrl.i18nExportToMail = i18n('datatables.info.export.bymail')
  ctrl.i18nCancel = i18n('na.button.label.cancel')

  ctrl.exportData = function() {} // overwritten in directive
  ctrl.setTableTotalRecords = function() {} // overwritten in directive
}
