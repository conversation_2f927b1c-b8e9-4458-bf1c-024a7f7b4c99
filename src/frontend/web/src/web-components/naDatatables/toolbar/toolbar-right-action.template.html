<div class="btn-group" data-ng-repeat-start="component in toolbarCtrl.components" data-ng-if="!component.type || ( component.type && component.type == 'list')">
    <button title="{{component.title}}" role="{{component.htmlElementType}}" class="{{component.htmlClass}}" type="button"
            data-toggle="dropdown"
            id="{{toolbarCtrl.datatableId}}_rightaction_{{component.action[0].label}}"
            data-na-portal-tool-bar-action>
        <i class="{{component.icon}}">
        </i>
        <span class="caret">
        </span>
    </button>
    <ul class="dropdown-menu fx-dropdown-open-to-left">
        <li ng-repeat="elem in component.action">
            <a href="javascript:void(0)" role="{{component.htmlElementType}}"
                data-na-portal-tool-bar-action
                data-event="{{elem.event}}"
                title="{{elem.label}}"
                data-url-target="{{elem.targetUrl}}"
                data-action-type="{{elem.type}}"
                data-ng-click="toolBarActionCtrl.action()"
                data-ng-bind="elem.labeli18n">
            </a>
        </li>
    </ul>
</div>

<div class="na-dropdown-export btn-group" data-na-portal-toolbar-detailed-export data-table-id="{{toolbarCtrl.datatableId}}" data-ng-if="component.type && component.type == 'detailedExport'">

    <button title="{{component.title}}" role="{{component.htmlElementType}}" class="{{component.htmlClass}}" type="button"
            id="{{toolbarDetailedExportCtrl.tableId}}_rightaction_{{component.type}}" data-na-portal-tool-bar-action data-ng-click="toolbarDetailedExportCtrl.setTableTotalRecords()">
        <i class="{{component.icon}}"></i>
        <span class="caret"></span>
    </button>

    <div class="fx-dropdown fx-dropdown-open-to-left dropdown-menu" id="{{toolbarDetailedExportCtrl.tableId}}_detailedExport_content">
        <div class="fx-dropdown-body">
            <label>{{toolbarDetailedExportCtrl.i18nExportingFormats}}</label>
            <ul class="list-unstyled">
                <li class="checkbox" data-ng-repeat-start="elem in component.action" data-ng-if="$first">
                    <label>
                        <input type="{{elem.htmlElementType}}"
                               data-event="{{elem.event}}"
                               data-url-target="{{elem.targetUrl}}"
                               data-action-type="{{elem.type}}"
                               data-ng-model="toolbarDetailedExportCtrl.selectedFormat"
                               value="{{elem.type}}">
                        {{elem.type}}
                    </label>
                </li>
                <li class="checkbox" data-ng-repeat-end data-ng-if="!$first">
                    <label>
                        <input type="{{elem.htmlElementType}}"
                               data-event="{{elem.event}}"
                               title="{{elem.label}}"
                               data-url-target="{{elem.targetUrl}}"
                               data-action-type="{{elem.type}}"
                               data-ng-model="toolbarDetailedExportCtrl.selectedFormat"
                               value="{{elem.type}}">
                        {{elem.type}}
                    </label>
                </li>
            </ul>
            <label>{{toolbarDetailedExportCtrl.i18nExportingResults}}</label>
            <ul class="list-unstyled">
                <li class="checkbox">
                    <label>
                        <input type="radio" name="iexport" data-ng-model="toolbarDetailedExportCtrl.infoToExport" value="one">
                        {{toolbarDetailedExportCtrl.i18nCurrentPage}}</label>
                </li>
                <li class="checkbox">
                    <label>
                        <input type="radio" name="iexport" data-ng-model="toolbarDetailedExportCtrl.infoToExport" value="all">
                        {{toolbarDetailedExportCtrl.i18nAllPages}} ({{toolbarDetailedExportCtrl.totalRecords}} {{toolbarDetailedExportCtrl.i18nResults}})</label>
                </li>
            </ul>
        </div>
        <div class="alert alert-info" data-ng-if="toolbarDetailedExportCtrl.sendExportByEmail == true" style="white-space: normal;">
            <div class="fx-alert-icon">
                <i class="fuxicons fuxicons-info"></i>
            </div>
            <div class="fx-alert-desc">
                <p>
                    <strong>{{toolbarDetailedExportCtrl.i18nExportToMail}}</strong>
                </p>
            </div>
        </div>
        <footer class="fx-dropdown-menu-actions">
            <button id="exportBtn" role="button" class="btn btn-primary btn-sm" data-ng-bind="toolbarDetailedExportCtrl.i18nExport" data-ng-click="toolbarDetailedExportCtrl.exportData()">
            </button>
            <button id="exportCancel" role="button" class="btn btn-default btn-sm" data-ng-bind="toolbarDetailedExportCtrl.i18nCancel" data-ng-click="toolbarDetailedExportCtrl.cancel()">
            </button>
        </footer>
    </div>
</div>
<div class="btn-group" data-ng-if="(!component.type || ( component.type && component.type == 'button'))">
    <button data-ng-if="!component.action[0].onClick"
            title="{{component.title}}" role="{{component.htmlElementType}}" class="{{component.htmlClass}}" type="button"
            data-event="{{component.action[0].event}}"
            data-url-target="{{component.action[0].targetUrl}}"
            data-action-type="{{component.action[0].type}}"
            data-ng-click="toolBarActionCtrl.action()"
            id="{{tableId}}_rightaction_{{component.action[0].label}}"
            data-extra-attributes="{{component.extraAttributes}}" data-na-portal-tool-bar-action>
            <i class="{{component.icon}}">
            </i>
            {{component.action[0].labeli18n}}
    </button>

    <button data-ng-if="component.action[0].onClick"
            title="{{component.title}}" role="{{component.htmlElementType}}" class="{{component.htmlClass}}" type="button"
            data-ng-click="toolBarActionCtrl.actionPredefined(component.action[0].onClick)"
            id="{{tableId}}_rightaction_{{component.elementId}}" data-extra-attributes="{{component.extraAttributes}}" data-na-portal-tool-bar-action>
        <i class="{{component.icon}}">
        </i>
        {{component.action[0].labeli18n}}
    </button>
</div>

<div class="btn-group" data-ng-if="(component.type && component.type == 'rowfiltering')">
    <button title="{{component.title}}" class="{{component.htmlClass}}"
            id="{{tableId}}_rightaction_{{component.elementId}}"
            data-toggle="collapse" data-target="#{{component.targetElement}}_{{toolbarCtrl.datatableId}}"
            data-na-portal-tool-bar-action style="margin-bottom: 0px;">
        <i class="{{component.icon}}">
        </i>
    </button>
</div>

<div class="btn-group" data-ng-if="component.type == 'columnsConf'">
    <div class="btn-group fx-dropdown-reverse">
        <button type="button" class="btn btn--table-column-filter btn-default dropdown-toggle btn btn-default btn-sm" aria-expanded="false" data-toggle="dropdown">
            <i class="fuxicons fuxicons-manage-columns fx-icon"></i>
            <span class="fa fa-angle-down"></span>
        </button>
        <div class="dropdown fx-dropdown dropdown-menu--table-column-filterdropdown dropdown-menu">
            <div class="fx-dropdown-body fx-dropdown-hammer">
                <div class="form-group fx-search-input">
                    <div class="fx-pos-rel">
                        <button class="glyphicon glyphicon-search" data-ng-click="toolbarCtrl.search(toolbarCtrl.datatableId + '_columnFilterTree')"></button>
                        <input id="columnFilterSearchBtn"
                               type="text"
                               data-ng-model="toolbarCtrl.searchField"
                               placeholder="{{toolbarCtrl.searchPlaceholder}}"
                               class="form-control input-sm full-width">
                    </div>
                </div>
                <div data-na-portal-tree data-ng-controller="NaPortalToolbarTreeColumnFilterController" data-na-portal-toolbar-tree-column-filter
                     id="{{toolbarCtrl.datatableId}}_columnFilterTree" class="jstree jstree-0 jstree-focused jstree-fuxi" data-table-id="{{toolbarCtrl.datatableId}}"
                     data-module="{{toolbarCtrl.module}}">
                </div>
            </div>
            <div class="fx-dropdown-footer clearfix fx-dropdown-hammer">
                <button id="columnFilterApplyBtn" class="btn btn-primary btn-sm" role="button" ng-bind="toolbarCtrl.i18nApply" data-ng-click="toolbarCtrl.applyChanges(toolbarCtrl.datatableId + '_columnFilterTree')">
                </button>
                <button id="columnFilterCancelBtn" class="btn btn-default btn-sm" role="button" ng-bind="toolbarCtrl.i18nCancel" data-ng-click="toolbarCtrl.cancelChanges(toolbarCtrl.datatableId + '_columnFilterTree')">
                </button>
            </div>
        </div>
    </div>
</div>
<span data-ng-repeat-end="" ></span>
