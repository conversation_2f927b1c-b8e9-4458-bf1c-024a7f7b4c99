/**
   * @ngdoc directive
   * @name module.directive:toolbarrightaction
   * @restrict EAC
   * @description
   * Directive that is responsible for the actions that are shown on the right side of the toolbar.
   * These actions aim to operate over the table.
   * @element DIV
   * @example
   * '<div toolbarrightaction></div>'
   **/
import { getDatatable } from '~components/naDatatables/datatables.service'
import { getTree, updateTree } from '~components/jstree/js-tree.service'
import templateHtml from './toolbar-right-action.template.html'
import { modules, providers } from '~basemodule/app/basemodule-providers'

angular.module(modules.datatables).directive(providers.datatables.directives.ToolbarRightAction, [
  ToolbarRightActionDirective
])

function ToolbarRightActionDirective() {
  return {
    restrict: 'AE',
    terminal: false,
    template: templateHtml,
    controller: providers.datatables.controllers.ToolbarRightAction,
    controllerAs: 'toolbarCtrl',
    link: function (scope, element, attrs, controller) {
      scope.tableId = attrs.tableId
      controller.datatableId = attrs.tableId
      const datatable = getDatatable(attrs.tableId)
      controller.datatableApi = datatable.datatableApi
      controller.jqueryDatatable = datatable.jQueryDatatable

      controller.applyChanges = function (id) {
        const tree = getTree(id)
        const temp = []

        const treeNodes = tree.tree.jstree(true).get_json()
        const unselectedNodes = treeNodes.filter(function (treeNode) {
          return treeNode.state.selected === false
        }).map(function (treeNode) {
          return { treeId: treeNode.id, columnId: treeNode.data.columnId }
        })

        tree.tree.jstree(true).get_selected().forEach(function (value) {
          const columnId = tree.tree.jstree(true).get_node(value).data.columnId
          controller.datatableApi.column(columnId).visible(true)
          temp.push({ index: value, checked: true })
        })

        unselectedNodes.forEach(function (unselectedNode) {
          controller.datatableApi.column(unselectedNode.columnId).visible(false)
          temp.push({ index: unselectedNode.treeId, checked: false })
        })

        tree.treeInfo = temp
        updateTree(id, tree.tree, tree.treeInfo)
        controller.datatableApi.draw(false)
        $('#' + controller.datatableId + '_columnsConf_content').hide()
      }

      controller.cancelChanges = function (id) {
        const tree = getTree(id)

        tree.treeInfo.forEach(function (value) {
          controller.datatableApi.column(value.index).visible(value.checked)
          tree.tree.jstree(value.checked ? 'check_node' : 'uncheck_node', value.index)
        })
        $('#' + controller.datatableId + '_columnsConf_content').hide()
      }

      controller.search = function (id) {
        getTree(id).tree.jstree('search', controller.searchField)
      }

      const ENTER_KEY = 13
      element.on('keypress', '.fx-search-input', function (e) {
        if (e.which === ENTER_KEY) {
          controller.search(controller.datatableId + '_columnFilterTree')
        }
      })

      /**
       * This hammer fixes the dropdown behavior when clicking on the dropdown itself.
       */
      element.on('click', '.fx-dropdown-hammer', function (e) {
        if (!e?.target?.matches('#columnFilterApplyBtn, #columnFilterCancelBtn')) {
          e.stopPropagation()
        }
      })

      /**
       * This hammer resets the dropdown state when opening the dropdown.
       */
      element.on('click', '.btn--table-column-filter', function () {
        controller.cancelChanges(controller.datatableId + '_columnFilterTree')
      })

      element.on('show.bs.dropdown', function (ev) {
        const btnGroup = $(ev.target)
        const dropdown = btnGroup.find('.dropdown-menu')
        if (dropdown.length <= 0) { return }
        queueMicrotask(() => {
          btnGroup.removeClass('dropup')
          const rect = dropdown[0].getBoundingClientRect()
          btnGroup.toggleClass('dropup', rect.bottom > document.documentElement.clientHeight)
        })
      })
    }
  }
}
