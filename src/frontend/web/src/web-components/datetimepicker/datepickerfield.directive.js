import { i18n } from '~utils/i18n'
import * as moment from 'moment'
import module from './datetimepicker.angular-module'
import { directiveName as fieldNotificationDirectiveName } from '~components/validation/directives/field-notification.directive'

/**
   * @ngdoc directive
   * @name module.directive:datapickerfield
   * @restrict A
   * @description
   * Directive that validates the syntax of the input against a regex.
   * Additionally, depending on the element Id and input received, fires an event in order to determine new dates.
   * @element Input associated with the timepicker
   * @example
   * <input data-datapickerfield data-pair-condition='afftectedDatepicker'></input>
   **/
module.directive('datapickerfield', [function () {
  const validate = function(notification, element, viewValue, scope) {
    if (typeof viewValue === 'undefined' || viewValue.length === 0) {
      notification.setMessageVisible(false)
      notification.setState(false)
      return undefined
    }

    let notificationType
    let notificationMsg
    const pass = moment(viewValue, [scope.dateFormat], true).isValid()
    if (!pass && viewValue !== 'undefined') {
      notificationType = 'error'
      notificationMsg = i18n('na.notifications.baddate')
      notification.setMsg(notificationMsg)
      notification.setState(true, notificationType)
      notification.setMessageVisible(true)
      return viewValue
    } else {
      notification.setState(false)
      notification.setMessageVisible(false)
      return viewValue
    }
  }

  return {
    require: ['^' + fieldNotificationDirectiveName, 'ngModel'],
    restrict: 'A',
    terminal: false,
    link: function (scope, element, attrs, ctrls) {
      const notification = ctrls[0]
      const ngModel = ctrls[1]
      scope.$on('DateTimePicker::ValueChanged', function (event, params) {
        ngModel.$setViewValue(params)
        ngModel.$render()
        scope.$applyAsync()
        element.trigger('input')
      })
      const modelInUTC = attrs.datapickerfieldModelInUtc != null

      scope.previousState.value = scope.$eval(attrs.ngModel)

      scope.$watch(attrs.ngModel, function () {
        const val = scope.$eval(attrs.ngModel)
        scope.$emit('DateTimePicker::InputValueChanged', { newValue: modelInUTC ? fromUTC(val) : val })
      })

      notification.setMessageVisible(false)

      function toUTC(date) {
        if (date == null) {
          return date
        }
        return moment(date, scope.dateFormat, true).utc().format(scope.dateFormat)
      }

      function fromUTC(date) {
        if (date == null) {
          return date
        }
        return moment.utc(date, scope.dateFormat, true).local().format(scope.dateFormat)
      }

      ngModel.$parsers.unshift(function(viewValue) {
        if (typeof viewValue === 'undefined') {
          return undefined
        } else {
          const result = validate(notification, element, viewValue, scope)
          return modelInUTC ? toUTC(result) : result
        }
      })

      ngModel.$formatters.unshift(function(modelValue) {
        if (typeof modelValue === 'undefined') {
          return undefined
        } else {
          const result = validate(notification, element, modelValue, scope)
          return modelInUTC ? fromUTC(result) : result
        }
      })
    }
  }
}])
