import './jquery-size-fallback'
import 'eonasdan-bootstrap-datetimepicker'
import 'basepack/lib/bootstrap-datetimepicker/4.17/css/bootstrap-datetimepicker.css'
import module from './datetimepicker.angular-module'
import * as moment from 'moment'
import { DatetimePickerService, converter } from '~utils/time-deprecated'

module.directive('dateTimePicker', [DateTimePickerDirective])
function DateTimePickerDirective() {
  function dateFormatOfBackend(scope, dateFormat) {
    if (typeof dateFormat !== 'undefined') {
      scope.dateFormat = dateFormat
    } else if (scope.dateTimePickerDateFormat) {
      scope.dateFormat = scope.dateTimePickerDateFormat
    } else {
      scope.dateFormat = DatetimePickerService.getDateFormatByLanguage()
      console.warn("undefined date format on datetimepicker, setting format to default '%s' ", scope.dateFormat)
    }
  }

  function initDateTimePicker(auxParams, params) {
    const scope = auxParams.scope; const element = auxParams.element
    const attrs = auxParams.attrs
    const icons = {}
    let pickDate, pickTime, useMinutes, useSeconds

    const language = document.body.getAttribute('lang')
    if (typeof element.attr('data-datetimepicker-ext') === 'undefined') {
      pickDate = params.pickDate
      pickTime = params.pickTime
      useMinutes = params.useMinutes
      useSeconds = params.useSeconds
    } else {
      pickDate = element.attr('data-datetimepicker-ext').indexOf('d') > -1
      if (element.attr('data-datetimepicker-ext').indexOf('t') > -1) {
        pickTime = true
        useMinutes = true
        useSeconds = false
      } else {
        pickTime = false
      }
    }

    const useCurrent = params.useCurrent
    const minuteStepping = params.minuteStepping
    let minDate = params.minDate
    if (typeof minDate === 'string') {
      minDate = moment(params.minDate, params.dateFormat)
    }
    let maxDate = params.maxDate
    const showToday = params.showToday
    let defaultDate = params.defaultDate
    const disabledDates = params.disabledDates
    if (params.icons.time !== '') {
      icons.time = params.icons.time
    }
    if (params.icons.date !== '') {
      icons.date = params.icons.date
    }
    if (params.icons.up !== '') {
      icons.up = params.icons.up
    }
    if (params.icons.down !== '') {
      icons.down = params.icons.down
    }
    const useStrict = params.useStrict
    const sideBySide = params.sideBySide
    const daysOfWeekDisabled = params.daysOfWeekDisabled

    // NA Customization
    if (attrs.maxDate !== undefined) {
      if (attrs.maxDate === 'today') {
        maxDate = moment().endOf('day')
      }
    }

    if (attrs.minDate !== undefined) {
      if (attrs.minDate === 'today') {
        minDate = moment().startOf('day')
      }
    }

    if (attrs.pickerValueEndOf !== undefined && attrs.pickerValueEndOf !== '') {
      defaultDate = moment().endOf(attrs.pickerValueEndOf)
    }

    element.datetimepicker({
      format: scope.dateFormat,
      pickDate,
      pickTime, // en/disables the time picker
      useMinutes, // en/disables the minutes picker
      useSeconds, // en/disables the seconds picker
      useCurrent, // when true, picker will set the value to the current date/time
      minuteStepping, // set the minute stepping
      minDate, // set a minimum date
      maxDate, // set a maximum date (defaults to today +100 years)
      showToday, // shows the today indicator
      language, // sets language locale
      defaultDate, // sets a default date, accepts js dates, strings and moment objects
      disabledDates, // an array of dates that cannot be selected
      icons,
      useStrict, // use "strict" when validating dates
      sideBySide, // show the date and time picker side by side
      daysOfWeekDisabled
    })

    scope.$broadcast('dateTimePickerConfLoaded', params)
    scope.$on('$destroy', function () {
      const dateTimePicker = element.data('DateTimePicker')
      if (dateTimePicker) {
        dateTimePicker.destroy()
      }
    })
  }

  function initializeEvents(scope, element) {
    const $pickerInput = element.find('input')
    $pickerInput.focusout(function() {
      element.data('DateTimePicker').hide()
    })

    function updateTime() {
      const elementDateObj = element.data('DateTimePicker').getDate()
      let elementDate, time
      if (elementDateObj) {
        elementDate = elementDateObj._d
        time = moment(elementDate).format(scope.dateFormat)
      } else {
        time = $pickerInput.val()
      }
      if (time !== scope.previousState.value) {
        scope.previousState.value = time
        scope.$broadcast('DateTimePicker::ValueChanged', time)
      }
    }

    function syncPickerWithValue(newValue) {
      const momentDate = moment(newValue, scope.dateFormat, true)
      if (momentDate.isValid()) {
        element.data('DateTimePicker').setDate(momentDate.toDate())
      }
    }

    scope.$watch(() => $pickerInput.val(), syncPickerWithValue)
    syncPickerWithValue($pickerInput.val())

    element.on('change.dp', function() { updateTime() })

    function clickDateEventHandler(event) {
      if ($(event.target).is('td.day.active, td.hour, td.minute') && element.data('DateTimePicker').unset) {
        element.data('DateTimePicker').unset = false
        updateTime()
      }
    }

    element.on('dp.show', function() {
      element.data('DateTimePicker').widget.get(0).addEventListener('click', clickDateEventHandler, true)
      $pickerInput.focus()
    })

    element.on('dp.hide', function() {
      element.data('DateTimePicker').widget.get(0).removeEventListener('click', clickDateEventHandler, true)
    })

    scope.$on('DateTimePicker::InputValueChanged', function (event, params) {
      // Update datetimepicker value with input value. This is necessary when input date is changed (manually)
      // and loose focus event is not triggered, then datetimepicker is not updated
      // element.data("DateTimePicker").setDate($pickerInput.val());

      const initialDate = params.newValue
      if (initialDate != null && initialDate !== '') {
        syncPickerWithValue(initialDate)
      } else {
        element.data('DateTimePicker').setDate(null)
      }
      scope.previousState.value = initialDate
    })
  }

  return {
    restrict: 'A',
    priority: 0,
    scope: true,
    terminal: false,
    link: {
      pre: function dateTimePickerPreLink(scope, element, attrs) {
        scope.previousState = {
          value: undefined
        }

        let dateFormat = attrs.dateFormat
        if (dateFormat == null && attrs.javaDateFormat) {
          dateFormat = converter.jDFToMomentFormatString(attrs.javaDateFormat)
        }
        dateFormatOfBackend(scope, dateFormat)
      },
      post: function dateTimePickerPostLink(scope, element, attrs) {
        DatetimePickerService.awaitDateTimePickerConf().then(conf => {
          initDateTimePicker({ scope, element, attrs }, conf)
          initializeEvents(scope, element, attrs)
        })
      }
    }

  }
}
