import { translate } from '~utils/i18n'
import { onProvide } from '~utils/i18n/provider'
import { parse } from '~utils/i18n/string-with-parameter-parser.util'

// no need for a separate css file for a single rule that does not need to be changed
const cssLoad = new CSSStyleSheet().replace(':host { display: contents; }')

const validI18nPrefix = ['i18n-', 'data-i18n-']
const isI18nAttribute = (attributeName) => validI18nPrefix.some(prefix => attributeName.startsWith(prefix))
const getI18nAttributeNameTarget = (i18nAttributeName) => {
  for (let i = 0, { length } = validI18nPrefix; i < length; i++) {
    const prefix = validI18nPrefix[i]
    if (i18nAttributeName.startsWith(prefix)) {
      return i18nAttributeName.slice(prefix.length)
    }
  }
}

const observerCallbackForElement = (element) => {
  let animationFrame
  const targets = new Set()
  const treeTargets = new Set()
  const elementNodeType = element.ownerDocument.defaultView.Node.ELEMENT_NODE

  return (mutations) => {
    mutations.forEach(function (mutation) {
      switch (mutation.type) {
        case 'childList':
          return mutation.addedNodes.forEach(node => node.nodeType === elementNodeType && treeTargets.add(node))
        case 'attributes':
          if (isI18nAttribute(mutation.attributeName) && mutation.target != null) {
            return targets.add(mutation.target)
          }
      }
    })

    if (targets.size <= 0 && treeTargets.size <= 0) {
      return
    }

    cancelAnimationFrame(animationFrame)
    animationFrame = requestAnimationFrame(() => {
      targets.forEach(updateI8nAttributes)
      treeTargets.forEach(updateI8nAttributesOnTree)
      targets.clear()
      treeTargets.clear()
      const customEvent = new CustomEvent('i18n-attributes-updated')
      element.dispatchEvent(customEvent)
    })
  }
}

const observerConfig = {
  attributes: true,
  subtree: true,
  childList: true,
}

function updateI8nAttributes (targetNode) {
  const { attributes } = targetNode
  for (let i = 0, { length } = attributes; i < length; i++) {
    const attribute = attributes[i]
    if (isI18nAttribute(attribute.name)) {
      const key = attribute.value.trim()
      if (key === '') {
        continue
      }

      const { key: parsedKey, parameters } = parse(key)

      const targetAttribute = getI18nAttributeNameTarget(attribute.name)
      targetNode.setAttribute(targetAttribute, translate(parsedKey, ...parameters))
    }
  }
}

function updateI8nAttributesOnTree (root) {
  updateI8nAttributes(root)
  for (const element of root.querySelectorAll('*')) {
    updateI8nAttributes(element)
  }
}

const initialObserverInfo = {
  /** @type {MutationObserver | null} */ mutationObserver: null,
  /** @type {ReturnType<onProvide> | null} */ providerObserver: null,
}

/** @type WeakMap <I18nContainerElement, typeof initialObserverInfo> */
const observersInfoMap = new WeakMap()

export class I18nContainerElement extends HTMLElement {
  constructor () {
    super()
    observersInfoMap.set(this, { ...initialObserverInfo })
    const shadowRoot = this.attachShadow({ mode: 'closed' })
    shadowRoot.append(document.createElement('slot'))
    cssLoad.then(stylesheet => { shadowRoot.adoptedStyleSheets = [stylesheet] })
  }

  connectedCallback () {
    const hasParentI18nContainerElement = (() => {
      let currentElement = this.parentElement
      while (currentElement != null && currentElement !== document.body) {
        if (currentElement instanceof I18nContainerElement) {
          return true
        }
        currentElement = currentElement.parentElement
      }
      return false
    })()
    // this if condition prevent running updateI8nAttributesOnTree multiple times
    if (!hasParentI18nContainerElement) {
      const observersInfo = observersInfoMap.get(this)
      observersInfo.providerObserver?.unregister()
      observersInfo.providerObserver = onProvide(() => {
        observersInfo.mutationObserver = observersInfo.mutationObserver ?? new MutationObserver(observerCallbackForElement(this))
        observersInfo.mutationObserver.disconnect()
        observersInfo.mutationObserver.observe(this, observerConfig)
        updateI8nAttributesOnTree(this)
      })
    }
  }

  disconnectedCallback () {
    const observersInfo = observersInfoMap.get(this)
    observersInfo.mutationObserver?.disconnect()
    observersInfo.providerObserver?.unregister()
    observersInfo.providerObserver = null
  }

  get isObservingChanges () {
    return observersInfoMap.get(this).providerObserver != null
  }
}

export const elementTagName = 'x-i18n-attr-container'
customElements.define(elementTagName, I18nContainerElement)
