import tape from 'tape'
import { provide, clearProvider } from '~utils/i18n/provider'
import { elementTagName as tag } from '../i18n-container.element'

const tokensToString = (tokens) => tokens.map(token => token.text).join('')

const provideI18n = () => {
  const tokenize = (key) => [{ type: 'text', text: 'i18n_' + key }]
  provide({
    tokenize,
    translate: (key) => tokensToString(tokenize(key)),
  })
}

const provideI18nWithArgs = () => {
  const tokenize = (key, ...args) => [
    { type: 'text', text: 'i18n_' + key },
    ...args.map((arg, index) => ({
      type: 'parameter',
      text: ',i18n_Arg_' + arg,
      position: index,
      fallback: arg,
      usingFallback: false,
    }))]

  provide({
    tokenize,
    translate: (key, ...args) => tokensToString(tokenize(key, ...args)),
  })
}

const provideI18nOtherLanguage = () => {
  const tokenize = (key) => [{ type: 'text', text: 'other_language_i18n_' + key }]
  provide({
    tokenize,
    translate: (key) => tokensToString(tokenize(key)),
  })
}

const prefix = 'i18n Component Container - '

tape.test(prefix + ' attributes of element is equal to the i18n translation', (t) => {
  // prepare
  t.plan(2)
  const { body } = window.document

  // act
  provideI18n()
  body.innerHTML = `<${tag} class="target" data-i18n-title="lorem_ipsum" i18n-custom-attr="custom_lorem_ipsum"></${tag}>`

  // assert
  t.isEqual(body.querySelector('.target').getAttribute('title'), 'i18n_lorem_ipsum')
  t.isEqual(body.querySelector('.target').getAttribute('custom-attr'), 'i18n_custom_lorem_ipsum')

  // clean
  body.innerHTML = ''
  clearProvider()
})

tape.test(prefix + ' attributes of children is equal to the i18n translation', (t) => {
  // prepare
  t.plan(2)
  const { body } = window.document

  // act
  provideI18n()
  body.innerHTML = `<${tag} class="root-target">
  <div class="target" data-i18n-title="lorem_ipsum" data-i18n-custom-attribute-child="custom_lorem_ipsum">
</${tag}>`

  // assert
  t.isEqual(body.querySelector('.target').getAttribute('title'), 'i18n_lorem_ipsum')
  t.isEqual(body.querySelector('.target').getAttribute('custom-attribute-child'), 'i18n_custom_lorem_ipsum')

  // clean
  body.innerHTML = ''
  clearProvider()
})

tape.test(prefix + ' attributes of descendant is equal to the i18n translation', (t) => {
  // prepare
  t.plan(5)
  const { body } = window.document

  // act
  provideI18n()
  body.innerHTML = `<${tag} class="root-target" data-i18n-data-lorem="lorem">
    <div class="target--child" data-i18n-am-i-child="yes">
      <div class="target--node" data-i18n-value="ok">
        <div class="target--leaf" data-i18n-title="lorem_ipsum" data-i18n-custom-attribute-descendant="custom_lorem_ipsum">
      </div>
    </div>
</${tag}>`

  // assert
  t.isEqual(body.querySelector('.root-target').getAttribute('data-lorem'), 'i18n_lorem')
  t.isEqual(body.querySelector('.target--child').getAttribute('am-i-child'), 'i18n_yes')
  t.isEqual(body.querySelector('.target--node').getAttribute('value'), 'i18n_ok')
  t.isEqual(body.querySelector('.target--leaf').getAttribute('title'), 'i18n_lorem_ipsum')
  t.isEqual(body.querySelector('.target--leaf').getAttribute('custom-attribute-descendant'), 'i18n_custom_lorem_ipsum')

  // clean
  body.innerHTML = ''
  clearProvider()
})

tape.test(prefix + ' attributes of descendant are not applied when no i18n implementation is provided', (t) => {
  // prepare
  t.plan(5)
  const { body } = window.document

  // act
  body.innerHTML = `<${tag} class="root-target" data-i18n-data-lorem="lorem">
    <div class="target--child" data-i18n-am-i-child="yes">
      <div class="target--node" data-i18n-value="ok">
        <div class="target--leaf" data-i18n-title="lorem_ipsum" data-i18n-custom-attribute-descendant="custom_lorem_ipsum">
      </div>
    </div>
</${tag}>`

  // assert
  t.false(body.querySelector('.root-target').hasAttribute('data-lorem'))
  t.false(body.querySelector('.target--child').hasAttribute('am-i-child'))
  t.false(body.querySelector('.target--node').hasAttribute('value'))
  t.false(body.querySelector('.target--leaf').hasAttribute('title'))
  t.false(body.querySelector('.target--leaf').hasAttribute('custom-attribute-descendant'))

  // clean
  body.innerHTML = ''
  clearProvider()
})

tape.test(prefix + ' attributes of descendant are updated when a i18n implementation is provided', (t) => {
  // prepare
  t.plan(3)
  const { body } = window.document

  // act
  body.innerHTML = `<${tag} class="root-target" data-i18n-data-lorem="lorem">
    <div class="target--child" data-i18n-am-i-child="yes">
      <div class="target--node" data-i18n-value="ok">
        <div class="target--leaf" data-i18n-title="lorem_ipsum" data-i18n-custom-attribute="custom_lorem_ipsum">
      </div>
    </div>
</${tag}>`

  const getAttributes = () => ({
    rootTarget: body.querySelector('.root-target').getAttribute('data-lorem'),
    childTarget: body.querySelector('.target--child').getAttribute('am-i-child'),
    nodeTarget: body.querySelector('.target--node').getAttribute('value'),
    leafTitle: body.querySelector('.target--leaf').getAttribute('title'),
    leafCustomAttribute: body.querySelector('.target--leaf').getAttribute('custom-attribute'),
  })

  const attributesBeforeProvide = getAttributes()
  provideI18n()
  const attributesAfterFirstProvideI18n = getAttributes()
  provideI18nOtherLanguage()
  const attributesAfterSecondProvideI18n = getAttributes()

  // assert
  t.deepEquals(attributesBeforeProvide, {
    rootTarget: null,
    childTarget: null,
    nodeTarget: null,
    leafTitle: null,
    leafCustomAttribute: null,
  })

  t.deepEquals(attributesAfterFirstProvideI18n, {
    rootTarget: 'i18n_lorem',
    childTarget: 'i18n_yes',
    nodeTarget: 'i18n_ok',
    leafTitle: 'i18n_lorem_ipsum',
    leafCustomAttribute: 'i18n_custom_lorem_ipsum',
  })

  t.deepEquals(attributesAfterSecondProvideI18n, {
    rootTarget: 'other_language_i18n_lorem',
    childTarget: 'other_language_i18n_yes',
    nodeTarget: 'other_language_i18n_ok',
    leafTitle: 'other_language_i18n_lorem_ipsum',
    leafCustomAttribute: 'other_language_i18n_custom_lorem_ipsum',
  })

  // clean
  body.innerHTML = ''
  clearProvider()
})

tape.test(prefix + ' attributes of descendant are not applied when i18n attributes are absent, empty or with spaces', (t) => {
  // prepare
  t.plan(5)
  const { body } = window.document

  // act
  provideI18n()
  body.innerHTML = `<${tag} class="root-target" data-i18n-data-lorem="">
    <div class="target--child">
      <div class="target--node" data-i18n-value=" ">
        <div class="target--leaf" data-i18n-title="" data-i18n-custom-attribute-descendant="still-visible">
      </div>
    </div>
</${tag}>`

  // assert
  t.false(body.querySelector('.root-target').hasAttribute('data-lorem'))
  t.false(body.querySelector('.target--child').hasAttribute('am-i-child'))
  t.false(body.querySelector('.target--node').hasAttribute('value'))
  t.false(body.querySelector('.target--leaf').hasAttribute('title'))
  t.true(body.querySelector('.target--leaf').hasAttribute('custom-attribute-descendant'))

  // clean
  body.innerHTML = ''
  clearProvider()
})

tape.test(prefix + ' attributes of descendant are updated when i18n attributes values changes', (t) => {
  // prepare
  t.plan(2)
  const { body } = window.document

  // act
  provideI18n()
  body.innerHTML = `<${tag} class="root-target" data-i18n-data-lorem="lorem">
    <div class="target--child" data-i18n-am-i-child="yes">
      <div class="target--node" data-i18n-value="ok">
        <div class="target--leaf" data-i18n-title="lorem_ipsum" data-i18n-custom-attribute="custom_lorem_ipsum">
      </div>
    </div>
</${tag}>`

  const getAttributes = () => ({
    rootTarget: body.querySelector('.root-target').getAttribute('data-lorem'),
    childTarget: body.querySelector('.target--child').getAttribute('am-i-child'),
    nodeTarget: body.querySelector('.target--node').getAttribute('value'),
    leafTitle: body.querySelector('.target--leaf').getAttribute('title'),
    leafCustomAttribute: body.querySelector('.target--leaf').getAttribute('custom-attribute'),
  })

  const rootTarget = body.querySelector('.root-target')
  const attributesBeforeUpdate = getAttributes()

  rootTarget.setAttribute('data-i18n-data-lorem', 'ipsum')
  body.querySelector('.target--child').setAttribute('data-i18n-am-i-child', 'yes-you-still-are')
  body.querySelector('.target--node').setAttribute('data-i18n-value', 'nok')
  body.querySelector('.target--leaf').setAttribute('data-i18n-title', 'dolor sit amet')
  body.querySelector('.target--leaf').setAttribute('data-i18n-custom-attribute', 'still-custom')

  rootTarget.addEventListener('i18n-attributes-updated', function onAttributesUpdated() {
    const attributesAfterUpdate = getAttributes()

    // assert

    t.deepEquals(attributesBeforeUpdate, {
      rootTarget: 'i18n_lorem',
      childTarget: 'i18n_yes',
      nodeTarget: 'i18n_ok',
      leafTitle: 'i18n_lorem_ipsum',
      leafCustomAttribute: 'i18n_custom_lorem_ipsum',
    })

    t.deepEquals(attributesAfterUpdate, {
      rootTarget: 'i18n_ipsum',
      childTarget: 'i18n_yes-you-still-are',
      nodeTarget: 'i18n_nok',
      leafTitle: 'i18n_dolor sit amet',
      leafCustomAttribute: 'i18n_still-custom',
    })

    // clean
    rootTarget.removeEventListener('i18n-attributes-updated', onAttributesUpdated)
    body.innerHTML = ''
    clearProvider()
  })
})

tape.test(prefix + ' attributes of descendant are updated when new elements with i18n attributes are created', (t) => {
  // prepare
  t.plan(5)
  const { body } = window.document

  // act
  provideI18n()
  body.innerHTML = `<${tag} class="root-target" data-i18n-data-lorem="lorem">
    <div class="target--child" data-i18n-am-i-child="yes">
    </div>
</${tag}>`

  const rootTarget = body.querySelector('.root-target')
  const childTarget = body.querySelector('.target--child')
  const rootTargetAttribute = rootTarget.getAttribute('data-lorem')
  const childTargetAttribute = childTarget.getAttribute('am-i-child')

  setTimeout(() => {
    childTarget.innerHTML = `<div class="target--node" data-i18n-value="ok">
    <div class="target--leaf" data-i18n-title="lorem_ipsum" data-i18n-custom-attribute="custom_lorem_ipsum">
  </div>`
  }, 0)

  rootTarget.addEventListener('i18n-attributes-updated', function onAttributesUpdated() {
    const nodeTargetAttribute = body.querySelector('.target--node').getAttribute('value')
    const leafTitleAttribute = body.querySelector('.target--leaf').getAttribute('title')
    const leafCustomAttribute = body.querySelector('.target--leaf').getAttribute('custom-attribute')

    // assert

    t.isEqual(rootTargetAttribute, 'i18n_lorem')
    t.isEqual(childTargetAttribute, 'i18n_yes')
    t.isEqual(nodeTargetAttribute, 'i18n_ok')
    t.isEqual(leafTitleAttribute, 'i18n_lorem_ipsum')
    t.isEqual(leafCustomAttribute, 'i18n_custom_lorem_ipsum')

    // clean
    rootTarget.removeEventListener('i18n-attributes-updated', onAttributesUpdated)
    body.innerHTML = ''
    clearProvider()
  })
})

tape.test(prefix + ' a i18n container inside another 18n container should not be watching for changes as to prevent running the same operation multiple times', (t) => {
  // prepare
  t.plan(3)
  const { body } = window.document

  // act
  provideI18n()
  body.innerHTML = `<${tag} class="root-target" data-i18n-data-lorem="lorem">
    <div class="target--child" data-i18n-am-i-child="yes">
      <${tag} class="child-container"></${tag}>
    </div>
</${tag}>`

  const rootTarget = body.querySelector('.root-target')
  const childContainer = body.querySelector('.child-container')

  setTimeout(() => {
    childContainer.innerHTML = `<div class="target--node" data-i18n-value="ok">
    <div class="target--leaf" data-i18n-title="lorem_ipsum" data-i18n-custom-attribute="custom_lorem_ipsum">
  </div>`
  }, 0)

  function prepareToFailIfItPasses() {
    t.fail('child container should not update attributes with an i18n container ascendant')
  }
  childContainer.addEventListener('i18n-attributes-updated', prepareToFailIfItPasses)

  rootTarget.addEventListener('i18n-attributes-updated', function onAttributesUpdated() {
    const attributes = {
      rootTarget: body.querySelector('.root-target').getAttribute('data-lorem'),
      childTarget: body.querySelector('.target--child').getAttribute('am-i-child'),
      nodeTarget: body.querySelector('.target--node').getAttribute('value'),
      leafTitle: body.querySelector('.target--leaf').getAttribute('title'),
      leafCustomAttribute: body.querySelector('.target--leaf').getAttribute('custom-attribute'),
    }
    // assert

    t.true(rootTarget.isObservingChanges)
    t.false(childContainer.isObservingChanges)
    t.deepEquals(attributes, {
      rootTarget: 'i18n_lorem',
      childTarget: 'i18n_yes',
      nodeTarget: 'i18n_ok',
      leafTitle: 'i18n_lorem_ipsum',
      leafCustomAttribute: 'i18n_custom_lorem_ipsum',
    })

    // clean
    childContainer.removeEventListener('i18n-attributes-updated', prepareToFailIfItPasses)
    rootTarget.removeEventListener('i18n-attributes-updated', onAttributesUpdated)
    body.innerHTML = ''
    clearProvider()
  })
})

tape.test(prefix + ' attributes of element is equal to the i18n translation with multiple arguments', (t) => {
  // prepare
  t.plan(2)
  const { body } = window.document

  // act
  provideI18nWithArgs()
  body.innerHTML = `<${tag} class="target" data-i18n-title="lorem_ipsum;argument1;argument2;argument3" i18n-custom-attr="custom_lorem_ipsum;argument1"></${tag}>`

  // assert
  t.isEqual(body.querySelector('.target').getAttribute('title'), 'i18n_lorem_ipsum,i18n_Arg_argument1,i18n_Arg_argument2,i18n_Arg_argument3')
  t.isEqual(body.querySelector('.target').getAttribute('custom-attr'), 'i18n_custom_lorem_ipsum,i18n_Arg_argument1')

  // clean
  body.innerHTML = ''
  clearProvider()
})
