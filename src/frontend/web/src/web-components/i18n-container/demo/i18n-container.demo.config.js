// This file is used by webpack-demos.config.js

const path = require('path')
module.exports = ({ menus }) => ({
  entry: path.resolve(__dirname, 'i18n-container.demo.js'),
  chunk: 'i18n/i18n-attr-container',
  htmlTemplate: path.resolve(__dirname, 'i18n-container.demo.html'),
  htmlOutput: 'i18n-attr-container.html',
  title: 'I18n on attributes Demo',
  navBarLabel: 'i18n attributes',
  menu: menus.i18n,
  dependencies: {}
})
