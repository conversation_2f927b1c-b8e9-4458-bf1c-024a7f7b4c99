<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title><%= title %></title>
<%= css %></head>
<body>
<%= navbar %>
<div class='container app'>
  <h1><%= title %></h1>

  <h3>Dependencies</h3>
  <%= dependencyTable %>

  <h3>Imports</h3>
  <pre><code class="code--import lang-js"></code></pre>

  <h3>Title attribute</h3>

  <x-i18n-attr-container>
    <button class="btn btn-m btn-primary" data-i18n-title="demo.i18n.hello.world">hover me</button>
  </x-i18n-attr-container>

  <pre><code class="xml">&lt;x-i18n-attr-container&gt;
  &lt;button class=&quot;btn btn-m btn-primary&quot; data-i18n-title=&quot;demo.hello.world&quot;&gt;hover me&lt;/button&gt;
&lt;/x-i18n-attr-container&gt;</code></pre>


  <h3>place holder attribute</h3>

  <x-i18n-attr-container>
    <input class="form-control input-sm" data-i18n-placeholder="demo.i18n.placeholder"/>
  </x-i18n-attr-container>

  <pre><code class="xml">&lt;x-i18n-attr-container&gt;
  &lt;input class=&quot;form-control input-sm&quot; data-i18n-placeholder=&quot;demo.i18n.placeholder&quot;/&gt;
&lt;/x-i18n-attr-container&gt;</code></pre>

  <h3>value attribute</h3>

  <x-i18n-attr-container>
    <input role="button" class="btn btn-m btn-primary" data-i18n-title="demo.i18n.hello.world" data-i18n-value="demo.i18n.hello.world" type="button"/>
  </x-i18n-attr-container>

  <pre><code class="xml">&lt;x-i18n-attr-container&gt;
  &lt;input role=&quot;button&quot; class=&quot;btn btn-m btn-primary&quot; data-i18n-title=&quot;demo.i18n.hello.world&quot; data-i18n-value=&quot;demo.i18n.hello.world&quot; type=&quot;button&quot;/&gt;
&lt;/x-i18n-attr-container&gt;</code></pre>



  <h3>Attributes with dynamic i18n</h3>

  <x-i18n-attr-container>
    <textarea class="form-control" data-i18n-placeholder="demo.i18n.dynamic.key;argument1;argument2"></textarea>
  </x-i18n-attr-container>

  <pre><code class="xml">&lt;x-i18n-attr-container&gt;
    &lt;textarea class=&quot;form-control&quot; data-i18n-placeholder=&quot;demo.i18n.dynamic.key;argument1;argument2&quot;&gt;&lt;/textarea&gt;
  &lt;/x-i18n-attr-container&gt;</code></pre>

  </div>
</div>
<%= js %></body>
</html>