/* eslint-disable camelcase */
import '~utils/proxy/implementation'
import './nossis-ui-contribution.css'

function importAll (requires) {
  requires.keys().forEach(requires)
}

importAll(require.context('.', true, /\.nossis-ui\.(js|ts)$/))

const applyFix1_fixTabContentWidth = () => {
  const styleFix = document.createElement('style')
  styleFix.innerHTML = '#operview-section .tab-content.operview-content{ flex-grow: 1 }'
  document.head.append(styleFix)
}

const applyFix2_makeDivAndIframeDimensionsConsistent = () => {
  const iframe = document.body.querySelector('#operview-content-iframe')
  const div = document.body.querySelector('.operview-content-wrap')

  if (!iframe || !div) {
    return requestAnimationFrame(applyFix2_makeDivAndIframeDimensionsConsistent)
  }

  const updateDivHeight = () => { div.style.height = iframe.style.height }
  updateDivHeight()

  const observer = new MutationObserver(updateDivHeight)
  observer.observe(iframe, {
    attributes: true,
    attributeFilter: ['style']
  })
}

document.addEventListener('DOMContentLoaded', () => {
  applyFix1_fixTabContentWidth()
  applyFix2_makeDivAndIframeDimensionsConsistent()
})
