body {
    overflow: hidden;
}

.navbar-brand {
    background: url('/na-ext/images/top-bar-logo-brand.svg') no-repeat 0 center !important;
}

.fx-footer {
    position: fixed !important;
}

/* ..fx-nav-tabs-stacked has display:flex but none of the children has any flex property defined on
 #operview-section element this style guarantees that the content fills the remaining space */
#operview-section .fx-nav-tabs-stacked > .operview-content {
    flex: 1;
}

.resume-title {
    border-bottom: 1px solid #d6d6d6;
    padding-bottom: 5px;
}