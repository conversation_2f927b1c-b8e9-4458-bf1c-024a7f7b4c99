import '~utils/i18n/implementation.play'
import bootstrapStylesheet from '~base-styles/bootstrap.stylesheet'
import fuxiStylesheet from '~base-styles/fuxi.stylesheet'
import { setAssetsBaseUrl, applyStylesOnStyleSheet } from '~utils/stylesheet-utils.util'
import cssText from './resume-scripts.css'
setAssetsBaseUrl(naportalBasemodulejsRoutes.na.naportalbase.controllers.Assets.versioned('na-portal-assets-vendors').url)
const emptyBody = new CSSStyleSheet()
emptyBody.replaceSync('body {display: none; font-display: block;}')
const css = new CSSStyleSheet()
applyStylesOnStyleSheet(css, cssText)
document.adoptedStyleSheets = [emptyBody, bootstrapStylesheet, fuxiStylesheet, css]

function importAll (requires) {
  requires.keys().forEach(requires)
}

importAll(require.context('.', true, /\.resume-script\.js$/))
import('~components/resume-section/resume-section.element')
