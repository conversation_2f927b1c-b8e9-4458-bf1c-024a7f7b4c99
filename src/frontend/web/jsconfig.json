{"compilerOptions": {"target": "es2019", "allowSyntheticDefaultImports": false, "moduleResolution": "node", "baseUrl": "./", "paths": {"~docs/*": ["./src/docs/*"], "~utils/*": ["./src/utils/*"], "~base-styles/*": ["./src/base-styles/*"], "~components/*": ["./src/web-components/*"], "~nowc/*": ["./node_modules/@alticelabsprojects/nossis-orchestration-web-components/dist/npm/*"], "~lib/*": ["./src/lib/*"], "~tsc-common-stores/*": ["./src/modules/tsc/commons-stores/*"], "~tsc-common-styles/*": ["./src/modules/tsc/common-styles/*"], "~tsc-utils/*": ["./src/modules/tsc/utils/*"], "~tsc-components/*": ["./src/modules/tsc/components/*"], "~basemodule/*": ["./src/modules/basemodule/*"], "~catalog-basemodule/*": ["./src/modules/catalog-basemodule/*"], "~monitoring/*": ["./src/modules/monitoring/*"], "~mpt/*": ["./src/modules/mpt/*"], "~go/*": ["./src/modules/go/*"], "~nadm/*": ["./src/modules/nadm/*"], "~operations-catalog/*": ["./src/modules/operations-catalog/*"], "~test-utils/*": ["./src/test-utils/*"]}}, "exclude": ["node_modules", "dist", "build"]}