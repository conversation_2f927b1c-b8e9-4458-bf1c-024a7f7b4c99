const path = require('path')
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin')

module.exports = {
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    symlinks: true, // Changed from false to true to support npm link
    modules: ['src', path.resolve('node_modules')],
    alias: {
      '~utils': path.resolve(__dirname, 'src/utils'),
      '~test-utils': path.resolve(__dirname, 'src/test-utils'),
      '~components': path.resolve(__dirname, 'src/web-components'),
      '~nowc': path.resolve(__dirname, 'node_modules/@alticelabsprojects/nossis-orchestration-web-components/dist/npm'),
      '~base-styles': path.resolve(__dirname, 'src/base-styles'),
      '~lib': path.resolve(__dirname, 'src/lib'),
      '~basemodule': path.resolve(__dirname, 'src/modules/basemodule'),
      '~catalog-basemodule': path.resolve(__dirname, 'src/modules/catalog-basemodule'),
      '~operations-catalog': path.resolve(__dirname, 'src/modules/operations-catalog'),
      '~monitoring': path.resolve(__dirname, 'src/modules/monitoring'),
      '~mpt': path.resolve(__dirname, 'src/modules/mpt'),
      '~go': path.resolve(__dirname, 'src/modules/go'),
      '~nadm': path.resolve(__dirname, 'src/modules/nadm'),
      '~docs': path.resolve(__dirname, 'src/docs'),
      '/na-ext/images': path.resolve(__dirname, 'dev-server')
    },
    fallback: {
      fs: false,
      stream: require.resolve('stream-browserify'),
      buffer: require.resolve('buffer/'),
      path: false,
      worker_threads: false,
    }
  },
  module: {
    rules: [{
      test: /\.m?js$/,
      exclude: /(node_modules|bower_components)/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: [['@babel/preset-env', {
            targets: {
              node: '22',
              chrome: '127',
              firefox: '129'
            }
          }]]
        }
      }
    }, {
      test: /\.ts$/,
      exclude: /(node_modules|bower_components)/,
      use: {
        loader: 'babel-loader',
      }
    }]
  },
  optimization: {
    runtimeChunk: {
      name: 'basemodule/runtime'
    },
    chunkIds: 'named',
    splitChunks: {
      // include all types of chunks
      cacheGroups: {
        // common chunk
        'vendors-graphs': {
          test: /[\\/]node_modules[\\/](dagre|jsplumb|graphlib)[\\/]/,
          name: 'na-portal-assets-vendors/vendors-graphs',
          chunks: 'all',
          priority: 20
        },

        'vendors-prettify': {
          test: /[\\/]node_modules[\\/]prettify[\\/]/,
          name: 'na-portal-assets-vendors/vendors-prettify',
          chunks: 'all',
          priority: 20
        },

        'vendors-notification': {
          test: /[\\/]node_modules[\\/]toastr[\\/]/,
          name: 'na-portal-assets-vendors/vendors-notification',
          chunks: 'async',
          priority: 20,
          enforce: true
        },

        'vendors-jquery': {
          test: /[\\/]node_modules[\\/]jquery[-a-zA-Z0-9]*[\\/]/,
          name: 'na-portal-assets-vendors/vendors-jquery',
          chunks: 'all',
          priority: 20,
          enforce: true
        },

        'vendors-tables': {
          test(module) {
            const path = require('path')
            return module.resource && (
              // all datatable plugins use prefix "datatables.net", so no leading slash is included in this next .includes()
              module.resource.includes('/node_modules/datatables.net'.replace(/\//g, path.sep)) ||
              module.resource.includes('/node_modules/basepack/lib/datatables/'.replace(/\//g, path.sep))
            )
          },
          name: 'na-portal-assets-vendors/vendors-tables',
          chunks: 'all',
          priority: 20,
          enforce: true
        },

        'vendors-std-utils': {
          test(module) {
            const path = require('path')
            return module.resource && (
              module.resource.includes('/node_modules/lodash/'.replace(/\//g, path.sep)) ||
                            module.resource.includes('/node_modules/underscore/'.replace(/\//g, path.sep))
            )
          },
          name: 'na-portal-assets-vendors/vendors-std-utils',
          chunks: 'all',
          priority: 20,
          enforce: true
        },

        'vendors-bootstrap': {
          test(module) {
            const path = require('path')
            return module.resource && (
              module.resource.includes('/node_modules/fuxi/app/css/vendor/bootstrap'.replace(/\//g, path.sep)) ||
              module.resource.includes('/node_modules/fuxi/app/js/vendor/bootstrap'.replace(/\//g, path.sep)) ||
              module.resource.includes('/node_modules/basepack/lib/bootstrap'.replace(/\//g, path.sep)) ||
              module.resource.includes('/node_modules/bootstrap'.replace(/\//g, path.sep))
            )
          },
          name: 'na-portal-assets-vendors/vendors-bootstrap',
          priority: 20,
          chunks: 'all'
        },

        'vendors-selects': {
          test(module) {
            const path = require('path')
            return module.resource && (
              module.resource.includes('/node_modules/basepack/lib/select2/'.replace(/\//g, path.sep)) ||
              module.resource.includes('/node_modules/basepack/src/select2-pt/'.replace(/\//g, path.sep)) ||
              module.resource.includes('/node_modules/select2/'.replace(/\//g, path.sep))
            )
          },
          name: 'na-portal-assets-vendors/vendors-selects',
          priority: 20,
          chunks: 'all',
          enforce: true, // https://github.com/webpack-contrib/mini-css-extract-plugin/issues/257#issuecomment-432594711
        },

        'vendors-fuxi': {
          test(module) {
            const path = require('path')
            return module.resource && (
              module.resource.includes('/node_modules/fuxi/'.replace(/\//g, path.sep)) &&
              !(
                module.resource.includes('/node_modules/fuxi/app/js/vendor/bootstrap'.replace(/\//g, path.sep)) ||
                module.resource.includes('/node_modules/fuxi/app/css/vendor/bootstrap'.replace(/\//g, path.sep))
              )
            )
          },
          name: 'na-portal-assets-vendors/vendors-fuxi',
          priority: 20,
          chunks: 'all',
          enforce: true
        },

        'vendors-fuxi-nossis': {
          test: /[\\/]node_modules[\\/]fuxi-nossis[\\/]/,
          name: 'na-portal-assets-vendors/vendors-fuxi-nossis',
          priority: 20,
          chunks: 'all',
          enforce: true
        },

        'vendors-basepack': {
          test: /[\\/]node_modules[\\/]basepack[\\/]/,
          name: 'na-portal-assets-vendors/vendors-basepack',
          chunks: 'all',
          priority: 15,
          enforce: true
        },

        'base-styles': {
          test: /[\\/]src[\\/]base-styles[\\/]/,
          name: 'na-portal-assets/base-styles',
          chunks: 'all',
          priority: 20,
          enforce: true
        },

        'web-component--resume-section': {
          test: /[\\/]src[\\/]web-components[\\/]resume-section[\\/]/,
          name: 'web-components/resume-section',
          chunks: 'all',
          priority: 20,
          enforce: true
        },

        basemodule: {
          test: /[\\/]src[\\/]basemodule[\\/]/,
          name: 'basemodule/basemodule',
          chunks: 'all'
        },

      }
    },
    minimizer: [
      '...',
      new CssMinimizerPlugin()
    ],
  }

}
