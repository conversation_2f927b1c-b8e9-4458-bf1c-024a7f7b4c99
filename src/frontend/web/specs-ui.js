require('babel-plugin-require-context-hook/register')()
const addHook = require('pirates').addHook
const jsStringEscape = require('js-string-escape')
const { Builder } = require('selenium-webdriver')
const moduleAlias = require('module-alias')
moduleAlias.addAliases(require('./webpack.config.parts.js').resolve.alias)
const { prepareReport } = require('~test-utils/test-ui/test-ui-utils.util')

const { Command } = require('commander');

const program = new Command();
program
  .version('3.0.0')
  .requiredOption('-o, --output <output-directory>', 'output directory')
  .parse()



const options = program.opts()

const report = prepareReport({ path: options.output });

/*
  prepareReport creates a tape stream , meaning the default one is removed (prints to stdout)
  the next code adds the default stream
 */
require('tape').createStream().pipe(process.stdout)


function matcher(/* filename */) {
  return true
}

addHook(
  (code, filename) => `module.exports = "${jsStringEscape(code)}" `,
  { exts: ['.css', '.html', '.pt', '.en', '.fr'], matcher }
)

function importAll (requires) {
  requires.keys().forEach(requires)
}

const driverPromise = new Builder().forBrowser('firefox').build()

globalThis.UITest = {
  /** @returns {ThenableWebDriver} */
  getDriver: () => driverPromise,
  get serverPort() {
    const { config } = require('./package.json')
    return config.server_port_ui_test_playground
  }
}

const cleanupTestsOnFinish = async () => {
  const tape = require('tape')
  const timeout = setTimeout(() => console.log('tests taking too long'), 86_400_000 /* 1 day */)

  tape.onFinish(() => {
    console.log('tests finished.')
    driverPromise
      .then(driver => {
        console.log('quitting browser...')
        return driver.quit()
      })
      .then(() => {
        console.log('driver quit.')
        console.log(`publishing report on ${report.path}.`)
        return report.publish()
      })
      .then(() => {
        console.log(`report published on ${report.path}.`)
        clearTimeout(timeout)
      })
      .catch(() => {
        clearTimeout(timeout)
      })
  })
}

importAll(require.context('./src', true, /\.spec-ui\.js$/))
cleanupTestsOnFinish()
