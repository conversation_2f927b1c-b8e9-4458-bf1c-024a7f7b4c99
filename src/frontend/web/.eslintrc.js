module.exports = {
  globals: {
    referenceDataJsRoutes: true,
    operationsCatalogJsRoutes: true,
    monitoringJsRoutes: true,
    naportalBasemodulejsRoutes: true,
    mptjsRoutes: true,
    na: true,
    angular: true,
    modules: true
  },
  env: {
    browser: true,
    es2021: true,
    jquery: true
  },
  extends: [
    'standard'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    'import/no-webpack-loader-syntax': 0,
    'no-useless-constructor': 0,
    'space-before-function-paren': 0,
    'multiline-ternary': 0,
    'comma-dangle': ['error', 'only-multiline'],
    'node/no-callback-literal': 0,
    'n/no-callback-literal': 0,
  }
}
