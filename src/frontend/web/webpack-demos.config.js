const path = require('node:path')
const fs = require('node:fs')
const webpack = require('webpack')
const shell = require('shelljs')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
const configParts = require('./webpack.config.parts.js')
registerRequireContext()

const menuGroups = {
  contributing: { label: 'Contributing' },
  api: { label: 'APIs' },
  baseComponents: { label: 'Base Components' },
  moduleComponents: { label: 'Module Components' },
  modulePages: { label: 'Module Pages' },
}

const menus = {
  modal: { label: 'Modal', group: menuGroups.api },
  notifications: { label: 'Notifications', group: menuGroups.api },
  tables: { label: 'Tables', group: menuGroups.baseComponents },
  selectbox: { label: 'Selectbox', group: menuGroups.baseComponents },
  scrolls: { label: 'Scrolls', group: menuGroups.baseComponents },
  breadcrumb: { label: 'Breadcrumb', group: menuGroups.baseComponents },
  i18n: { label: 'I18n', group: menuGroups.baseComponents },
  resumeSection: { label: 'Resume section', group: menuGroups.baseComponents },
  collapsible: { label: 'Collapsible', group: menuGroups.baseComponents },
  inputContainer: { label: 'Inputs Container', group: menuGroups.baseComponents },
  label: { label: 'Label', group: menuGroups.baseComponents },
  monitoringComponents: { label: 'Monitoring Components', group: menuGroups.moduleComponents },
  nadmComponents: { label: 'NADM Components', group: menuGroups.moduleComponents },
  monitoringPages: { label: 'Monitoring Pages' }, // add "group: menuGroups.modulePages" on multiple modules

  contributingDevelopment: { label: 'Development', group: menuGroups.contributing },
  contributingArchitecture: { label: 'Architecture', group: menuGroups.contributing }
}

const pages = {
  ...require.context('./src', true, /\.demo\.config\.js$/).keys().reduce((acc, key) => {
    acc[key] = require(key)({ menus })
    return acc
  }, {}),

  // Common components

  modal: {
    entry: './src/utils/modal/demo/demo.js',
    chunk: 'modals/modal',
    htmlTemplate: './src/utils/modal/demo/demo.html',
    htmlOutput: 'modal.html',
    title: 'Modals Demo',
    navBarLabel: 'Modals',
    menu: menus.modal,
    dependencies: {
      Bootstrap: { logic: true, styles: true },
      Fuxi: { styles: true }
    }
  },

  naDatatablesV2: {
    entry: './src/web-components/naDatatables-v2/demo/demo.js',
    chunk: 'naDatatableV2/naDatatableV2',
    htmlTemplate: './src/web-components/naDatatables-v2/demo/demo.html',
    htmlOutput: 'datatable.html',
    title: 'Tables Demo (Angular Js Directive)',
    navBarLabel: 'Tables (Angular Js Directive)',
    menu: menus.tables,
    dependencies: {
      Angular: { logic: true },
      Bootstrap: { styles: true },
      datatables: { logic: true, styles: true },
      Fuxi: { styles: true }
    }
  },
  toastNotification: {
    entry: './src/utils/toast-notification/demo/demo.js',
    chunk: 'toast/toast',
    htmlTemplate: './src/utils/toast-notification/demo/demo.html',
    htmlOutput: 'toast-notification.html',
    title: 'Toast Notifications Demo',
    navBarLabel: 'Toast',
    menu: menus.notifications,
    dependencies: {
      Toastr: { logic: true, styles: true },
      Fuxi: { styles: true }
    }
  },
  selectBoxAngular: {
    entry: './src/web-components/selectbox/demo/demo-angular.js',
    chunk: 'selectboxangular/selectboxangular',
    htmlTemplate: './src/web-components/selectbox/demo/selectbox-demo-angular.html',
    htmlOutput: 'selectbox-angular.html',
    title: 'SelectBox Demo (AngularJS Directive)',
    navBarLabel: 'Selectbox ( AngularJS Directive)',
    menu: menus.selectbox,
    dependencies: {
      Angular: { logic: true },
      Bootstrap: { styles: true },
      Select2: { logic: true, styles: true },
      Fuxi: { styles: true }
    }
  },
  'element-loading-notification': {
    entry: './src/utils/element-notification/demo/demo.js',
    chunk: 'el-notify/el-notify',
    htmlTemplate: './src/utils/element-notification/demo/element-notification-demo.html',
    htmlOutput: 'element-loading-notification.html',
    title: 'Element Notification Demo',
    menu: menus.notifications,
    navBarLabel: 'Element Block',
    dependencies: {
      Bootstrap: { styles: true }
    }
  },
  'page-loading-notification': {
    entry: './src/utils/page-notification/demo/demo.js',
    chunk: 'page-notify/page-notify',
    htmlTemplate: './src/utils/page-notification/demo/demo.html',
    htmlOutput: 'page-loading-notification.html',
    title: 'Page Notification Demo',
    navBarLabel: 'Page Block',
    menu: menus.notifications,
    dependencies: {
      Bootstrap: { logic: true, styles: true }
    }
  },

  breadcrumb: {
    entry: './src/web-components/breadcrumb/demo/breadcrumb-demo.js',
    chunk: 'breadcrumb/breadcrumb',
    htmlTemplate: './src/web-components/breadcrumb/demo/breadcrumb-demo.html',
    htmlOutput: 'breadcrumb.html',
    title: 'Breadcrumb Demo',
    navBarLabel: 'Breadcrumb',
    menu: menus.breadcrumb,
    dependencies: {
      Fuxi: { styles: true }
    }
  }
}

Object.values(pages).forEach((page) => {
  page.menu.subOptions = page.menu.subOptions || []
  page.menu.subOptions.push(page)
})

const dependenciesInfo = {
  dayjs: {
    label: 'Day.js',
    homepage: 'https://day.js.org/',
  },
  Bootstrap: {
    label: 'Bootstrap',
    homepage: 'https://getbootstrap.com/docs/3.3/',
  },
  Fuxi: {
    label: 'Fuxi v2',
    homepage: 'http://fuxi-server2.c.ptin.corppt.com/',
  },
  FontAwesome: {
    label: 'Font  Awesome 5 Free',
    homepage: 'https://fontawesome.com/v5/search',
  },

  datatables: {
    label: 'DataTables',
    homepage: 'https://datatables.net/',
  },
  Select2: {
    label: 'Select2',
    homepage: 'https://select2.org/',
  },
  Angular: {
    label: 'AngularJS',
    homepage: 'https://angularjs.org/',
  },
  DateTimePicker: {
    label: 'Bootstrap DateTimePicker',
    homepage: 'https://getdatepicker.com/4/',
  }
}

module.exports = env => {
  const mode = getMode(env)
  const outputDir = typeof env.outputDir === 'string' ? env.outputDir : 'demo'

  return {
    mode,
    entry: Object.values(pages)
      .reduce((acc, page) => {
        acc[page.chunk] = [].concat(page.entry, './src/docs/demo-common.js')
        return acc
      }, {
        demo: './src/docs/demo-common.js',
        demoIndex: './src/docs/demo-index.js'
      }),
    output: {
      path: path.resolve(__dirname, outputDir),
      filename: outputFilenameNameByMode[mode]
    },
    module: {
      rules: [
        ...configParts.module.rules,
        {
          test: { or: [/\.(element|template|directive)\.html$/, /\/implementation\.html$/] },
          include: function (resourcePath) {
            return resourcePath.includes('/node_modules/@alticelabsprojects/nossis-orchestration-web-components'.replace(/\//g, path.sep)) ||
                (!resourcePath.includes('/node_modules/') && !resourcePath.includes('/bower_components/'))
          },
          use: {
            loader: 'raw-loader',
          }
        }, {
          test: /\.css$/,
          use: [
            { loader: 'css-loader', options: { sourceMap: true } }
          ]
        }, {
          test: /\.s[ac]ss$/,
          exclude: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        },
        {
          test: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        }, {
          test: /\.(woff|woff2|eot|ttf|otf|wav|mp3|png|jpg|jpeg|gif|svg)(\?v=\d+\.\d+\.\d+)?$/,
          include: /node_modules/,
          exclude: /node_modules\/@fontsource/,
          loader: 'file-loader',
          options: {
            outputPath: 'na-portal-assets-vendors/vendors-assets',
            publicPath: 'vendors-assets'
          }
        },
        {
          test: /\.(woff|woff2|eot|ttf|otf|svg)$/,
          include: /node_modules\/@fontsource/,
          loader: 'file-loader',
          options: {
            outputPath: 'na-portal-assets-vendors/vendors-assets',
            publicPath: 'na-portal-assets-vendors/vendors-assets'
          }
        },
        {
          test: /\.(png|jpg|jpeg|gif|svg)$/,
          include: /src\/docs/,
          exclude: /(node_modules|bower_components)/,
          loader: 'file-loader',
          options: {
            name: '[name].[ext]',
            outputPath: 'demo-assets/',
            publicPath: 'demo-assets',
            esModule: false
          }
        },
        {
          test: /\.(png|jpeg)$/,
          include: /src\/web-components/,
          loader: 'file-loader',
          options: {
            name: '[name].[ext]',
            outputPath: 'na-ext',
            publicPath: 'na-ext'
          }
        }, {
          test: /\.(pt|en|fr)$/,
          exclude: /(node_modules|bower_components)/,
          use: {
            loader: 'raw-loader',
          }
        },
        {
          test: /\.adoc$/,
          exclude: /(node_modules|bower_components)/,
          use: [{
            loader: 'raw-loader',
          }, {
            loader: path.resolve(__dirname, 'webpack-adoc-loader.js'),
            options: {
              safe: 'safe',
              to_file: false,
              standalone: false,
              attributes: { 'data-uri': true, 'allow-uri-read': true }
            }
          }]
        }]
    },
    resolve: {
      ...configParts.resolve,
      alias: {
        ...configParts.resolve.alias,
        '~basemodule-play-conf': path.resolve(__dirname, '../play/basemodule/conf'),
        '~tsc-catalog-play-conf': path.resolve(__dirname, '../play/tsc-catalog/conf'),
      }
    },
    optimization: {
      ...configParts.optimization,
      splitChunks: {
        // include all types of chunks
        cacheGroups: {

          ...configParts.optimization.splitChunks.cacheGroups,

          // Demo specific vendors, used to identify the css and js origins

          'vendors-angular': {
            test: /[\\/]node_modules[\\/]angular[\\/]/,
            name: 'na-portal-assets-vendors/vendors-angular',
            chunks: 'all',
            priority: 20,
            enforce: true
          },
          'vendors-fontsource': {
            test: /[\\/]node_modules[\\/]@fontsource[\\/]/,
            name: 'na-portal-assets-vendors/vendors-fontsource',
            chunks: 'all',
            priority: 20,
            enforce: true
          },
        }
      }
    },

    plugins: [
      new JsonModuleDependenciesGraphPlugin(),

      new HtmlWebpackPlugin({
        inject: false,
        chunks: ['demoIndex'],
        filename: 'index.html',
        template: './src/docs/demo-index.html',
        templateParameters: (compilation, assets) => {
          const { js, css } = assets
          // prevents FOUC (flash of unstyled content)
          const noFOUCStyle = '<style class="no-fouc-style">html { visibility: hidden; opacity: 0;} </style>'
          return {
            js: js.map(js => `<script defer src="${js}"></script>`).join('\n'),
            css: noFOUCStyle + orderedCss(css).map(css => `<link rel="stylesheet" href="${css}">`).join('\n'),
            navbar: navBarHtml(''),
            title: 'NA Portal Demos'
          }
        }
      }),

      webPackHooksPlugin({
        compile: () => {
          const i18nFromProductFolder = path.resolve(__dirname, 'src/docs/i18n/from-product')
          if (!fs.existsSync(i18nFromProductFolder)) {
            shell.mkdir('-p', i18nFromProductFolder)
          }
          const i18nFileStatus = {
            copiedFiles: 0,
            uptoDateFiles: 0
          }

          const i18nJinjaFoldersToImport = [
            '../../dist/docker/frontend-portal/src/main/jinja/files/na-portal/conf/i18n/fulfillment',
          ]
          for (const folder of i18nJinjaFoldersToImport) {
            const sourcePath = path.resolve(__dirname, folder)
            getFilesOfDir(sourcePath, {
              patterns: [/.*\.pt\.j2$/, /.*\.en\.j2$/, /.*\.fr\.j2$/]
            }).forEach(file => {
              const relativePath = path.relative(sourcePath, file.absolutePath)
              const destination = path.resolve(i18nFromProductFolder, relativePath).slice(0, -3) // .slice removes ".j2"
              if (fs.existsSync(destination)) {
                const fstat = fs.statSync(destination)
                if (fstat.mtime > file.modified) {
                  // No need to copy updated files
                  i18nFileStatus.uptoDateFiles++
                  return
                }
              }
              const content = fs.readFileSync(file.absolutePath, { encoding: 'utf8' })
              const replacedContent = content.replaceAll(/{{[^}]+}}/g, '')
              ensureDirectoryExistsOnFile(destination)
              fs.writeFileSync(destination, replacedContent, { encoding: 'utf8' })
              i18nFileStatus.copiedFiles++
            })
          }

          const i18nFoldersToImport = [
            '../../dist/docker/frontend-base/src/main/jinja/files/na-portal/conf/i18n/product',
            '../../dist/docker/frontend-mpt/src/main/jinja/files/na-portal/conf/i18n/product',
            '../../dist/docker/frontend-go/src/main/jinja/files/na-portal/conf/i18n/product',
            '../../dist/docker/frontend-veacs/src/main/jinja/files/na-portal/conf/i18n/customization',
            '../../dist/docker/frontend-monitoring/src/main/jinja/files/na-portal/conf/i18n/product',
            '../../dist/docker/frontend-operations-catalog/src/main/jinja/files/na-portal/conf/i18n/product',
            '../../dist/docker/frontend-reference-data/src/main/jinja/files/na-portal/conf/i18n/product',
            '../../dist/docker/frontend-diagnostics/src/main/jinja/files/na-portal/conf/i18n/product'
          ]
          for (const folder of i18nFoldersToImport) {
            const sourcePath = path.resolve(__dirname, folder)
            getFilesOfDir(sourcePath, {
              patterns: [/.*\.pt$/, /.*\.en$/, /.*\.fr$/]
            }).forEach(file => {
              const relativePath = path.relative(sourcePath, file.absolutePath)
              const destination = path.resolve(i18nFromProductFolder, relativePath)
              if (fs.existsSync(destination)) {
                const fstat = fs.statSync(destination)
                if (fstat.mtime > file.modified) {
                  // No need to copy updated files
                  i18nFileStatus.uptoDateFiles++
                  return
                }
              }
              ensureDirectoryExistsOnFile(destination)
              fs.copyFileSync(file.absolutePath, destination)
              i18nFileStatus.copiedFiles++
            })
          }
          console.log('build-demo: i18n files: copied %d files, %d files up to date', i18nFileStatus.copiedFiles, i18nFileStatus.uptoDateFiles)
        },
        afterRun: () => {
          const nowcInputDir = path.join(__dirname, 'node_modules/@alticelabsprojects/nossis-orchestration-web-components/dist/docs')
          const nowcOutputDir = path.join(__dirname, outputDir, 'nowc')
          
          // Check if symlink exists and is broken (target doesn't exist)
          if (fs.existsSync(nowcOutputDir)) {
            try {
              const linkTarget = fs.readlinkSync(nowcOutputDir)
              // If it's a symlink but target doesn't exist, remove it
              if (!fs.existsSync(path.resolve(path.dirname(nowcOutputDir), linkTarget))) {
                fs.unlinkSync(nowcOutputDir)
              }
            } catch (e) {
              // Not a symlink, check if it needs to be replaced
              if (!fs.existsSync(nowcInputDir)) {
                return // Source doesn't exist, skip
              }
            }
          }
          
          // Function to wait for docs to be available and create symlink
          const createSymlinkWhenReady = (retries = 0) => {
            if (!fs.existsSync(nowcOutputDir) && fs.existsSync(nowcInputDir)) {
              if (mode === DEVELOPMENT) {
                // no need to constantly copy the docs contents if it is just for development,
                // just a symlink is enough
                fs.symlinkSync(nowcInputDir, nowcOutputDir)
              } else {
                fs.cpSync(nowcInputDir, nowcOutputDir, { recursive: true })
              }
            } else if (!fs.existsSync(nowcInputDir) && retries < 10) {
              // NOWC might be rebuilding, wait a bit and retry
              setTimeout(() => createSymlinkWhenReady(retries + 1), 500)
            }
          }
          
          createSymlinkWhenReady()
        }
      }),
      ...Object.values(pages).map(page => new HtmlWebpackPlugin({
        chunks: [page.chunk],
        inject: false,
        favicon: './src/docs/favicon.ico',
        filename: page.htmlOutput,
        template: page.htmlTemplate,
        minify: false,
        chunksSortMode: 'manual',
        templateParameters: (compilation, assets) => {
          const { js, css } = assets

          // prevents FOUC (flash of unstyled content)
          const noFOUCStyle = '<style class="no-fouc-style">html { visibility: hidden; opacity: 0;} </style>'
          return {
            js: js.map(js => `<script src="${js}"></script>`).join('\n'),
            css: noFOUCStyle + orderedCss(css).map(css => `<link rel="stylesheet" href="${css}">`).join('\n'),
            navbar: navBarHtml(page.htmlOutput),
            title: page.title,
            dependencyTable: dependencyTable(page.dependencies)
          }
        }
      })),
      new webpack.DefinePlugin({
        setImmediate: 'requestAnimationFrame'
      }),
      new webpack.ProvidePlugin({
        $: 'jquery',
        jQuery: 'jquery',
        process: 'process',
        'window.jQuery': 'jquery',
        Buffer: ['buffer', 'Buffer'],
      })
    ],

    performance: {
      hints: mode === PRODUCTION ? 'warning' : false,
      maxEntrypointSize: 1024000
    },
  }
}

// constants
const DEVELOPMENT = 'development'
const PRODUCTION = 'production'

const outputFilenameNameByMode = {
  [DEVELOPMENT]: '[name].js',
  [PRODUCTION]: '[name].[contenthash].min.js'
}

/*
 * Guarantees that the result is either "development" or "production"
 */
function getMode({ NODE_ENV }) {
  return typeof NODE_ENV === 'string' && NODE_ENV.toLowerCase() === PRODUCTION ? PRODUCTION : DEVELOPMENT
}

function dependencyTable(dependencyMap = {}) {
  const htmlEscape = require('lodash/escape')
  const okSignHtml = '<i class="glyphicon glyphicon-ok"></i>'
  const dependencies = Object.keys(dependencyMap).map(name => ({ name: name.replace(/^([a-z])/g, $1 => $1.toUpperCase()), ...dependenciesInfo[name], ...dependencyMap[name] }))
  dependencies.sort((a, b) => a.name.localeCompare(b.name))
  const rows = dependencies.map(dependency => {
    const label = htmlEscape(dependency.label ?? dependency.name)
    const labelHtml = dependency.homepage ? `<a href="${htmlEscape(dependency.homepage)}">${label}</a>` : label
    return `<tr><th>${labelHtml}</th><th>${dependency.logic === true ? okSignHtml : ''}</th><th>${dependency.styles === true ? okSignHtml : ''}</th></tr>`
  }).join('')

  const emptyTableRow = '<tr><td colSpan="3" class="dataTables_empty"><i class="fx-aux-text">No dependencies</i></td></tr>'

  return `<table class="table dataTable">
            <thead><tr><th>Dependency</th><th>Logic</th><th>Style</th></tr></thead>
            <tbody>${rows || emptyTableRow}</tbody>
        </table>`
}

function orderedCss(cssList) {
  const demoCss = cssList.filter(css => css.includes('demo-assets/'))
  const vendorsCss = cssList.filter(css => css.includes('na-portal-assets-vendors/'))
  const vendorFuxiCss = vendorsCss.filter(css => css.includes('na-portal-assets-vendors/vendors-fuxi'))
  const otherVendorsCss = vendorsCss.filter(css => !vendorFuxiCss.includes(css))
  const otherCss = cssList.filter(css => !vendorsCss.includes(css) && !demoCss.includes(css))
  return [...demoCss, ...otherVendorsCss, ...vendorFuxiCss, ...otherCss]
}

function navBarHtml(activePage) {
  const options = Object.values(menus).filter(option => option.subOptions != null)

  const menuGroupsInfo = options.reduce((acc, option) => {
    if (option.group && option.group.label) {
      acc[option.group.label] = {
        active: false,
        htmlList: []
      }
    }
    acc[option.label] = {
      active: false,
      htmlList: []
    }
    return acc
  }, {})

  options.forEach((option) => {
    const { group, subOptions } = option
    const isActive = subOptions.some(suboption => suboption.htmlOutput === activePage)
    const isInGroup = group && group.label != null
    const groupLabel = isInGroup ? group.label : option.label
    const hasDropdown = subOptions.length > 1

    const subOptionTemplate = (subOption) => {
      const isActive = subOption.htmlOutput === activePage
      if (isActive) {
        return `<li class="active"><a>${subOption.navBarLabel}</a></li>`
      } else {
        return `<li><a href="${subOption.htmlOutput}">${subOption.navBarLabel}</a></li>`
      }
    }

    const dropdownClass = isInGroup ? 'dropdown-submenu' : 'dropdown'
    const caret = isInGroup ? '' : '<span class="caret"></span>'

    const optionHtml = (() => {
      if (hasDropdown && isActive) {
        return `<li class="active ${dropdownClass}">
            <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">${option.label}${caret}</a>
            <ul class="dropdown-menu">
                ${subOptions.map(subOptionTemplate).join('')}
            </ul>
            </li>`
      }
      if (hasDropdown && !isActive) {
        return `<li class="${dropdownClass}">
            <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">${option.label}${caret}</a>
            <ul class="dropdown-menu">
                ${subOptions.map(subOptionTemplate).join('')}
            </ul>
            </li>`
      }
      if (!hasDropdown && isActive) {
        return `<li class="active"><a>${option.label}</a></li>`
      }
      if (!hasDropdown && !isActive) {
        return `<li><a href="${subOptions[0].htmlOutput}">${option.label}</a></li>`
      }
      return ''
    })()

    const groupInfo = menuGroupsInfo[groupLabel]
    groupInfo.active = groupInfo.active || isActive
    groupInfo.htmlList.push(optionHtml)
  })

  const menuGroupsHtml = Object.entries(menuGroupsInfo).reduce((acc, [key, value]) => {
    const { htmlList, active } = value
    if (htmlList.length <= 0) {
      return acc
    } else if (htmlList.length === 1) {
      return acc + htmlList[0]
    } else {
      const activeClass = active ? 'active' : ''
      return acc + `<li class=" ${activeClass} dropdown">
            <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">${key}<span class="caret"></span></a>
            <ul class="dropdown-menu">
                ${htmlList.join('')}
            </ul>
            </li>`
    }
  }, '')

  return `<nav class="fx-main-nav fx-main-nav-fixed navbar-default">
    <div class="navbar-header">
        <button type="button" data-target="#navbarCollapse" data-toggle="collapse" class="navbar-toggle">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
        </button>
        <a href="./" class="navbar-brand"></a>
        <a href="./" class="navbar-brand" style="width: auto; background: none"><span>NA Portal Demos</span></a>
    </div>
    <div id="navbarCollapse" class="collapse navbar-collapse fx-classic-menu">
        <ul class="nav navbar-nav">${menuGroupsHtml}</ul>
    </div>
    <ul class="nav navbar-nav navbar-right fx-navbar-secondary">
          <li><a href="./nowc/">To NOWC Documentation</a></li>
          <li class="nav-item">
              <form class="navbar-form form-inline">
                  <label for="app-theme-select"><span class="nav-link">Theme:</span></label>
                  <select id="app-theme-select" class="form-control app-theme-select"><option>Fuxi</option><option>Nossis One</option></select>
              </form>
          </li>
      </ul>
</nav>`
}

function registerRequireContext() {
  // require.context polyfill for Node.js environment
// This condition actually should detect if it's a Node environment
  if (typeof require.context === 'undefined') {
    const fs = require('node:fs')
    const path = require('node:path')

    require.context = (base = '.', scanSubDirectories = false, regularExpression = /\.js$/) => {
      const files = {}

      function readDirectory (directory) {
        fs.readdirSync(directory).forEach((file) => {
          const fullPath = path.resolve(directory, file)
          if (fs.statSync(fullPath).isDirectory()) {
            if (scanSubDirectories) readDirectory(fullPath)
            return
          }
          if (!regularExpression.test(fullPath)) return
          files[fullPath] = true
        })
      }
      readDirectory(path.resolve(__dirname, base))
      function Module (file) {
        return require(file)
      }
      Module.keys = () => Object.keys(files)
      return Module
    }
  }
}

class JsonModuleDependenciesGraphPlugin {
  constructor(options = {}) {
    const defaultOptions = {
      outputFile: 'module-graph.json',
    }
    this.options = { ...defaultOptions, ...options }
    this.moduleDependencyGraph = {}
    this.cache = Symbol('cache')
  }

  apply (compiler) {
    const outputPath = compiler.options.output.path

    compiler.hooks.compilation.tap('JsonModuleDependenciesGraphPlugin', compilation => {
      compilation.hooks.finishModules.tap('JsonModuleDependenciesGraphPlugin', modules => {
        const moduleGraph = compilation.moduleGraph
        const { moduleDependencyGraph } = this
        if (moduleDependencyGraph[this.cache] == null) {
          moduleDependencyGraph[this.cache] = { nodes: new Set() }
        }
        const cache = moduleDependencyGraph[this.cache]
        const nodes = moduleDependencyGraph.nodes || []
        const edges = moduleDependencyGraph.edges || []

        modules.forEach(module => {
          const { resource, dependencies, blocks } = module

          if (!resource) {
            return
          }

          const moduleFilePath = path.relative(__dirname, resource)
          const moduleFileName = path.basename(resource)
          if (cache.nodes.has(moduleFilePath)) {
            return
          }
          cache.nodes.add(moduleFilePath)
          nodes.push({
            file: moduleFileName,
            path: moduleFilePath
          })

          if (resource.includes('/node_modules/')) {
            return
          }

          const addEdge = (dependency, async) => {
            const { request } = dependency
            const edge = {
              src: moduleFilePath,
              request,
              async
            }
            if (request == null || request.includes('/node_modules/css-loader/') || edgesCache.has(request)) {
              return
            }
            edgesCache.add(request)
            edges.push(edge)
            const dependencyConnection = moduleGraph.getConnection(dependency)
            const dependencyResource = dependencyConnection && dependencyConnection.module && dependencyConnection.module.resource
            if (dependencyResource != null) {
              edge.dest = path.relative(__dirname, dependencyResource)
            }
          }

          const edgesCache = new Set()
          dependencies.forEach(dependency => addEdge(dependency, false))
          blocks.flatMap(block => block.dependencies).forEach(dependency => addEdge(dependency, true))
        })
        moduleDependencyGraph.nodes = nodes
        moduleDependencyGraph.edges = edges
      })
    })

    compiler.hooks.afterEmit.tap('JsonModuleDependenciesGraphPlugin', () => {
      const { moduleDependencyGraph, options } = this
      const outputFilePath = path.resolve(outputPath, options.outputFile)
      const { nodes, edges } = moduleDependencyGraph
      const edgesDests = new Set(edges.map(({ dest }) => dest))
      const moduleNodes = nodes
        .filter(({ path }) => !path.includes('node_modules/') || edgesDests.has(path))
        .map((node, index) => ({ id: index, ...node }))
      const nodePathIdMap = Object.fromEntries(moduleNodes.map(node => [node.path, node.id]))
      const content = moduleNodes.map(node => ({
        ...node,
        dependsOn: edges.filter(({ src, async }) => !async && src === node.path).map(edge => nodePathIdMap[edge.dest]),
        dependsOnAsync: edges.filter(({ src, async }) => async && src === node.path).map(edge => nodePathIdMap[edge.dest])
      }))

      const outputFileContent = JSON.stringify(content)
      this.moduleDependencyGraph = {}
      fs.writeFile(outputFilePath, outputFileContent, (err) => {
        if (err) throw err
        console.log(`${outputFilePath} module graph generated`)
      })
    })
  }
}

/*
 * Small plugin to add hooks to webpack compilation process.
 * Used to execute the hooks every time the build is run during watch mode
 */
function webPackHooksPlugin({ beforeRun, compile, afterRun }) {
  return {
    apply: (compiler) => {
      beforeRun && compiler.hooks.beforeRun.tap('beforeRunPlugin', beforeRun)
      compile && compiler.hooks.compile.tap('compilePlugin', compile)
      afterRun && compiler.hooks.afterEmit.tap('AfterEmitPlugin', afterRun)
    }
  }
}

/**
 * Gets all files information in a specific folder including its sub folders
 *
 * @param {string} source - target directory
 * @param {object} [options]
 * @param {RegExp[]} [options.patterns] - filtering patterns, get all files if absent or empty
 * @return {{absolutePath: string,  dir: string, name: string, created: string, modified: number}[]} info of all files in target directory
 */
const getFilesOfDir = (source, { patterns = [] } = {}) => {
  const files = []
  function getFiles(dir) {
    fs.readdirSync(dir).forEach(file => {
      const absolutePath = path.join(dir, file)

      const stats = fs.statSync(absolutePath)
      if (fs.statSync(absolutePath).isDirectory()) {
        return getFiles(absolutePath)
      } else if (patterns && patterns.length > 0 && patterns.every(pattern => !pattern.test(absolutePath))) {
        // file is ignored: do nothing
      } else {
        const modified = {
          absolutePath,
          dir,
          name: file,
          created: stats.birthtime,
          modified: stats.mtime
        }
        return files.push(modified)
      }
    })
  }
  getFiles(path.resolve(__dirname, source))
  return files
}

function ensureDirectoryExistsOnFile(filePath) {
  const dirname = path.dirname(filePath)
  if (fs.existsSync(dirname)) {
    return true
  }
  ensureDirectoryExistsOnFile(dirname)
  fs.mkdirSync(dirname)
}
