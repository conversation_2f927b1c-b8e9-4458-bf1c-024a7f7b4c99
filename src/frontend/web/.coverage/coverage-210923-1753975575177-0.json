{"result": [{"scriptId": "6", "url": "node:internal/per_context/primordials", "functions": [{"functionName": "SafeIterator", "ranges": [{"startOffset": 9149, "endOffset": 9220, "count": 6}], "isBlockCoverage": false}, {"functionName": "next", "ranges": [{"startOffset": 9225, "endOffset": 9274, "count": 80}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9279, "endOffset": 9324, "count": 5}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10889, "endOffset": 10956, "count": 4}], "isBlockCoverage": false}, {"functionName": "SafeMap", "ranges": [{"startOffset": 11614, "endOffset": 11642, "count": 8}], "isBlockCoverage": false}, {"functionName": "SafeSet", "ranges": [{"startOffset": 11943, "endOffset": 11971, "count": 7}], "isBlockCoverage": false}, {"functionName": "SafeFinalizationRegistry", "ranges": [{"startOffset": 12395, "endOffset": 12451, "count": 2}], "isBlockCoverage": false}, {"functionName": "SafeWeakRef", "ranges": [{"startOffset": 12604, "endOffset": 12642, "count": 7}], "isBlockCoverage": false}]}, {"scriptId": "9", "url": "node:internal/bootstrap/realm", "functions": [{"functionName": "internalBinding", "ranges": [{"startOffset": 5981, "endOffset": 6243, "count": 26}], "isBlockCoverage": false}, {"functionName": "canBeRequiredByUsers", "ranges": [{"startOffset": 9476, "endOffset": 9551, "count": 2}], "isBlockCoverage": true}, {"functionName": "canBeRequiredWithoutScheme", "ranges": [{"startOffset": 9562, "endOffset": 9656, "count": 8}], "isBlockCoverage": true}, {"functionName": "normalizeRequirableId", "ranges": [{"startOffset": 9667, "endOffset": 10012, "count": 5}, {"startOffset": 9743, "endOffset": 9905, "count": 0}, {"startOffset": 9961, "endOffset": 9985, "count": 1}, {"startOffset": 9985, "endOffset": 10011, "count": 4}], "isBlockCoverage": true}, {"functionName": "getCanBeRequiredByUsersWithoutSchemeList", "ranges": [{"startOffset": 10275, "endOffset": 10384, "count": 1}], "isBlockCoverage": true}, {"functionName": "compileForPublicLoader", "ranges": [{"startOffset": 10542, "endOffset": 11205, "count": 1}, {"startOffset": 10623, "endOffset": 10815, "count": 0}, {"startOffset": 11138, "endOffset": 11142, "count": 0}], "isBlockCoverage": true}, {"functionName": "compileForInternalLoader", "ranges": [{"startOffset": 12487, "endOffset": 13309, "count": 188}], "isBlockCoverage": false}, {"functionName": "requireBuiltin", "ranges": [{"startOffset": 13504, "endOffset": 13869, "count": 187}], "isBlockCoverage": false}]}, {"scriptId": "10", "url": "node:internal/errors", "functions": [{"functionName": "setInternalPrepareStackTrace", "ranges": [{"startOffset": 2771, "endOffset": 2862, "count": 1}], "isBlockCoverage": true}, {"functionName": "wrappedFn", "ranges": [{"startOffset": 14690, "endOffset": 14896, "count": 147}], "isBlockCoverage": false}]}, {"scriptId": "11", "url": "node:internal/assert", "functions": [{"functionName": "assert", "ranges": [{"startOffset": 157, "endOffset": 307, "count": 9}, {"startOffset": 205, "endOffset": 305, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "12", "url": "node:internal/bootstrap/node", "functions": [{"functionName": "get", "ranges": [{"startOffset": 3830, "endOffset": 3880, "count": 4}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3886, "endOffset": 3944, "count": 1}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 4128, "endOffset": 4547, "count": 1}, {"startOffset": 4187, "endOffset": 4469, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 13230, "endOffset": 13266, "count": 4}], "isBlockCoverage": true}]}, {"scriptId": "13", "url": "node:internal/timers", "functions": [{"functionName": "", "ranges": [{"startOffset": 4385, "endOffset": 4410, "count": 1}], "isBlockCoverage": true}, {"functionName": "initAsyncResource", "ranges": [{"startOffset": 5763, "endOffset": 6041, "count": 1}, {"startOffset": 5989, "endOffset": 6039, "count": 0}], "isBlockCoverage": true}, {"functionName": "Timeout", "ranges": [{"startOffset": 6147, "endOffset": 7193, "count": 1}, {"startOffset": 6295, "endOffset": 6647, "count": 0}, {"startOffset": 7004, "endOffset": 7011, "count": 0}], "isBlockCoverage": true}, {"functionName": "TimersList", "ranges": [{"startOffset": 7935, "endOffset": 8241, "count": 1}], "isBlockCoverage": true}, {"functionName": "incRefCount", "ranges": [{"startOffset": 9618, "endOffset": 9784, "count": 1}], "isBlockCoverage": true}, {"functionName": "decRefCount", "ranges": [{"startOffset": 9786, "endOffset": 9953, "count": 1}], "isBlockCoverage": true}, {"functionName": "insert", "ranges": [{"startOffset": 11102, "endOffset": 11841, "count": 1}], "isBlockCoverage": true}, {"functionName": "setPosition", "ranges": [{"startOffset": 12905, "endOffset": 12976, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "14", "url": "node:internal/async_hooks", "functions": [{"functionName": "newAsyncId", "ranges": [{"startOffset": 14549, "endOffset": 14619, "count": 5}], "isBlockCoverage": true}, {"functionName": "getDefaultTriggerAsyncId", "ranges": [{"startOffset": 14994, "endOffset": 15284, "count": 5}, {"startOffset": 15208, "endOffset": 15250, "count": 4}, {"startOffset": 15250, "endOffset": 15283, "count": 1}], "isBlockCoverage": true}, {"functionName": "defaultTriggerAsyncIdScope", "ranges": [{"startOffset": 15601, "endOffset": 16094, "count": 2}, {"startOffset": 15711, "endOffset": 15742, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasHooks", "ranges": [{"startOffset": 16096, "endOffset": 16159, "count": 22}], "isBlockCoverage": true}, {"functionName": "enabledHooksExist", "ranges": [{"startOffset": 16161, "endOffset": 16220, "count": 4}], "isBlockCoverage": true}, {"functionName": "initHooksExist", "ranges": [{"startOffset": 16222, "endOffset": 16277, "count": 5}], "isBlockCoverage": true}, {"functionName": "destroyHooksExist", "ranges": [{"startOffset": 16338, "endOffset": 16399, "count": 5}], "isBlockCoverage": true}, {"functionName": "emitBeforeScript", "ranges": [{"startOffset": 16890, "endOffset": 17066, "count": 4}, {"startOffset": 17038, "endOffset": 17064, "count": 0}], "isBlockCoverage": true}, {"functionName": "emitAfterScript", "ranges": [{"startOffset": 17069, "endOffset": 17189, "count": 4}, {"startOffset": 17133, "endOffset": 17158, "count": 0}], "isBlockCoverage": true}, {"functionName": "pushAsyncContext", "ranges": [{"startOffset": 17534, "endOffset": 18104, "count": 4}, {"startOffset": 17754, "endOffset": 17804, "count": 0}], "isBlockCoverage": true}, {"functionName": "popAsyncContext", "ranges": [{"startOffset": 18169, "endOffset": 18780, "count": 4}, {"startOffset": 18285, "endOffset": 18298, "count": 0}, {"startOffset": 18377, "endOffset": 18483, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "15", "url": "node:internal/validators", "functions": [{"functionName": "isInt32", "ranges": [{"startOffset": 1087, "endOffset": 1146, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4381, "endOffset": 4493, "count": 124}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5898, "endOffset": 6012, "count": 1}, {"startOffset": 5955, "endOffset": 6010, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12161, "endOffset": 12522, "count": 1}, {"startOffset": 12422, "endOffset": 12435, "count": 0}, {"startOffset": 12438, "endOffset": 12501, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13109, "endOffset": 13225, "count": 14}], "isBlockCoverage": false}]}, {"scriptId": "16", "url": "node:internal/util", "functions": [{"functionName": "getDeprecationWarningEmitter", "ranges": [{"startOffset": 2398, "endOffset": 3047, "count": 1}], "isBlockCoverage": false}, {"functionName": "shouldEmitW<PERSON>ning", "ranges": [{"startOffset": 2497, "endOffset": 2507, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2544, "endOffset": 3044, "count": 0}], "isBlockCoverage": false}, {"functionName": "deprecate", "ranges": [{"startOffset": 3796, "endOffset": 4850, "count": 1}], "isBlockCoverage": false}, {"functionName": "deprecated", "ranges": [{"startOffset": 4160, "endOffset": 4462, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 5373, "endOffset": 5504, "count": 3}, {"startOffset": 5443, "endOffset": 5461, "count": 0}, {"startOffset": 5477, "endOffset": 5503, "count": 0}], "isBlockCoverage": true}, {"functionName": "exposeInterface", "ranges": [{"startOffset": 16032, "endOffset": 16252, "count": 1}], "isBlockCoverage": false}, {"functionName": "defineLazyProperties", "ranges": [{"startOffset": 16751, "endOffset": 17684, "count": 1}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 16970, "endOffset": 17113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17216, "endOffset": 17412, "count": 0}], "isBlockCoverage": false}, {"functionName": "defineReplaceableLazyAttribute", "ranges": [{"startOffset": 17686, "endOffset": 18586, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17898, "endOffset": 18134, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 18239, "endOffset": 18310, "count": 0}], "isBlockCoverage": false}, {"functionName": "exposeLazyInterfaces", "ranges": [{"startOffset": 18588, "endOffset": 18688, "count": 1}], "isBlockCoverage": false}, {"functionName": "setOwnProperty", "ranges": [{"startOffset": 19883, "endOffset": 20073, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22175, "endOffset": 22303, "count": 8}, {"startOffset": 22219, "endOffset": 22281, "count": 2}], "isBlockCoverage": true}, {"functionName": "setupCoverageHooks", "ranges": [{"startOffset": 22431, "endOffset": 23108, "count": 1}, {"startOffset": 22901, "endOffset": 23078, "count": 0}], "isBlockCoverage": true}, {"functionName": "guessHandleType", "ranges": [{"startOffset": 24498, "endOffset": 24595, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 24597, "endOffset": 25141, "count": 7}], "isBlockCoverage": false}, {"functionName": "WeakReference", "ranges": [{"startOffset": 24672, "endOffset": 24739, "count": 7}], "isBlockCoverage": true}, {"functionName": "incRef", "ranges": [{"startOffset": 24743, "endOffset": 24961, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 24965, "endOffset": 25093, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 25097, "endOffset": 25139, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "17", "url": "node:internal/options", "functions": [{"functionName": "getCLIOptionsFromBinding", "ranges": [{"startOffset": 495, "endOffset": 623, "count": 63}, {"startOffset": 553, "endOffset": 599, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEmbedderOptions", "ranges": [{"startOffset": 745, "endOffset": 889, "count": 5}, {"startOffset": 801, "endOffset": 861, "count": 1}], "isBlockCoverage": true}, {"functionName": "refreshOptions", "ranges": [{"startOffset": 891, "endOffset": 947, "count": 1}], "isBlockCoverage": true}, {"functionName": "getOptionValue", "ranges": [{"startOffset": 949, "endOffset": 1072, "count": 63}], "isBlockCoverage": true}]}, {"scriptId": "18", "url": "node:internal/util/types", "functions": [{"functionName": "isUint8Array", "ranges": [{"startOffset": 236, "endOffset": 342, "count": 5}], "isBlockCoverage": true}]}, {"scriptId": "19", "url": "node:internal/linkedlist", "functions": [{"functionName": "remove", "ranges": [{"startOffset": 260, "endOffset": 487, "count": 2}], "isBlockCoverage": true}, {"functionName": "append", "ranges": [{"startOffset": 543, "endOffset": 995, "count": 1}, {"startOffset": 594, "endOffset": 611, "count": 0}], "isBlockCoverage": true}, {"functionName": "isEmpty", "ranges": [{"startOffset": 997, "endOffset": 1057, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "20", "url": "node:internal/priority_queue", "functions": [{"functionName": "insert", "ranges": [{"startOffset": 692, "endOffset": 875, "count": 1}, {"startOffset": 826, "endOffset": 843, "count": 0}], "isBlockCoverage": true}, {"functionName": "percolateUp", "ranges": [{"startOffset": 1646, "endOffset": 2134, "count": 1}, {"startOffset": 1821, "endOffset": 2043, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeAt", "ranges": [{"startOffset": 2138, "endOffset": 2468, "count": 1}, {"startOffset": 2296, "endOffset": 2310, "count": 0}, {"startOffset": 2312, "endOffset": 2464, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "22", "url": "node:internal/util/debuglog", "functions": [{"functionName": "initializeDebugEnv", "ranges": [{"startOffset": 503, "endOffset": 976, "count": 1}, {"startOffset": 595, "endOffset": 932, "count": 0}], "isBlockCoverage": true}, {"functionName": "testEnabled", "ranges": [{"startOffset": 870, "endOffset": 927, "count": 0}], "isBlockCoverage": false}, {"functionName": "testEnabled", "ranges": [{"startOffset": 958, "endOffset": 969, "count": 6}], "isBlockCoverage": true}, {"functionName": "noop", "ranges": [{"startOffset": 1404, "endOffset": 1412, "count": 37}], "isBlockCoverage": true}, {"functionName": "debuglogImpl", "ranges": [{"startOffset": 1533, "endOffset": 2091, "count": 6}, {"startOffset": 1608, "endOffset": 2063, "count": 4}, {"startOffset": 1627, "endOffset": 2016, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1716, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "debuglog", "ranges": [{"startOffset": 2311, "endOffset": 3337, "count": 3}], "isBlockCoverage": false}, {"functionName": "init", "ranges": [{"startOffset": 2342, "endOffset": 2438, "count": 6}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 2453, "endOffset": 2847, "count": 6}, {"startOffset": 2700, "endOffset": 2730, "count": 2}, {"startOffset": 2737, "endOffset": 2776, "count": 3}, {"startOffset": 2783, "endOffset": 2837, "count": 1}], "isBlockCoverage": true}, {"functionName": "test", "ranges": [{"startOffset": 2877, "endOffset": 2946, "count": 0}], "isBlockCoverage": false}, {"functionName": "logger", "ranges": [{"startOffset": 2965, "endOffset": 3160, "count": 6}, {"startOffset": 3013, "endOffset": 3043, "count": 2}, {"startOffset": 3050, "endOffset": 3089, "count": 3}, {"startOffset": 3096, "endOffset": 3150, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3231, "endOffset": 3265, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "23", "url": "node:events", "functions": [{"functionName": "EventEmitter", "ranges": [{"startOffset": 6575, "endOffset": 6644, "count": 2}], "isBlockCoverage": false}, {"functionName": "checkListener", "ranges": [{"startOffset": 8095, "endOffset": 8173, "count": 9}], "isBlockCoverage": false}, {"functionName": "hasEventListener", "ranges": [{"startOffset": 8446, "endOffset": 8625, "count": 1}, {"startOffset": 8516, "endOffset": 8550, "count": 0}], "isBlockCoverage": true}, {"functionName": "EventEmitter.init", "ranges": [{"startOffset": 10152, "endOffset": 10820, "count": 2}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 13617, "endOffset": 15920, "count": 10}], "isBlockCoverage": false}, {"functionName": "_addListener", "ranges": [{"startOffset": 15923, "endOffset": 17839, "count": 7}], "isBlockCoverage": false}, {"functionName": "addListener", "ranges": [{"startOffset": 18017, "endOffset": 18109, "count": 7}], "isBlockCoverage": false}, {"functionName": "onceWrapper", "ranges": [{"startOffset": 18502, "endOffset": 18766, "count": 1}, {"startOffset": 18704, "endOffset": 18764, "count": 0}], "isBlockCoverage": true}, {"functionName": "_onceWrap", "ranges": [{"startOffset": 18768, "endOffset": 19009, "count": 1}], "isBlockCoverage": true}, {"functionName": "once", "ranges": [{"startOffset": 19200, "endOffset": 19326, "count": 1}], "isBlockCoverage": true}, {"functionName": "removeListener", "ranges": [{"startOffset": 19936, "endOffset": 21299, "count": 1}, {"startOffset": 20059, "endOffset": 20071, "count": 0}, {"startOffset": 20170, "endOffset": 20199, "count": 0}, {"startOffset": 20313, "endOffset": 20564, "count": 0}, {"startOffset": 20572, "endOffset": 21273, "count": 0}], "isBlockCoverage": true}, {"functionName": "listenerCount", "ranges": [{"startOffset": 24551, "endOffset": 25256, "count": 1}], "isBlockCoverage": false}, {"functionName": "arrayClone", "ranges": [{"startOffset": 25508, "endOffset": 25981, "count": 2}], "isBlockCoverage": false}]}, {"scriptId": "25", "url": "node:buffer", "functions": [{"functionName": "get", "ranges": [{"startOffset": 8395, "endOffset": 8423, "count": 1}], "isBlockCoverage": true}, {"functionName": "isEncoding", "ranges": [{"startOffset": 16155, "endOffset": 16306, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "26", "url": "node:internal/buffer", "functions": [{"functionName": "FastBuffer", "ranges": [{"startOffset": 26365, "endOffset": 26465, "count": 1}], "isBlockCoverage": false}, {"functionName": "reconnectZeroFillToggle", "ranges": [{"startOffset": 30682, "endOffset": 30754, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "28", "url": "node:internal/process/per_thread", "functions": [{"functionName": "toggleTraceCategoryState", "ranges": [{"startOffset": 12335, "endOffset": 12665, "count": 1}, {"startOffset": 12415, "endOffset": 12590, "count": 0}, {"startOffset": 12622, "endOffset": 12663, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "29", "url": "node:internal/fs/utils", "functions": [{"functionName": "assertEncoding", "ranges": [{"startOffset": 3557, "endOffset": 3757, "count": 5}, {"startOffset": 3608, "endOffset": 3639, "count": 2}, {"startOffset": 3641, "endOffset": 3755, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOptions", "ranges": [{"startOffset": 7391, "endOffset": 8017, "count": 5}, {"startOffset": 7509, "endOffset": 7541, "count": 0}, {"startOffset": 7578, "endOffset": 7696, "count": 2}, {"startOffset": 7696, "endOffset": 7818, "count": 3}, {"startOffset": 7735, "endOffset": 7818, "count": 0}, {"startOffset": 7932, "endOffset": 7996, "count": 0}], "isBlockCoverage": true}, {"functionName": "stringToFlags", "ranges": [{"startOffset": 15001, "endOffset": 16257, "count": 2}, {"startOffset": 15082, "endOffset": 15137, "count": 0}, {"startOffset": 15160, "endOffset": 15186, "count": 0}, {"startOffset": 15243, "endOffset": 15254, "count": 0}, {"startOffset": 15276, "endOffset": 15313, "count": 0}, {"startOffset": 15318, "endOffset": 15344, "count": 0}, {"startOffset": 15349, "endOffset": 15361, "count": 0}, {"startOffset": 15383, "endOffset": 15419, "count": 0}, {"startOffset": 15425, "endOffset": 15472, "count": 0}, {"startOffset": 15477, "endOffset": 15488, "count": 0}, {"startOffset": 15510, "endOffset": 15567, "count": 0}, {"startOffset": 15573, "endOffset": 15619, "count": 0}, {"startOffset": 15624, "endOffset": 15635, "count": 0}, {"startOffset": 15657, "endOffset": 15712, "count": 0}, {"startOffset": 15718, "endOffset": 15766, "count": 0}, {"startOffset": 15771, "endOffset": 15782, "count": 0}, {"startOffset": 15804, "endOffset": 15862, "count": 0}, {"startOffset": 15867, "endOffset": 15878, "count": 0}, {"startOffset": 15900, "endOffset": 15958, "count": 0}, {"startOffset": 15964, "endOffset": 16011, "count": 0}, {"startOffset": 16016, "endOffset": 16027, "count": 0}, {"startOffset": 16049, "endOffset": 16105, "count": 0}, {"startOffset": 16110, "endOffset": 16121, "count": 0}, {"startOffset": 16143, "endOffset": 16199, "count": 0}, {"startOffset": 16203, "endOffset": 16256, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18284, "endOffset": 18996, "count": 5}, {"startOffset": 18346, "endOffset": 18368, "count": 0}, {"startOffset": 18370, "endOffset": 18477, "count": 0}, {"startOffset": 18668, "endOffset": 18688, "count": 0}, {"startOffset": 18758, "endOffset": 18826, "count": 0}, {"startOffset": 18845, "endOffset": 18995, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19041, "endOffset": 19176, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "30", "url": "node:internal/url", "functions": [{"functionName": "isURL", "ranges": [{"startOffset": 21737, "endOffset": 21862, "count": 5}, {"startOffset": 21788, "endOffset": 21804, "count": 0}, {"startOffset": 21805, "endOffset": 21831, "count": 0}, {"startOffset": 21832, "endOffset": 21858, "count": 0}], "isBlockCoverage": true}, {"functionName": "toPathIfFileURL", "ranges": [{"startOffset": 47872, "endOffset": 48009, "count": 5}, {"startOffset": 47968, "endOffset": 48008, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "33", "url": "node:path", "functions": [{"functionName": "isPosixPathSeparator", "ranges": [{"startOffset": 2214, "endOffset": 2291, "count": 3769}], "isBlockCoverage": true}, {"functionName": "normalizeString", "ranges": [{"startOffset": 2956, "endOffset": 4904, "count": 59}, {"startOffset": 3173, "endOffset": 4888, "count": 3768}, {"startOffset": 3206, "endOffset": 3248, "count": 3709}, {"startOffset": 3248, "endOffset": 3339, "count": 59}, {"startOffset": 3291, "endOffset": 3297, "count": 58}, {"startOffset": 3297, "endOffset": 3339, "count": 1}, {"startOffset": 3339, "endOffset": 3372, "count": 3710}, {"startOffset": 3372, "endOffset": 4789, "count": 500}, {"startOffset": 3404, "endOffset": 3417, "count": 398}, {"startOffset": 3419, "endOffset": 3444, "count": 104}, {"startOffset": 3444, "endOffset": 4746, "count": 396}, {"startOffset": 3466, "endOffset": 4501, "count": 2}, {"startOffset": 3522, "endOffset": 3596, "count": 0}, {"startOffset": 3597, "endOffset": 3671, "count": 0}, {"startOffset": 3825, "endOffset": 3901, "count": 0}, {"startOffset": 4182, "endOffset": 4351, "count": 0}, {"startOffset": 4361, "endOffset": 4493, "count": 0}, {"startOffset": 4501, "endOffset": 4746, "count": 394}, {"startOffset": 4547, "endOffset": 4616, "count": 335}, {"startOffset": 4616, "endOffset": 4691, "count": 59}, {"startOffset": 4746, "endOffset": 4789, "count": 498}, {"startOffset": 4789, "endOffset": 4884, "count": 3210}, {"startOffset": 4817, "endOffset": 4831, "count": 106}, {"startOffset": 4833, "endOffset": 4854, "count": 52}, {"startOffset": 4854, "endOffset": 4884, "count": 3158}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 37532, "endOffset": 38636, "count": 58}, {"startOffset": 37654, "endOffset": 37674, "count": 111}, {"startOffset": 37681, "endOffset": 37986, "count": 109}, {"startOffset": 37812, "endOffset": 37839, "count": 0}, {"startOffset": 38015, "endOffset": 38190, "count": 0}, {"startOffset": 38575, "endOffset": 38625, "count": 0}, {"startOffset": 38626, "endOffset": 38631, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalize", "ranges": [{"startOffset": 38702, "endOffset": 39315, "count": 1}, {"startOffset": 38788, "endOffset": 38799, "count": 0}, {"startOffset": 39127, "endOffset": 39221, "count": 0}, {"startOffset": 39255, "endOffset": 39267, "count": 0}, {"startOffset": 39291, "endOffset": 39303, "count": 0}], "isBlockCoverage": true}, {"functionName": "isAbsolute", "ranges": [{"startOffset": 39382, "endOffset": 39538, "count": 3}], "isBlockCoverage": true}, {"functionName": "toNamespacedPath", "ranges": [{"startOffset": 42487, "endOffset": 42563, "count": 29}], "isBlockCoverage": true}, {"functionName": "dirname", "ranges": [{"startOffset": 42629, "endOffset": 43305, "count": 7}, {"startOffset": 42712, "endOffset": 42723, "count": 0}, {"startOffset": 42897, "endOffset": 43150, "count": 190}, {"startOffset": 42968, "endOffset": 43052, "count": 7}, {"startOffset": 43052, "endOffset": 43144, "count": 183}, {"startOffset": 43178, "endOffset": 43205, "count": 0}, {"startOffset": 43242, "endOffset": 43254, "count": 0}], "isBlockCoverage": true}, {"functionName": "basename", "ranges": [{"startOffset": 43401, "endOffset": 45831, "count": 2}, {"startOffset": 43462, "endOffset": 43495, "count": 0}, {"startOffset": 43627, "endOffset": 43647, "count": 0}, {"startOffset": 43648, "endOffset": 43679, "count": 0}, {"startOffset": 43681, "endOffset": 45205, "count": 0}, {"startOffset": 45253, "endOffset": 45738, "count": 53}, {"startOffset": 45324, "endOffset": 45548, "count": 2}, {"startOffset": 45548, "endOffset": 45732, "count": 51}, {"startOffset": 45570, "endOffset": 45732, "count": 2}, {"startOffset": 45766, "endOffset": 45776, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "35", "url": "node:internal/process/task_queues", "functions": [{"functionName": "setHasTickScheduled", "ranges": [{"startOffset": 1059, "endOffset": 1145, "count": 6}, {"startOffset": 1135, "endOffset": 1138, "count": 3}, {"startOffset": 1139, "endOffset": 1142, "count": 3}], "isBlockCoverage": true}, {"functionName": "processTicksAndRejections", "ranges": [{"startOffset": 1445, "endOffset": 2424, "count": 3}, {"startOffset": 1547, "endOffset": 2279, "count": 4}, {"startOffset": 1748, "endOffset": 1781, "count": 1}, {"startOffset": 1781, "endOffset": 2155, "count": 3}, {"startOffset": 1868, "endOffset": 1901, "count": 1}, {"startOffset": 1914, "endOffset": 1956, "count": 2}, {"startOffset": 1969, "endOffset": 2020, "count": 0}, {"startOffset": 2033, "endOffset": 2093, "count": 0}, {"startOffset": 2106, "endOffset": 2133, "count": 0}, {"startOffset": 2217, "endOffset": 2238, "count": 0}], "isBlockCoverage": true}, {"functionName": "nextTick", "ranges": [{"startOffset": 2568, "endOffset": 3442, "count": 4}, {"startOffset": 2669, "endOffset": 2676, "count": 0}, {"startOffset": 2724, "endOffset": 2738, "count": 1}, {"startOffset": 2743, "endOffset": 2780, "count": 1}, {"startOffset": 2785, "endOffset": 2836, "count": 2}, {"startOffset": 2841, "endOffset": 2906, "count": 0}, {"startOffset": 2911, "endOffset": 3054, "count": 0}, {"startOffset": 3083, "endOffset": 3109, "count": 3}, {"startOffset": 3354, "endOffset": 3414, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "36", "url": "node:internal/process/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 1267, "endOffset": 1379, "count": 2}], "isBlockCoverage": false}, {"functionName": "setHasRejectionToWarn", "ranges": [{"startOffset": 4097, "endOffset": 4187, "count": 3}, {"startOffset": 4177, "endOffset": 4180, "count": 0}], "isBlockCoverage": true}, {"functionName": "processPromiseRejections", "ranges": [{"startOffset": 12037, "endOffset": 13386, "count": 3}, {"startOffset": 12197, "endOffset": 12376, "count": 0}, {"startOffset": 12582, "endOffset": 13292, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "37", "url": "node:internal/fixed_queue", "functions": [{"functionName": "isEmpty", "ranges": [{"startOffset": 3130, "endOffset": 3182, "count": 14}], "isBlockCoverage": true}, {"functionName": "isFull", "ranges": [{"startOffset": 3186, "endOffset": 3253, "count": 4}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 3257, "endOffset": 3344, "count": 4}], "isBlockCoverage": true}, {"functionName": "shift", "ranges": [{"startOffset": 3348, "endOffset": 3563, "count": 7}, {"startOffset": 3441, "endOffset": 3453, "count": 3}, {"startOffset": 3453, "endOffset": 3562, "count": 4}], "isBlockCoverage": true}, {"functionName": "isEmpty", "ranges": [{"startOffset": 3683, "endOffset": 3730, "count": 7}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 3734, "endOffset": 3998, "count": 4}, {"startOffset": 3775, "endOffset": 3968, "count": 0}], "isBlockCoverage": true}, {"functionName": "shift", "ranges": [{"startOffset": 4002, "endOffset": 4257, "count": 7}, {"startOffset": 4094, "endOffset": 4115, "count": 6}, {"startOffset": 4117, "endOffset": 4236, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "39", "url": "node:timers", "functions": [{"functionName": "", "ranges": [{"startOffset": 1886, "endOffset": 1911, "count": 1}], "isBlockCoverage": true}, {"functionName": "unenroll", "ranges": [{"startOffset": 2207, "endOffset": 3391, "count": 1}, {"startOffset": 2260, "endOffset": 2267, "count": 0}, {"startOffset": 2327, "endOffset": 2373, "count": 0}, {"startOffset": 2464, "endOffset": 2502, "count": 0}, {"startOffset": 2508, "endOffset": 2543, "count": 0}], "isBlockCoverage": true}, {"functionName": "setTimeout", "ranges": [{"startOffset": 4093, "endOffset": 4762, "count": 1}, {"startOffset": 4260, "endOffset": 4267, "count": 0}, {"startOffset": 4297, "endOffset": 4338, "count": 0}, {"startOffset": 4343, "endOffset": 4390, "count": 0}, {"startOffset": 4395, "endOffset": 4628, "count": 0}], "isBlockCoverage": true}, {"functionName": "clearTimeout", "ranges": [{"startOffset": 5082, "endOffset": 5448, "count": 2}, {"startOffset": 5125, "endOffset": 5144, "count": 1}, {"startOffset": 5146, "endOffset": 5274, "count": 1}, {"startOffset": 5276, "endOffset": 5446, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "40", "url": "node:internal/process/execution", "functions": [{"functionName": "tryGetCwd", "ranges": [{"startOffset": 945, "endOffset": 1275, "count": 1}, {"startOffset": 1006, "endOffset": 1273, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "43", "url": "node:internal/source_map/source_map_cache", "functions": [{"functionName": "setSourceMapsEnabled", "ranges": [{"startOffset": 1729, "endOffset": 2313, "count": 1}, {"startOffset": 1837, "endOffset": 2020, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "44", "url": "node:internal/modules/helpers", "functions": [{"functionName": "", "ranges": [{"startOffset": 1182, "endOffset": 1207, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1676, "endOffset": 1808, "count": 3}], "isBlockCoverage": true}, {"functionName": "initializeCjsConditions", "ranges": [{"startOffset": 1924, "endOffset": 2415, "count": 1}, {"startOffset": 2103, "endOffset": 2107, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadBuiltinModule", "ranges": [{"startOffset": 2836, "endOffset": 3216, "count": 1}, {"startOffset": 2925, "endOffset": 2942, "count": 0}], "isBlockCoverage": true}, {"functionName": "lazyModule", "ranges": [{"startOffset": 3309, "endOffset": 3422, "count": 2}, {"startOffset": 3353, "endOffset": 3401, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeRequireFunction", "ranges": [{"startOffset": 4011, "endOffset": 6543, "count": 2}, {"startOffset": 4150, "endOffset": 4213, "count": 0}, {"startOffset": 4280, "endOffset": 5551, "count": 0}], "isBlockCoverage": true}, {"functionName": "require", "ranges": [{"startOffset": 4423, "endOffset": 5546, "count": 0}], "isBlockCoverage": false}, {"functionName": "require", "ranges": [{"startOffset": 5573, "endOffset": 5713, "count": 2}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 5899, "endOffset": 6045, "count": 0}], "isBlockCoverage": false}, {"functionName": "paths", "ranges": [{"startOffset": 6190, "endOffset": 6312, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasStartedUserCJSExecution", "ranges": [{"startOffset": 11438, "endOffset": 11512, "count": 1}], "isBlockCoverage": true}, {"functionName": "setHasStartedUserCJSExecution", "ranges": [{"startOffset": 11516, "endOffset": 11593, "count": 2}], "isBlockCoverage": true}, {"functionName": "hasStartedUserESMExecution", "ranges": [{"startOffset": 11597, "endOffset": 11671, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "45", "url": "node:fs", "functions": [{"functionName": "isFileType", "ranges": [{"startOffset": 5197, "endOffset": 5457, "count": 32}, {"startOffset": 5396, "endOffset": 5416, "count": 12}], "isBlockCoverage": true}, {"functionName": "readFileSync", "ranges": [{"startOffset": 11296, "endOffset": 12935, "count": 2}, {"startOffset": 11418, "endOffset": 11449, "count": 0}, {"startOffset": 11622, "endOffset": 11712, "count": 0}, {"startOffset": 11713, "endOffset": 11753, "count": 0}, {"startOffset": 11841, "endOffset": 11851, "count": 0}, {"startOffset": 11852, "endOffset": 11855, "count": 0}, {"startOffset": 11985, "endOffset": 12934, "count": 0}], "isBlockCoverage": true}, {"functionName": "splitRoot", "ranges": [{"startOffset": 69496, "endOffset": 69706, "count": 2}, {"startOffset": 69563, "endOffset": 69686, "count": 4}, {"startOffset": 69641, "endOffset": 69680, "count": 2}, {"startOffset": 69686, "endOffset": 69705, "count": 0}], "isBlockCoverage": true}, {"functionName": "encodeRealpathResult", "ranges": [{"startOffset": 69711, "endOffset": 69998, "count": 2}, {"startOffset": 69796, "endOffset": 69826, "count": 0}, {"startOffset": 69846, "endOffset": 69997, "count": 0}], "isBlockCoverage": true}, {"functionName": "nextPart", "ranges": [{"startOffset": 70404, "endOffset": 70479, "count": 22}], "isBlockCoverage": true}, {"functionName": "realpathSync", "ranges": [{"startOffset": 70653, "endOffset": 74472, "count": 3}, {"startOffset": 70777, "endOffset": 70795, "count": 0}, {"startOffset": 70956, "endOffset": 70991, "count": 1}, {"startOffset": 70991, "endOffset": 71553, "count": 2}, {"startOffset": 71553, "endOffset": 71743, "count": 0}, {"startOffset": 71743, "endOffset": 71880, "count": 2}, {"startOffset": 71880, "endOffset": 74399, "count": 22}, {"startOffset": 71992, "endOffset": 72123, "count": 2}, {"startOffset": 72123, "endOffset": 72281, "count": 20}, {"startOffset": 72398, "endOffset": 72535, "count": 10}, {"startOffset": 72489, "endOffset": 72513, "count": 0}, {"startOffset": 72535, "endOffset": 72601, "count": 12}, {"startOffset": 72638, "endOffset": 72687, "count": 0}, {"startOffset": 72687, "endOffset": 73876, "count": 12}, {"startOffset": 72983, "endOffset": 73008, "count": 0}, {"startOffset": 73137, "endOffset": 73796, "count": 0}, {"startOffset": 73840, "endOffset": 73870, "count": 0}, {"startOffset": 73876, "endOffset": 74191, "count": 0}, {"startOffset": 74193, "endOffset": 74395, "count": 0}, {"startOffset": 74399, "endOffset": 74413, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "52", "url": "node:internal/process/permission", "functions": [{"functionName": "isEnabled", "ranges": [{"startOffset": 332, "endOffset": 576, "count": 5}, {"startOffset": 392, "endOffset": 537, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "55", "url": "node:internal/console/constructor", "functions": [{"functionName": "value", "ranges": [{"startOffset": 5763, "endOffset": 6408, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5972, "endOffset": 6067, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6079, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6239, "endOffset": 6338, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6350, "endOffset": 6380, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeGlobalConsole", "ranges": [{"startOffset": 20356, "endOffset": 21346, "count": 1}, {"startOffset": 20675, "endOffset": 21345, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21241, "endOffset": 21342, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "57", "url": "node:internal/event_target", "functions": [{"functionName": "defineEventHandler", "ranges": [{"startOffset": 29893, "endOffset": 31359, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 30053, "endOffset": 30189, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30288, "endOffset": 31126, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "61", "url": "node:internal/bootstrap/switches/is_main_thread", "functions": [{"functionName": "createWritableStdioStream", "ranges": [{"startOffset": 1424, "endOffset": 3026, "count": 1}, {"startOffset": 1716, "endOffset": 1910, "count": 0}, {"startOffset": 1916, "endOffset": 1928, "count": 0}, {"startOffset": 1933, "endOffset": 2645, "count": 0}, {"startOffset": 2651, "endOffset": 2905, "count": 0}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 2841, "endOffset": 2888, "count": 0}], "isBlockCoverage": false}, {"functionName": "addCleanup", "ranges": [{"startOffset": 3639, "endOffset": 3730, "count": 1}, {"startOffset": 3693, "endOffset": 3728, "count": 0}], "isBlockCoverage": true}, {"functionName": "getStderr", "ranges": [{"startOffset": 4426, "endOffset": 5117, "count": 1}, {"startOffset": 4463, "endOffset": 4477, "count": 0}], "isBlockCoverage": true}, {"functionName": "cleanupStderr", "ranges": [{"startOffset": 4783, "endOffset": 4961, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "62", "url": "node:internal/v8/startup_snapshot", "functions": [{"functionName": "isBuildingSnapshot", "ranges": [{"startOffset": 433, "endOffset": 504, "count": 5}], "isBlockCoverage": false}, {"functionName": "runDeserializeCallbacks", "ranges": [{"startOffset": 831, "endOffset": 1004, "count": 1}, {"startOffset": 910, "endOffset": 1002, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "63", "url": "node:internal/process/signal", "functions": [{"functionName": "isSignal", "ranges": [{"startOffset": 238, "endOffset": 334, "count": 2}], "isBlockCoverage": false}, {"functionName": "startListeningIfSignal", "ranges": [{"startOffset": 398, "endOffset": 908, "count": 2}], "isBlockCoverage": false}]}, {"scriptId": "65", "url": "node:internal/modules/cjs/loader", "functions": [{"functionName": "", "ranges": [{"startOffset": 4692, "endOffset": 4783, "count": 1}, {"startOffset": 4739, "endOffset": 4775, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4832, "endOffset": 4875, "count": 1}], "isBlockCoverage": true}, {"functionName": "stat", "ranges": [{"startOffset": 6287, "endOffset": 6701, "count": 4}, {"startOffset": 6385, "endOffset": 6483, "count": 3}, {"startOffset": 6461, "endOffset": 6479, "count": 0}, {"startOffset": 6556, "endOffset": 6570, "count": 3}, {"startOffset": 6572, "endOffset": 6682, "count": 2}], "isBlockCoverage": true}, {"functionName": "update<PERSON><PERSON><PERSON>n", "ranges": [{"startOffset": 7192, "endOffset": 7394, "count": 2}, {"startOffset": 7264, "endOffset": 7274, "count": 1}, {"startOffset": 7291, "endOffset": 7344, "count": 1}, {"startOffset": 7301, "endOffset": 7343, "count": 0}, {"startOffset": 7346, "endOffset": 7392, "count": 1}], "isBlockCoverage": true}, {"functionName": "reportModuleToWatchMode", "ranges": [{"startOffset": 7511, "endOffset": 7667, "count": 4}, {"startOffset": 7592, "endOffset": 7607, "count": 0}, {"startOffset": 7609, "endOffset": 7665, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8181, "endOffset": 8931, "count": 2}, {"startOffset": 8487, "endOffset": 8497, "count": 0}, {"startOffset": 8515, "endOffset": 8879, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10914, "endOffset": 10939, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeCJS", "ranges": [{"startOffset": 11659, "endOffset": 12270, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryFile", "ranges": [{"startOffset": 14794, "endOffset": 15057, "count": 1}, {"startOffset": 14883, "endOffset": 14894, "count": 0}, {"startOffset": 14923, "endOffset": 14951, "count": 0}, {"startOffset": 14978, "endOffset": 15021, "count": 0}], "isBlockCoverage": true}, {"functionName": "tryExtensions", "ranges": [{"startOffset": 15322, "endOffset": 15542, "count": 1}, {"startOffset": 15524, "endOffset": 15541, "count": 0}], "isBlockCoverage": true}, {"functionName": "findLongestRegisteredExtension", "ranges": [{"startOffset": 15726, "endOffset": 16202, "count": 2}, {"startOffset": 16001, "endOffset": 16014, "count": 0}, {"startOffset": 16184, "endOffset": 16201, "count": 0}], "isBlockCoverage": true}, {"functionName": "trySelf<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 16325, "endOffset": 16618, "count": 2}, {"startOffset": 16377, "endOffset": 16452, "count": 1}, {"startOffset": 16452, "endOffset": 16616, "count": 0}], "isBlockCoverage": true}, {"functionName": "trySelf", "ranges": [{"startOffset": 16826, "endOffset": 17754, "count": 2}, {"startOffset": 16885, "endOffset": 17002, "count": 1}, {"startOffset": 17002, "endOffset": 17024, "count": 0}, {"startOffset": 17025, "endOffset": 17050, "count": 0}, {"startOffset": 17052, "endOffset": 17075, "count": 1}, {"startOffset": 17075, "endOffset": 17752, "count": 0}], "isBlockCoverage": true}, {"functionName": "resolveExports", "ranges": [{"startOffset": 18102, "endOffset": 18920, "count": 1}, {"startOffset": 18350, "endOffset": 18473, "count": 0}, {"startOffset": 18475, "endOffset": 18918, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultExtensions", "ranges": [{"startOffset": 18982, "endOffset": 19477, "count": 1}, {"startOffset": 19125, "endOffset": 19153, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19401, "endOffset": 19473, "count": 4}, {"startOffset": 19425, "endOffset": 19473, "count": 1}], "isBlockCoverage": true}, {"functionName": "Module._findPath", "ranges": [{"startOffset": 19770, "endOffset": 23329, "count": 3}, {"startOffset": 19880, "endOffset": 19903, "count": 2}, {"startOffset": 19903, "endOffset": 19966, "count": 1}, {"startOffset": 19943, "endOffset": 19966, "count": 0}, {"startOffset": 20099, "endOffset": 20122, "count": 1}, {"startOffset": 20122, "endOffset": 20705, "count": 2}, {"startOffset": 20344, "endOffset": 20698, "count": 0}, {"startOffset": 20760, "endOffset": 20914, "count": 1}, {"startOffset": 20877, "endOffset": 20910, "count": 0}, {"startOffset": 20914, "endOffset": 23310, "count": 2}, {"startOffset": 21128, "endOffset": 21224, "count": 1}, {"startOffset": 21162, "endOffset": 21200, "count": 0}, {"startOffset": 21231, "endOffset": 21254, "count": 0}, {"startOffset": 21282, "endOffset": 21422, "count": 1}, {"startOffset": 21375, "endOffset": 21416, "count": 0}, {"startOffset": 21574, "endOffset": 22558, "count": 1}, {"startOffset": 21607, "endOffset": 21796, "count": 0}, {"startOffset": 21850, "endOffset": 22490, "count": 0}, {"startOffset": 22581, "endOffset": 22779, "count": 1}, {"startOffset": 22805, "endOffset": 22816, "count": 0}, {"startOffset": 22818, "endOffset": 23036, "count": 0}, {"startOffset": 23132, "endOffset": 23310, "count": 0}, {"startOffset": 23310, "endOffset": 23328, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._nodeModulePaths", "ranges": [{"startOffset": 25406, "endOffset": 26594, "count": 2}, {"startOffset": 25678, "endOffset": 25717, "count": 0}, {"startOffset": 26038, "endOffset": 26472, "count": 196}, {"startOffset": 26134, "endOffset": 26342, "count": 20}, {"startOffset": 26342, "endOffset": 26466, "count": 176}, {"startOffset": 26362, "endOffset": 26466, "count": 26}, {"startOffset": 26397, "endOffset": 26423, "count": 6}, {"startOffset": 26423, "endOffset": 26458, "count": 20}], "isBlockCoverage": true}, {"functionName": "Module._resolveLookupPaths", "ranges": [{"startOffset": 26729, "endOffset": 28016, "count": 2}, {"startOffset": 26809, "endOffset": 26875, "count": 0}, {"startOffset": 26960, "endOffset": 27159, "count": 1}, {"startOffset": 27091, "endOffset": 27158, "count": 0}, {"startOffset": 27161, "endOffset": 27488, "count": 1}, {"startOffset": 27221, "endOffset": 27236, "count": 0}, {"startOffset": 27238, "endOffset": 27348, "count": 0}, {"startOffset": 27477, "endOffset": 27483, "count": 0}, {"startOffset": 27488, "endOffset": 27556, "count": 1}, {"startOffset": 27557, "endOffset": 27576, "count": 1}, {"startOffset": 27578, "endOffset": 27827, "count": 0}, {"startOffset": 27827, "endOffset": 28015, "count": 1}], "isBlockCoverage": true}, {"functionName": "Module._load", "ranges": [{"startOffset": 31138, "endOffset": 35016, "count": 3}, {"startOffset": 31221, "endOffset": 32049, "count": 2}, {"startOffset": 31680, "endOffset": 32045, "count": 0}, {"startOffset": 32102, "endOffset": 32374, "count": 0}, {"startOffset": 32527, "endOffset": 33453, "count": 0}, {"startOffset": 33513, "endOffset": 33596, "count": 1}, {"startOffset": 33596, "endOffset": 33726, "count": 2}, {"startOffset": 33750, "endOffset": 34127, "count": 2}, {"startOffset": 33768, "endOffset": 34003, "count": 1}, {"startOffset": 34127, "endOffset": 34988, "count": 2}, {"startOffset": 34331, "endOffset": 34734, "count": 0}, {"startOffset": 34916, "endOffset": 34984, "count": 0}, {"startOffset": 34988, "endOffset": 35015, "count": 2}], "isBlockCoverage": true}, {"functionName": "Module._resolveFilename", "ranges": [{"startOffset": 35478, "endOffset": 38364, "count": 3}, {"startOffset": 35575, "endOffset": 35600, "count": 1}, {"startOffset": 35600, "endOffset": 35650, "count": 2}, {"startOffset": 35650, "endOffset": 35669, "count": 0}, {"startOffset": 35671, "endOffset": 36522, "count": 0}, {"startOffset": 36522, "endOffset": 36617, "count": 2}, {"startOffset": 36617, "endOffset": 36665, "count": 0}, {"startOffset": 36667, "endOffset": 37309, "count": 0}, {"startOffset": 37309, "endOffset": 37470, "count": 2}, {"startOffset": 37470, "endOffset": 37666, "count": 0}, {"startOffset": 37666, "endOffset": 37826, "count": 2}, {"startOffset": 37826, "endOffset": 38363, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module.load", "ranges": [{"startOffset": 40021, "endOffset": 40688, "count": 2}, {"startOffset": 40356, "endOffset": 40386, "count": 0}, {"startOffset": 40388, "endOffset": 40440, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module.require", "ranges": [{"startOffset": 40975, "endOffset": 41274, "count": 2}, {"startOffset": 41035, "endOffset": 41153, "count": 0}], "isBlockCoverage": true}, {"functionName": "wrapSafe", "ranges": [{"startOffset": 46694, "endOffset": 48701, "count": 2}, {"startOffset": 46987, "endOffset": 47859, "count": 0}, {"startOffset": 47970, "endOffset": 48378, "count": 1}, {"startOffset": 48574, "endOffset": 48681, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._compile", "ranges": [{"startOffset": 49102, "endOffset": 50633, "count": 2}, {"startOffset": 49201, "endOffset": 49211, "count": 0}, {"startOffset": 49229, "endOffset": 49382, "count": 0}, {"startOffset": 49567, "endOffset": 49599, "count": 0}, {"startOffset": 49632, "endOffset": 49805, "count": 0}, {"startOffset": 50029, "endOffset": 50059, "count": 1}, {"startOffset": 50150, "endOffset": 50184, "count": 1}, {"startOffset": 50186, "endOffset": 50397, "count": 0}, {"startOffset": 50593, "endOffset": 50614, "count": 1}], "isBlockCoverage": true}, {"functionName": "getMaybeCachedSource", "ranges": [{"startOffset": 50887, "endOffset": 51292, "count": 2}, {"startOffset": 51050, "endOffset": 51125, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._extensions..js", "ranges": [{"startOffset": 51471, "endOffset": 53565, "count": 2}, {"startOffset": 51842, "endOffset": 51848, "count": 0}, {"startOffset": 51863, "endOffset": 53354, "count": 0}, {"startOffset": 53372, "endOffset": 53378, "count": 0}, {"startOffset": 53395, "endOffset": 53429, "count": 0}, {"startOffset": 53433, "endOffset": 53516, "count": 0}], "isBlockCoverage": true}, {"functionName": "isRelative", "ranges": [{"startOffset": 56411, "endOffset": 56752, "count": 2}, {"startOffset": 56494, "endOffset": 56556, "count": 1}, {"startOffset": 56557, "endOffset": 56599, "count": 1}, {"startOffset": 56600, "endOffset": 56643, "count": 0}, {"startOffset": 56644, "endOffset": 56749, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._initPaths", "ranges": [{"startOffset": 56872, "endOffset": 57837, "count": 1}, {"startOffset": 56913, "endOffset": 56938, "count": 0}, {"startOffset": 56990, "endOffset": 57013, "count": 0}, {"startOffset": 57221, "endOffset": 57263, "count": 0}, {"startOffset": 57562, "endOffset": 57704, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "66", "url": "node:internal/modules/package_json_reader", "functions": [{"functionName": "read", "ranges": [{"startOffset": 1112, "endOffset": 3862, "count": 44}, {"startOffset": 1208, "endOffset": 1245, "count": 33}, {"startOffset": 1245, "endOffset": 1922, "count": 11}, {"startOffset": 1922, "endOffset": 1931, "count": 0}, {"startOffset": 1932, "endOffset": 1946, "count": 0}, {"startOffset": 1947, "endOffset": 1969, "count": 11}, {"startOffset": 1993, "endOffset": 3812, "count": 0}, {"startOffset": 3812, "endOffset": 3861, "count": 11}], "isBlockCoverage": true}, {"functionName": "readPackage", "ranges": [{"startOffset": 3930, "endOffset": 4020, "count": 44}], "isBlockCoverage": true}, {"functionName": "readPackageScope", "ranges": [{"startOffset": 4237, "endOffset": 5057, "count": 4}, {"startOffset": 4424, "endOffset": 4994, "count": 44}, {"startOffset": 4673, "endOffset": 4719, "count": 0}, {"startOffset": 4721, "endOffset": 4748, "count": 0}, {"startOffset": 4815, "endOffset": 4842, "count": 0}, {"startOffset": 4913, "endOffset": 4990, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "67", "url": "node:internal/modules/esm/utils", "functions": [{"functionName": "initializeDefaultConditions", "ranges": [{"startOffset": 1785, "endOffset": 2290, "count": 1}, {"startOffset": 1968, "endOffset": 1972, "count": 0}, {"startOffset": 2085, "endOffset": 2089, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeESM", "ranges": [{"startOffset": 10706, "endOffset": 11092, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "68", "url": "node:internal/process/pre_execution", "functions": [{"functionName": "prepareMainThreadExecution", "ranges": [{"startOffset": 1060, "endOffset": 1244, "count": 1}], "isBlockCoverage": true}, {"functionName": "prepareExecution", "ranges": [{"startOffset": 2474, "endOffset": 4718, "count": 1}, {"startOffset": 3485, "endOffset": 3589, "count": 0}, {"startOffset": 4477, "endOffset": 4639, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupSymbolDisposePolyfill", "ranges": [{"startOffset": 4720, "endOffset": 5455, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupUserModules", "ranges": [{"startOffset": 5457, "endOffset": 6205, "count": 1}, {"startOffset": 5827, "endOffset": 5858, "count": 0}], "isBlockCoverage": true}, {"functionName": "refreshRuntimeOptions", "ranges": [{"startOffset": 6207, "endOffset": 6263, "count": 1}], "isBlockCoverage": true}, {"functionName": "patchProcessObject", "ranges": [{"startOffset": 6690, "endOffset": 9027, "count": 1}, {"startOffset": 7675, "endOffset": 7727, "count": 0}], "isBlockCoverage": true}, {"functionName": "addReadOnlyProcessAlias", "ranges": [{"startOffset": 9029, "endOffset": 9310, "count": 13}, {"startOffset": 9150, "endOffset": 9308, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupWarningHandler", "ranges": [{"startOffset": 9312, "endOffset": 9832, "count": 1}, {"startOffset": 9687, "endOffset": 9826, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9716, "endOffset": 9818, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9907, "endOffset": 10326, "count": 1}, {"startOffset": 9981, "endOffset": 10141, "count": 0}, {"startOffset": 10235, "endOffset": 10324, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupEventsource", "ranges": [{"startOffset": 10394, "endOffset": 10519, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupWebCrypto", "ranges": [{"startOffset": 10623, "endOffset": 11575, "count": 1}, {"startOffset": 10758, "endOffset": 10775, "count": 0}, {"startOffset": 11264, "endOffset": 11573, "count": 0}], "isBlockCoverage": true}, {"functionName": "cryptoThisCheck", "ranges": [{"startOffset": 10949, "endOffset": 11125, "count": 0}], "isBlockCoverage": false}, {"functionName": "get crypto", "ranges": [{"startOffset": 11426, "endOffset": 11525, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupCodeCoverage", "ranges": [{"startOffset": 11577, "endOffset": 12098, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupCustomEvent", "ranges": [{"startOffset": 12204, "endOffset": 12481, "count": 1}, {"startOffset": 12343, "endOffset": 12360, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupStacktracePrinterOnSigint", "ranges": [{"startOffset": 12483, "endOffset": 12708, "count": 1}, {"startOffset": 12585, "endOffset": 12707, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeReport", "ranges": [{"startOffset": 12710, "endOffset": 12959, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 12855, "endOffset": 12950, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupDebugEnv", "ranges": [{"startOffset": 12961, "endOffset": 13192, "count": 1}, {"startOffset": 13112, "endOffset": 13190, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeReportSignalHandlers", "ranges": [{"startOffset": 13254, "endOffset": 13442, "count": 1}, {"startOffset": 13342, "endOffset": 13440, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeHeapSnapshotSignalHandlers", "ranges": [{"startOffset": 13444, "endOffset": 14178, "count": 1}, {"startOffset": 13639, "endOffset": 14176, "count": 0}], "isBlockCoverage": true}, {"functionName": "doWriteHeapSnapshot", "ranges": [{"startOffset": 13752, "endOffset": 13906, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14098, "endOffset": 14170, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTraceCategoryState", "ranges": [{"startOffset": 14180, "endOffset": 14439, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupInspectorHooks", "ranges": [{"startOffset": 14441, "endOffset": 15023, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupNetworkInspection", "ranges": [{"startOffset": 15025, "endOffset": 15338, "count": 1}, {"startOffset": 15162, "endOffset": 15336, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeDeprecations", "ranges": [{"startOffset": 15533, "endOffset": 17485, "count": 1}, {"startOffset": 16245, "endOffset": 16547, "count": 16}, {"startOffset": 16290, "endOffset": 16522, "count": 0}, {"startOffset": 16864, "endOffset": 17061, "count": 0}, {"startOffset": 17089, "endOffset": 17483, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupChildProcessIpcChannel", "ranges": [{"startOffset": 17487, "endOffset": 18052, "count": 1}, {"startOffset": 17563, "endOffset": 18050, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeClusterIPC", "ranges": [{"startOffset": 18054, "endOffset": 18324, "count": 1}, {"startOffset": 18141, "endOffset": 18322, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializePermission", "ranges": [{"startOffset": 18326, "endOffset": 20323, "count": 1}, {"startOffset": 18468, "endOffset": 19897, "count": 0}], "isBlockCoverage": true}, {"functionName": "binding", "ranges": [{"startOffset": 18492, "endOffset": 18579, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20155, "endOffset": 20315, "count": 6}, {"startOffset": 20233, "endOffset": 20309, "count": 0}], "isBlockCoverage": true}, {"functionName": "readPolicyFromDisk", "ranges": [{"startOffset": 20325, "endOffset": 22144, "count": 1}, {"startOffset": 20453, "endOffset": 22142, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeCJSLoader", "ranges": [{"startOffset": 22146, "endOffset": 22267, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeESMLoader", "ranges": [{"startOffset": 22269, "endOffset": 22852, "count": 1}, {"startOffset": 22605, "endOffset": 22850, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeSourceMapsHandlers", "ranges": [{"startOffset": 22854, "endOffset": 23052, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeFrozenIntrinsics", "ranges": [{"startOffset": 23054, "endOffset": 23241, "count": 1}, {"startOffset": 23139, "endOffset": 23239, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadPreloadModules", "ranges": [{"startOffset": 23348, "endOffset": 23696, "count": 1}, {"startOffset": 23542, "endOffset": 23694, "count": 0}], "isBlockCoverage": true}, {"functionName": "markBootstrapComplete", "ranges": [{"startOffset": 23698, "endOffset": 23792, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "70", "url": "node:internal/modules/run_main", "functions": [{"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 569, "endOffset": 1672, "count": 1}, {"startOffset": 743, "endOffset": 847, "count": 0}, {"startOffset": 1140, "endOffset": 1151, "count": 0}, {"startOffset": 1378, "endOffset": 1646, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldUseESMLoader", "ranges": [{"startOffset": 1833, "endOffset": 3041, "count": 1}, {"startOffset": 1939, "endOffset": 1955, "count": 0}, {"startOffset": 2429, "endOffset": 2445, "count": 0}, {"startOffset": 2561, "endOffset": 2577, "count": 0}, {"startOffset": 2640, "endOffset": 2657, "count": 0}, {"startOffset": 2867, "endOffset": 2873, "count": 0}, {"startOffset": 2881, "endOffset": 2914, "count": 0}, {"startOffset": 2919, "endOffset": 2955, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeUserEntryPoint", "ranges": [{"startOffset": 5520, "endOffset": 6505, "count": 1}, {"startOffset": 6089, "endOffset": 6503, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6257, "endOffset": 6497, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "71", "url": "node:internal/dns/utils", "functions": [{"functionName": "initializeDns", "ranges": [{"startOffset": 5468, "endOffset": 5949, "count": 1}, {"startOffset": 5611, "endOffset": 5816, "count": 0}, {"startOffset": 5864, "endOffset": 5948, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5889, "endOffset": 5945, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "72", "url": "node:internal/net", "functions": [{"functionName": "isIPv4", "ranges": [{"startOffset": 1023, "endOffset": 1296, "count": 1}], "isBlockCoverage": true}, {"functionName": "isIP", "ranges": [{"startOffset": 1573, "endOffset": 1659, "count": 1}, {"startOffset": 1618, "endOffset": 1658, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "73", "url": "node:internal/bootstrap/switches/does_own_process_state", "functions": [{"functionName": "wrappedCwd", "ranges": [{"startOffset": 3781, "endOffset": 3884, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "80", "url": "node:internal/main/run_main_module", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1182, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "81", "url": "file:///home/<USER>/.local/share/JetBrains/Toolbox/apps/intellij-idea-ultimate/plugins/javascript-debugger/debugConnectorPortPublisher.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1306, "count": 1}], "isBlockCoverage": true}, {"functionName": "publishDebugPort", "ranges": [{"startOffset": 111, "endOffset": 1185, "count": 1}, {"startOffset": 192, "endOffset": 298, "count": 0}, {"startOffset": 344, "endOffset": 358, "count": 0}, {"startOffset": 514, "endOffset": 635, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 706, "endOffset": 1026, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 736, "endOffset": 804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 889, "endOffset": 1011, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1077, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatMessage", "ranges": [{"startOffset": 1187, "endOffset": 1305, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "82", "url": "file:///home/<USER>/.local/share/JetBrains/Toolbox/apps/intellij-idea-ultimate/plugins/javascript-debugger/debugConnectorUtil.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1714, "count": 1}], "isBlockCoverage": true}, {"functionName": "exports.getGatewayHostPort", "ranges": [{"startOffset": 29, "endOffset": 290, "count": 0}], "isBlockCoverage": false}, {"functionName": "exports.forwardDebugConnectionAndWait", "ranges": [{"startOffset": 333, "endOffset": 1212, "count": 0}], "isBlockCoverage": false}, {"functionName": "isVerboseLoggingEnabled", "ranges": [{"startOffset": 1250, "endOffset": 1453, "count": 1}], "isBlockCoverage": true}, {"functionName": "exports.formatMessage", "ranges": [{"startOffset": 1539, "endOffset": 1712, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "83", "url": "node:net", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 68429, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1645, "endOffset": 1670, "count": 1}], "isBlockCoverage": true}, {"functionName": "noop", "ranges": [{"startOffset": 4172, "endOffset": 4180, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFlags", "ranges": [{"startOffset": 4653, "endOffset": 4747, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHandle", "ranges": [{"startOffset": 4749, "endOffset": 5132, "count": 0}], "isBlockCoverage": false}, {"functionName": "getNewAsyncId", "ranges": [{"startOffset": 5135, "endOffset": 5273, "count": 3}, {"startOffset": 5230, "endOffset": 5248, "count": 0}], "isBlockCoverage": true}, {"functionName": "isPipeName", "ranges": [{"startOffset": 5276, "endOffset": 5359, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServer", "ranges": [{"startOffset": 5560, "endOffset": 5664, "count": 0}], "isBlockCoverage": false}, {"functionName": "connect", "ranges": [{"startOffset": 5887, "endOffset": 6288, "count": 1}, {"startOffset": 6115, "endOffset": 6179, "count": 0}, {"startOffset": 6203, "endOffset": 6248, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultAutoSelectFamily", "ranges": [{"startOffset": 6290, "endOffset": 6365, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamily", "ranges": [{"startOffset": 6367, "endOffset": 6483, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6485, "endOffset": 6588, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6590, "endOffset": 6777, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeArgs", "ranges": [{"startOffset": 7271, "endOffset": 7997, "count": 1}, {"startOffset": 7339, "endOffset": 7420, "count": 0}, {"startOffset": 7568, "endOffset": 7816, "count": 0}, {"startOffset": 7890, "endOffset": 7912, "count": 0}], "isBlockCoverage": true}, {"functionName": "initSocketHandle", "ranges": [{"startOffset": 8069, "endOffset": 8672, "count": 3}, {"startOffset": 8236, "endOffset": 8670, "count": 2}, {"startOffset": 8425, "endOffset": 8666, "count": 0}], "isBlockCoverage": true}, {"functionName": "closeSocketHandle", "ranges": [{"startOffset": 8674, "endOffset": 9014, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8793, "endOffset": 9006, "count": 1}, {"startOffset": 8895, "endOffset": 9000, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket", "ranges": [{"startOffset": 9266, "endOffset": 13893, "count": 2}, {"startOffset": 9326, "endOffset": 9353, "count": 0}, {"startOffset": 9381, "endOffset": 9510, "count": 0}, {"startOffset": 9580, "endOffset": 9828, "count": 0}, {"startOffset": 9890, "endOffset": 10086, "count": 0}, {"startOffset": 10648, "endOffset": 10674, "count": 0}, {"startOffset": 11080, "endOffset": 12436, "count": 1}, {"startOffset": 11224, "endOffset": 12436, "count": 0}, {"startOffset": 12523, "endOffset": 12600, "count": 0}, {"startOffset": 12601, "endOffset": 12647, "count": 0}, {"startOffset": 12649, "endOffset": 12864, "count": 0}, {"startOffset": 13368, "endOffset": 13397, "count": 1}, {"startOffset": 13399, "endOffset": 13664, "count": 1}, {"startOffset": 13432, "endOffset": 13601, "count": 0}, {"startOffset": 13633, "endOffset": 13660, "count": 0}, {"startOffset": 13688, "endOffset": 13740, "count": 0}], "isBlockCoverage": true}, {"functionName": "_unrefTimer", "ranges": [{"startOffset": 14066, "endOffset": 14196, "count": 4}, {"startOffset": 14168, "endOffset": 14190, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket._final", "ranges": [{"startOffset": 14316, "endOffset": 14936, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterShutdown", "ranges": [{"startOffset": 14939, "endOffset": 15085, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeAfterFIN", "ranges": [{"startOffset": 15292, "endOffset": 15800, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._onTimeout", "ranges": [{"startOffset": 15882, "endOffset": 16386, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setNoDelay", "ranges": [{"startOffset": 16420, "endOffset": 16797, "count": 1}, {"startOffset": 16546, "endOffset": 16552, "count": 0}, {"startOffset": 16586, "endOffset": 16640, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket.setKeepAlive", "ranges": [{"startOffset": 16833, "endOffset": 17439, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.address", "ranges": [{"startOffset": 17470, "endOffset": 17514, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17600, "endOffset": 17644, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17724, "endOffset": 17780, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17891, "endOffset": 18206, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18295, "endOffset": 18375, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18463, "endOffset": 18508, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryReadStart", "ranges": [{"startOffset": 18516, "endOffset": 18766, "count": 1}, {"startOffset": 18716, "endOffset": 18764, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket._read", "ranges": [{"startOffset": 18858, "endOffset": 19177, "count": 1}, {"startOffset": 19021, "endOffset": 19113, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19088, "endOffset": 19107, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.end", "ranges": [{"startOffset": 19204, "endOffset": 19360, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resetAndDestroy", "ranges": [{"startOffset": 19398, "endOffset": 19748, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.pause", "ranges": [{"startOffset": 19776, "endOffset": 20114, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resume", "ranges": [{"startOffset": 20144, "endOffset": 20328, "count": 1}, {"startOffset": 20177, "endOffset": 20196, "count": 0}, {"startOffset": 20197, "endOffset": 20212, "count": 0}, {"startOffset": 20213, "endOffset": 20243, "count": 0}, {"startOffset": 20245, "endOffset": 20274, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket.read", "ranges": [{"startOffset": 20356, "endOffset": 20542, "count": 3}, {"startOffset": 20390, "endOffset": 20409, "count": 0}, {"startOffset": 20410, "endOffset": 20425, "count": 0}, {"startOffset": 20426, "endOffset": 20456, "count": 0}, {"startOffset": 20458, "endOffset": 20487, "count": 0}], "isBlockCoverage": true}, {"functionName": "onReadableStreamEnd", "ranges": [{"startOffset": 20589, "endOffset": 20688, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.destroySoon", "ranges": [{"startOffset": 20722, "endOffset": 20869, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._destroy", "ranges": [{"startOffset": 20901, "endOffset": 22415, "count": 1}, {"startOffset": 21198, "endOffset": 21204, "count": 0}, {"startOffset": 21424, "endOffset": 21669, "count": 0}, {"startOffset": 21710, "endOffset": 21946, "count": 0}, {"startOffset": 22165, "endOffset": 22236, "count": 0}, {"startOffset": 22258, "endOffset": 22413, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21499, "endOffset": 21584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21890, "endOffset": 21938, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._reset", "ranges": [{"startOffset": 22444, "endOffset": 22543, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getpeername", "ranges": [{"startOffset": 22578, "endOffset": 22875, "count": 0}], "isBlockCoverage": false}, {"functionName": "protoGetter", "ranges": [{"startOffset": 22878, "endOffset": 23060, "count": 9}], "isBlockCoverage": true}, {"functionName": "bytesRead", "ranges": [{"startOffset": 23087, "endOffset": 23178, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteAddress", "ranges": [{"startOffset": 23211, "endOffset": 23277, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteFamily", "ranges": [{"startOffset": 23309, "endOffset": 23373, "count": 0}], "isBlockCoverage": false}, {"functionName": "remotePort", "ranges": [{"startOffset": 23403, "endOffset": 23463, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getsockname", "ranges": [{"startOffset": 23500, "endOffset": 23776, "count": 0}], "isBlockCoverage": false}, {"functionName": "localAddress", "ranges": [{"startOffset": 23808, "endOffset": 23873, "count": 0}], "isBlockCoverage": false}, {"functionName": "localPort", "ranges": [{"startOffset": 23903, "endOffset": 23962, "count": 0}], "isBlockCoverage": false}, {"functionName": "localFamily", "ranges": [{"startOffset": 23993, "endOffset": 24056, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.<computed>", "ranges": [{"startOffset": 24097, "endOffset": 24144, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writeGeneric", "ranges": [{"startOffset": 24180, "endOffset": 25092, "count": 1}, {"startOffset": 24402, "endOffset": 24746, "count": 0}, {"startOffset": 24827, "endOffset": 24883, "count": 0}, {"startOffset": 24937, "endOffset": 24973, "count": 0}, {"startOffset": 25052, "endOffset": 25090, "count": 0}], "isBlockCoverage": true}, {"functionName": "connect", "ranges": [{"startOffset": 24497, "endOffset": 24611, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClose", "ranges": [{"startOffset": 24618, "endOffset": 24697, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writev", "ranges": [{"startOffset": 25123, "endOffset": 25191, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._write", "ranges": [{"startOffset": 25221, "endOffset": 25302, "count": 1}], "isBlockCoverage": true}, {"functionName": "_bytesDispatched", "ranges": [{"startOffset": 25495, "endOffset": 25599, "count": 0}], "isBlockCoverage": false}, {"functionName": "bytes<PERSON>ritten", "ranges": [{"startOffset": 25631, "endOffset": 26560, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkBindError", "ranges": [{"startOffset": 26565, "endOffset": 27364, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnect", "ranges": [{"startOffset": 27367, "endOffset": 29467, "count": 1}, {"startOffset": 27622, "endOffset": 28261, "count": 0}, {"startOffset": 28777, "endOffset": 28841, "count": 0}, {"startOffset": 28845, "endOffset": 29004, "count": 0}, {"startOffset": 29017, "endOffset": 29269, "count": 0}, {"startOffset": 29343, "endOffset": 29465, "count": 0}], "isBlockCoverage": true}, {"functionName": "internalConnectMultiple", "ranges": [{"startOffset": 29470, "endOffset": 32410, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.connect", "ranges": [{"startOffset": 32439, "endOffset": 34035, "count": 1}, {"startOffset": 32848, "endOffset": 32897, "count": 0}, {"startOffset": 33109, "endOffset": 33135, "count": 0}, {"startOffset": 33137, "endOffset": 33159, "count": 0}, {"startOffset": 33244, "endOffset": 33267, "count": 0}, {"startOffset": 33273, "endOffset": 33329, "count": 0}, {"startOffset": 33380, "endOffset": 33416, "count": 0}, {"startOffset": 33440, "endOffset": 33524, "count": 0}, {"startOffset": 33654, "endOffset": 33692, "count": 0}, {"startOffset": 33826, "endOffset": 33970, "count": 0}], "isBlockCoverage": true}, {"functionName": "reinitializeHandle", "ranges": [{"startOffset": 34078, "endOffset": 34232, "count": 0}], "isBlockCoverage": false}, {"functionName": "socketToDnsFamily", "ranges": [{"startOffset": 34235, "endOffset": 34381, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnect", "ranges": [{"startOffset": 34383, "endOffset": 38314, "count": 1}, {"startOffset": 34501, "endOffset": 34515, "count": 0}, {"startOffset": 34613, "endOffset": 34635, "count": 0}, {"startOffset": 34637, "endOffset": 34694, "count": 0}, {"startOffset": 34713, "endOffset": 34770, "count": 0}, {"startOffset": 34871, "endOffset": 34997, "count": 0}, {"startOffset": 35073, "endOffset": 35145, "count": 0}, {"startOffset": 35252, "endOffset": 35450, "count": 0}, {"startOffset": 35945, "endOffset": 36177, "count": 0}, {"startOffset": 36202, "endOffset": 36231, "count": 0}, {"startOffset": 36232, "endOffset": 36261, "count": 0}, {"startOffset": 36262, "endOffset": 36290, "count": 0}, {"startOffset": 36292, "endOffset": 36480, "count": 0}, {"startOffset": 36510, "endOffset": 36533, "count": 0}, {"startOffset": 36534, "endOffset": 36550, "count": 0}, {"startOffset": 36551, "endOffset": 36570, "count": 0}, {"startOffset": 36572, "endOffset": 38313, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35714, "endOffset": 35927, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36690, "endOffset": 36947, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 37020, "endOffset": 38310, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnectMultiple", "ranges": [{"startOffset": 38316, "endOffset": 42415, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectErrorNT", "ranges": [{"startOffset": 42417, "endOffset": 42476, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.ref", "ranges": [{"startOffset": 42502, "endOffset": 42689, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.unref", "ranges": [{"startOffset": 42718, "endOffset": 42911, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnect", "ranges": [{"startOffset": 42915, "endOffset": 44603, "count": 1}, {"startOffset": 43085, "endOffset": 43102, "count": 0}, {"startOffset": 43268, "endOffset": 43317, "count": 0}, {"startOffset": 43354, "endOffset": 43379, "count": 0}, {"startOffset": 43533, "endOffset": 43561, "count": 0}, {"startOffset": 43563, "endOffset": 43642, "count": 0}, {"startOffset": 43912, "endOffset": 43933, "count": 0}, {"startOffset": 43935, "endOffset": 43993, "count": 0}, {"startOffset": 43997, "endOffset": 44601, "count": 0}], "isBlockCoverage": true}, {"functionName": "addClientAbortSignalOption", "ranges": [{"startOffset": 44605, "endOffset": 45015, "count": 0}], "isBlockCoverage": false}, {"functionName": "createConnectionError", "ranges": [{"startOffset": 45017, "endOffset": 45538, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnectMultiple", "ranges": [{"startOffset": 45540, "endOffset": 46858, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnectMultipleTimeout", "ranges": [{"startOffset": 46860, "endOffset": 47356, "count": 0}], "isBlockCoverage": false}, {"functionName": "addServerAbortSignalOption", "ranges": [{"startOffset": 47358, "endOffset": 47788, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server", "ranges": [{"startOffset": 47790, "endOffset": 49402, "count": 0}], "isBlockCoverage": false}, {"functionName": "toNumber", "ranges": [{"startOffset": 49513, "endOffset": 49578, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServerHandle", "ranges": [{"startOffset": 49646, "endOffset": 51098, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupListenHandle", "ranges": [{"startOffset": 51100, "endOffset": 53794, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 53861, "endOffset": 53923, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitListeningNT", "ranges": [{"startOffset": 53926, "endOffset": 54041, "count": 0}], "isBlockCoverage": false}, {"functionName": "listenInCluster", "ranges": [{"startOffset": 54044, "endOffset": 55626, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.listen", "ranges": [{"startOffset": 55655, "endOffset": 60017, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndListen", "ranges": [{"startOffset": 60020, "endOffset": 60563, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 60645, "endOffset": 60688, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.address", "ranges": [{"startOffset": 60764, "endOffset": 61057, "count": 0}], "isBlockCoverage": false}, {"functionName": "onconnection", "ranges": [{"startOffset": 61060, "endOffset": 62963, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.getConnections", "ranges": [{"startOffset": 63112, "endOffset": 63865, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.close", "ranges": [{"startOffset": 63894, "endOffset": 64784, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 64826, "endOffset": 64946, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server._emitCloseIfDrained", "ranges": [{"startOffset": 64988, "endOffset": 65374, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 65378, "endOffset": 65461, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 65520, "endOffset": 65682, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 65904, "endOffset": 65940, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 65944, "endOffset": 65985, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 66065, "endOffset": 66096, "count": 33}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 66100, "endOffset": 66136, "count": 3}], "isBlockCoverage": true}, {"functionName": "Server._setupWorker", "ranges": [{"startOffset": 66175, "endOffset": 66416, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.ref", "ranges": [{"startOffset": 66442, "endOffset": 66540, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.unref", "ranges": [{"startOffset": 66568, "endOffset": 66667, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 66809, "endOffset": 67478, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 67517, "endOffset": 67758, "count": 0}], "isBlockCoverage": false}, {"functionName": "get BlockList", "ranges": [{"startOffset": 67887, "endOffset": 67989, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>cket<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 67993, "endOffset": 68115, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "84", "url": "node:stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5095, "count": 1}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 2552, "endOffset": 2712, "count": 0}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 3214, "endOffset": 3352, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4400, "endOffset": 4432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4530, "endOffset": 4571, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4664, "endOffset": 4705, "count": 0}], "isBlockCoverage": false}, {"functionName": "_uint8ArrayToBuffer", "ranges": [{"startOffset": 4886, "endOffset": 5093, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "85", "url": "node:internal/streams/operators", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10772, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 1019, "endOffset": 1599, "count": 0}], "isBlockCoverage": false}, {"functionName": "map", "ranges": [{"startOffset": 1601, "endOffset": 4704, "count": 0}], "isBlockCoverage": false}, {"functionName": "asIndexedPairs", "ranges": [{"startOffset": 4706, "endOffset": 5179, "count": 0}], "isBlockCoverage": false}, {"functionName": "some", "ranges": [{"startOffset": 5181, "endOffset": 5329, "count": 0}], "isBlockCoverage": false}, {"functionName": "every", "ranges": [{"startOffset": 5331, "endOffset": 5659, "count": 0}], "isBlockCoverage": false}, {"functionName": "find", "ranges": [{"startOffset": 5661, "endOffset": 5803, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 5805, "endOffset": 6175, "count": 0}], "isBlockCoverage": false}, {"functionName": "filter", "ranges": [{"startOffset": 6177, "endOffset": 6504, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReduceAwareErrMissingArgs", "ranges": [{"startOffset": 6720, "endOffset": 6834, "count": 0}], "isBlockCoverage": false}, {"functionName": "reduce", "ranges": [{"startOffset": 6838, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "toArray", "ranges": [{"startOffset": 8282, "endOffset": 8711, "count": 0}], "isBlockCoverage": false}, {"functionName": "flatMap", "ranges": [{"startOffset": 8713, "endOffset": 8907, "count": 0}], "isBlockCoverage": false}, {"functionName": "toIntegerOrInfinity", "ranges": [{"startOffset": 8909, "endOffset": 9231, "count": 0}], "isBlockCoverage": false}, {"functionName": "drop", "ranges": [{"startOffset": 9233, "endOffset": 9781, "count": 0}], "isBlockCoverage": false}, {"functionName": "take", "ranges": [{"startOffset": 9783, "endOffset": 10453, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "86", "url": "node:internal/abort_controller", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12429, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyMessageChannel", "ranges": [{"startOffset": 1609, "endOffset": 1742, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyMakeTransferable", "ranges": [{"startOffset": 1744, "endOffset": 1902, "count": 0}], "isBlockCoverage": false}, {"functionName": "customInspect", "ranges": [{"startOffset": 2372, "endOffset": 2631, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateThisAbortSignal", "ranges": [{"startOffset": 2633, "endOffset": 2758, "count": 0}], "isBlockCoverage": false}, {"functionName": "setWeakAbortSignalTimeout", "ranges": [{"startOffset": 3340, "endOffset": 3740, "count": 0}], "isBlockCoverage": false}, {"functionName": "AbortSignal", "ranges": [{"startOffset": 3784, "endOffset": 3844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get aborted", "ranges": [{"startOffset": 3881, "endOffset": 3964, "count": 0}], "isBlockCoverage": false}, {"functionName": "get reason", "ranges": [{"startOffset": 3997, "endOffset": 4076, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfAborted", "ranges": [{"startOffset": 4080, "endOffset": 4196, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4200, "endOffset": 4329, "count": 0}], "isBlockCoverage": false}, {"functionName": "abort", "ranges": [{"startOffset": 4407, "endOffset": 4551, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeout", "ranges": [{"startOffset": 4629, "endOffset": 4895, "count": 0}], "isBlockCoverage": false}, {"functionName": "any", "ranges": [{"startOffset": 4982, "endOffset": 6325, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6329, "endOffset": 7097, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7101, "endOffset": 7455, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7459, "endOffset": 8066, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8070, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8326, "endOffset": 8774, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal", "ranges": [{"startOffset": 8778, "endOffset": 8862, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal.<computed>", "ranges": [{"startOffset": 8907, "endOffset": 8915, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAbortSignal", "ranges": [{"startOffset": 9393, "endOffset": 9806, "count": 0}], "isBlockCoverage": false}, {"functionName": "abortSignal", "ranges": [{"startOffset": 9808, "endOffset": 10170, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 10172, "endOffset": 10828, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortSignal", "ranges": [{"startOffset": 10976, "endOffset": 11168, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortController", "ranges": [{"startOffset": 11240, "endOffset": 11329, "count": 0}], "isBlockCoverage": false}, {"functionName": "aborted", "ranges": [{"startOffset": 11423, "endOffset": 11980, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "87", "url": "node:internal/streams/end-of-stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8492, "count": 1}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 891, "endOffset": 986, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 1000, "endOffset": 1008, "count": 0}], "isBlockCoverage": false}, {"functionName": "eos", "ranges": [{"startOffset": 1011, "endOffset": 7061, "count": 0}], "isBlockCoverage": false}, {"functionName": "eosWeb", "ranges": [{"startOffset": 7063, "endOffset": 7972, "count": 0}], "isBlockCoverage": false}, {"functionName": "finished", "ranges": [{"startOffset": 7974, "endOffset": 8432, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "88", "url": "node:internal/streams/compose", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5511, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 592, "endOffset": 5509, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "89", "url": "node:internal/streams/pipeline", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12389, "count": 1}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 1056, "endOffset": 1485, "count": 0}], "isBlockCoverage": false}, {"functionName": "popCallback", "ranges": [{"startOffset": 1487, "endOffset": 1815, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeAsyncIterable", "ranges": [{"startOffset": 1817, "endOffset": 2103, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromReadable", "ranges": [{"startOffset": 2105, "endOffset": 2279, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToNode", "ranges": [{"startOffset": 2281, "endOffset": 3325, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToWeb", "ranges": [{"startOffset": 3327, "endOffset": 3925, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 3927, "endOffset": 4020, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipelineImpl", "ranges": [{"startOffset": 4022, "endOffset": 10539, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipe", "ranges": [{"startOffset": 10541, "endOffset": 12342, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "90", "url": "node:internal/streams/destroy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7354, "count": 1}], "isBlockCoverage": false}, {"functionName": "checkError", "ranges": [{"startOffset": 487, "endOffset": 798, "count": 2}, {"startOffset": 531, "endOffset": 796, "count": 0}], "isBlockCoverage": true}, {"functionName": "destroy", "ranges": [{"startOffset": 908, "endOffset": 1785, "count": 1}, {"startOffset": 1077, "endOffset": 1081, "count": 0}, {"startOffset": 1182, "endOffset": 1259, "count": 0}, {"startOffset": 1620, "endOffset": 1727, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1646, "endOffset": 1721, "count": 0}], "isBlockCoverage": false}, {"functionName": "_destroy", "ranges": [{"startOffset": 1787, "endOffset": 2411, "count": 1}, {"startOffset": 2372, "endOffset": 2409, "count": 0}], "isBlockCoverage": true}, {"functionName": "onDestroy", "ranges": [{"startOffset": 1847, "endOffset": 2316, "count": 1}, {"startOffset": 1889, "endOffset": 1910, "count": 0}, {"startOffset": 2159, "endOffset": 2181, "count": 0}, {"startOffset": 2196, "endOffset": 2256, "count": 0}], "isBlockCoverage": true}, {"functionName": "emitErrorCloseNT", "ranges": [{"startOffset": 2413, "endOffset": 2500, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 2502, "endOffset": 2822, "count": 1}, {"startOffset": 2791, "endOffset": 2820, "count": 0}], "isBlockCoverage": true}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 2824, "endOffset": 3171, "count": 0}], "isBlockCoverage": false}, {"functionName": "undestroy", "ranges": [{"startOffset": 3173, "endOffset": 3863, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3865, "endOffset": 5004, "count": 0}], "isBlockCoverage": false}, {"functionName": "construct", "ranges": [{"startOffset": 5006, "endOffset": 5422, "count": 0}], "isBlockCoverage": false}, {"functionName": "constructNT", "ranges": [{"startOffset": 5424, "endOffset": 6163, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 6165, "endOffset": 6261, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseLegacy", "ranges": [{"startOffset": 6263, "endOffset": 6323, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseLegacy", "ranges": [{"startOffset": 6325, "endOffset": 6445, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 6480, "endOffset": 7262, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "91", "url": "node:internal/streams/duplex", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6588, "count": 1}], "isBlockCoverage": false}, {"functionName": "Duplex", "ranges": [{"startOffset": 2350, "endOffset": 4313, "count": 2}, {"startOffset": 2414, "endOffset": 2441, "count": 0}, {"startOffset": 3128, "endOffset": 3264, "count": 0}, {"startOffset": 3302, "endOffset": 3477, "count": 0}, {"startOffset": 3529, "endOffset": 3555, "count": 0}, {"startOffset": 3608, "endOffset": 3636, "count": 0}, {"startOffset": 3690, "endOffset": 3720, "count": 0}, {"startOffset": 3775, "endOffset": 3807, "count": 0}, {"startOffset": 3860, "endOffset": 3888, "count": 0}, {"startOffset": 3945, "endOffset": 3981, "count": 0}, {"startOffset": 4013, "endOffset": 4050, "count": 0}, {"startOffset": 4054, "endOffset": 4096, "count": 0}, {"startOffset": 4160, "endOffset": 4311, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4194, "endOffset": 4305, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5461, "endOffset": 5673, "count": 3}, {"startOffset": 5559, "endOffset": 5590, "count": 0}, {"startOffset": 5634, "endOffset": 5666, "count": 0}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 5679, "endOffset": 5940, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 6014, "endOffset": 6178, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.fromWeb", "ranges": [{"startOffset": 6197, "endOffset": 6315, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.toWeb", "ranges": [{"startOffset": 6333, "endOffset": 6422, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.from", "ranges": [{"startOffset": 6455, "endOffset": 6586, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "92", "url": "node:internal/streams/legacy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3251, "count": 1}], "isBlockCoverage": false}, {"functionName": "Stream", "ranges": [{"startOffset": 130, "endOffset": 178, "count": 2}], "isBlockCoverage": true}, {"functionName": "Stream.pipe", "ranges": [{"startOffset": 292, "endOffset": 2094, "count": 0}], "isBlockCoverage": false}, {"functionName": "eventNames", "ranges": [{"startOffset": 2127, "endOffset": 2393, "count": 0}], "isBlockCoverage": false}, {"functionName": "prependListener", "ranges": [{"startOffset": 2396, "endOffset": 3203, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "93", "url": "node:internal/streams/readable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 51364, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1880, "endOffset": 1905, "count": 1}], "isBlockCoverage": true}, {"functionName": "nop", "ranges": [{"startOffset": 2867, "endOffset": 2875, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3771, "endOffset": 3993, "count": 19}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3846, "endOffset": 3890, "count": 7}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3896, "endOffset": 3985, "count": 24}, {"startOffset": 3926, "endOffset": 3946, "count": 3}, {"startOffset": 3946, "endOffset": 3979, "count": 21}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6167, "endOffset": 6255, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6261, "endOffset": 6427, "count": 3}, {"startOffset": 6291, "endOffset": 6371, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6504, "endOffset": 6604, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6610, "endOffset": 6840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6909, "endOffset": 6997, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7003, "endOffset": 7169, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7239, "endOffset": 7329, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7335, "endOffset": 7504, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7573, "endOffset": 7676, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7682, "endOffset": 7949, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadableState", "ranges": [{"startOffset": 7962, "endOffset": 9958, "count": 2}, {"startOffset": 8362, "endOffset": 8390, "count": 0}, {"startOffset": 8453, "endOffset": 8481, "count": 0}, {"startOffset": 8729, "endOffset": 8765, "count": 0}, {"startOffset": 9107, "endOffset": 9137, "count": 0}, {"startOffset": 9416, "endOffset": 9445, "count": 0}, {"startOffset": 9446, "endOffset": 9476, "count": 0}, {"startOffset": 9525, "endOffset": 9684, "count": 0}, {"startOffset": 9857, "endOffset": 9956, "count": 0}], "isBlockCoverage": true}, {"functionName": "onConstructed", "ranges": [{"startOffset": 10002, "endOffset": 10119, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable", "ranges": [{"startOffset": 10122, "endOffset": 11156, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable._destroy", "ranges": [{"startOffset": 11293, "endOffset": 11325, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11376, "endOffset": 11414, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11458, "endOffset": 11714, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.push", "ranges": [{"startOffset": 11943, "endOffset": 12212, "count": 1}, {"startOffset": 12143, "endOffset": 12209, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.unshift", "ranges": [{"startOffset": 12308, "endOffset": 12575, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftByteMode", "ranges": [{"startOffset": 12579, "endOffset": 13645, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftObjectMode", "ranges": [{"startOffset": 13647, "endOffset": 13887, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftValue", "ranges": [{"startOffset": 13889, "endOffset": 14222, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushByteMode", "ranges": [{"startOffset": 14224, "endOffset": 15616, "count": 1}, {"startOffset": 14318, "endOffset": 14404, "count": 0}, {"startOffset": 14439, "endOffset": 14604, "count": 0}, {"startOffset": 14663, "endOffset": 14957, "count": 0}, {"startOffset": 14994, "endOffset": 15097, "count": 0}, {"startOffset": 15137, "endOffset": 15221, "count": 0}, {"startOffset": 15278, "endOffset": 15301, "count": 0}, {"startOffset": 15372, "endOffset": 15384, "count": 0}, {"startOffset": 15386, "endOffset": 15543, "count": 0}], "isBlockCoverage": true}, {"functionName": "readableAddChunkPushObjectMode", "ranges": [{"startOffset": 15618, "endOffset": 16215, "count": 0}], "isBlockCoverage": false}, {"functionName": "canPushMore", "ranges": [{"startOffset": 16217, "endOffset": 16547, "count": 1}, {"startOffset": 16522, "endOffset": 16543, "count": 0}], "isBlockCoverage": true}, {"functionName": "addChunk", "ranges": [{"startOffset": 16549, "endOffset": 17493, "count": 1}, {"startOffset": 16864, "endOffset": 16910, "count": 0}, {"startOffset": 17033, "endOffset": 17459, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.isPaused", "ranges": [{"startOffset": 17525, "endOffset": 17680, "count": 1}], "isBlockCoverage": true}, {"functionName": "Readable.setEncoding", "ranges": [{"startOffset": 17744, "endOffset": 18314, "count": 0}], "isBlockCoverage": false}, {"functionName": "computeNewHighWaterMark", "ranges": [{"startOffset": 18375, "endOffset": 18726, "count": 0}], "isBlockCoverage": false}, {"functionName": "howMuchToRead", "ranges": [{"startOffset": 18839, "endOffset": 19320, "count": 3}, {"startOffset": 18887, "endOffset": 18944, "count": 1}, {"startOffset": 18950, "endOffset": 18959, "count": 2}, {"startOffset": 18959, "endOffset": 19007, "count": 1}, {"startOffset": 19007, "endOffset": 19016, "count": 0}, {"startOffset": 19016, "endOffset": 19219, "count": 1}, {"startOffset": 19144, "endOffset": 19190, "count": 0}, {"startOffset": 19219, "endOffset": 19313, "count": 0}, {"startOffset": 19314, "endOffset": 19317, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.read", "ranges": [{"startOffset": 19417, "endOffset": 24092, "count": 3}, {"startOffset": 19601, "endOffset": 19619, "count": 1}, {"startOffset": 19619, "endOffset": 19686, "count": 2}, {"startOffset": 19650, "endOffset": 19686, "count": 0}, {"startOffset": 19851, "endOffset": 19900, "count": 0}, {"startOffset": 19921, "endOffset": 19956, "count": 1}, {"startOffset": 20142, "endOffset": 20188, "count": 2}, {"startOffset": 20189, "endOffset": 20341, "count": 1}, {"startOffset": 20272, "endOffset": 20298, "count": 0}, {"startOffset": 20343, "endOffset": 20520, "count": 0}, {"startOffset": 20664, "endOffset": 20739, "count": 0}, {"startOffset": 22034, "endOffset": 22075, "count": 0}, {"startOffset": 22462, "endOffset": 22540, "count": 2}, {"startOffset": 22540, "endOffset": 23164, "count": 1}, {"startOffset": 22849, "endOffset": 22901, "count": 0}, {"startOffset": 23128, "endOffset": 23160, "count": 0}, {"startOffset": 23194, "endOffset": 23219, "count": 0}, {"startOffset": 23339, "endOffset": 23342, "count": 0}, {"startOffset": 23358, "endOffset": 23540, "count": 0}, {"startOffset": 23863, "endOffset": 23896, "count": 1}, {"startOffset": 23904, "endOffset": 23922, "count": 0}, {"startOffset": 23947, "endOffset": 24005, "count": 0}, {"startOffset": 24007, "endOffset": 24075, "count": 0}], "isBlockCoverage": true}, {"functionName": "onEofChunk", "ranges": [{"startOffset": 24095, "endOffset": 25057, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable", "ranges": [{"startOffset": 25258, "endOffset": 25589, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable_", "ranges": [{"startOffset": 25591, "endOffset": 26263, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore", "ranges": [{"startOffset": 26613, "endOffset": 26820, "count": 1}], "isBlockCoverage": true}, {"functionName": "maybeReadMore_", "ranges": [{"startOffset": 26822, "endOffset": 28651, "count": 1}, {"startOffset": 28365, "endOffset": 28434, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable._read", "ranges": [{"startOffset": 28922, "endOffset": 28988, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pipe", "ranges": [{"startOffset": 29017, "endOffset": 33527, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeOnDrain", "ranges": [{"startOffset": 33530, "endOffset": 34207, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unpipe", "ranges": [{"startOffset": 34238, "endOffset": 34939, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.on", "ranges": [{"startOffset": 35065, "endOffset": 36074, "count": 5}, {"startOffset": 35197, "endOffset": 35626, "count": 1}, {"startOffset": 35426, "endOffset": 35446, "count": 0}, {"startOffset": 35626, "endOffset": 36057, "count": 4}, {"startOffset": 35655, "endOffset": 36057, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.removeListener", "ranges": [{"startOffset": 36169, "endOffset": 36876, "count": 1}, {"startOffset": 36369, "endOffset": 36755, "count": 0}, {"startOffset": 36779, "endOffset": 36814, "count": 0}, {"startOffset": 36816, "endOffset": 36859, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.removeAllListeners", "ranges": [{"startOffset": 36979, "endOffset": 37573, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateReadableListening", "ranges": [{"startOffset": 37576, "endOffset": 38278, "count": 0}], "isBlockCoverage": false}, {"functionName": "nReadingNextTick", "ranges": [{"startOffset": 38280, "endOffset": 38368, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.resume", "ranges": [{"startOffset": 38522, "endOffset": 39027, "count": 1}, {"startOffset": 38874, "endOffset": 38921, "count": 0}], "isBlockCoverage": true}, {"functionName": "resume", "ranges": [{"startOffset": 39030, "endOffset": 39203, "count": 1}], "isBlockCoverage": true}, {"functionName": "resume_", "ranges": [{"startOffset": 39205, "endOffset": 39519, "count": 1}, {"startOffset": 39332, "endOffset": 39357, "count": 0}, {"startOffset": 39502, "endOffset": 39517, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.pause", "ranges": [{"startOffset": 39548, "endOffset": 39860, "count": 0}], "isBlockCoverage": false}, {"functionName": "flow", "ranges": [{"startOffset": 39863, "endOffset": 40014, "count": 1}, {"startOffset": 40011, "endOffset": 40012, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 40198, "endOffset": 41219, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 41264, "endOffset": 41316, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.iterator", "ranges": [{"startOffset": 41349, "endOffset": 41492, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamToAsyncIterator", "ranges": [{"startOffset": 41495, "endOffset": 41744, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAsyncIterator", "ranges": [{"startOffset": 41746, "endOffset": 42822, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43041, "endOffset": 43430, "count": 1}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 43436, "endOffset": 43566, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43648, "endOffset": 43712, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43794, "endOffset": 44002, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44090, "endOffset": 44156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44237, "endOffset": 44319, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44401, "endOffset": 44461, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 44472, "endOffset": 44581, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44657, "endOffset": 44711, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44791, "endOffset": 44879, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44957, "endOffset": 45042, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45111, "endOffset": 45195, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45240, "endOffset": 45324, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45395, "endOffset": 45482, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 45488, "endOffset": 45777, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45852, "endOffset": 45940, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46081, "endOffset": 46126, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46206, "endOffset": 46264, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 46270, "endOffset": 46431, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromList", "ranges": [{"startOffset": 46737, "endOffset": 49189, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadable", "ranges": [{"startOffset": 49191, "endOffset": 49417, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadableNT", "ranges": [{"startOffset": 49419, "endOffset": 50312, "count": 0}], "isBlockCoverage": false}, {"functionName": "endWritableNT", "ranges": [{"startOffset": 50314, "endOffset": 50474, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.from", "ranges": [{"startOffset": 50492, "endOffset": 50561, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 50626, "endOffset": 50790, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.fromWeb", "ranges": [{"startOffset": 50811, "endOffset": 50945, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.toWeb", "ranges": [{"startOffset": 50965, "endOffset": 51099, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 51118, "endOffset": 51362, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "94", "url": "node:internal/streams/add-abort-signal", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1741, "count": 1}], "isBlockCoverage": false}, {"functionName": "validateAbortSignal", "ranges": [{"startOffset": 570, "endOffset": 729, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAbortSignal", "ranges": [{"startOffset": 764, "endOffset": 1075, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports.addAbortSignalNoValidate", "ranges": [{"startOffset": 1120, "endOffset": 1739, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "95", "url": "node:internal/streams/state", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1332, "count": 1}], "isBlockCoverage": false}, {"functionName": "highWaterMarkFrom", "ranges": [{"startOffset": 287, "endOffset": 454, "count": 4}, {"startOffset": 385, "endOffset": 408, "count": 0}, {"startOffset": 445, "endOffset": 451, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultHighWaterMark", "ranges": [{"startOffset": 456, "endOffset": 586, "count": 3}, {"startOffset": 523, "endOffset": 555, "count": 0}], "isBlockCoverage": true}, {"functionName": "setDefaultHighWaterMark", "ranges": [{"startOffset": 588, "endOffset": 799, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHighWaterMark", "ranges": [{"startOffset": 801, "endOffset": 1234, "count": 4}, {"startOffset": 948, "endOffset": 1160, "count": 1}, {"startOffset": 992, "endOffset": 1129, "count": 0}, {"startOffset": 1160, "endOffset": 1233, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "96", "url": "node:string_decoder", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5141, "count": 1}], "isBlockCoverage": false}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 2172, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "StringDecoder", "ranges": [{"startOffset": 2633, "endOffset": 2832, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 3155, "endOffset": 3540, "count": 0}], "isBlockCoverage": false}, {"functionName": "end", "ranges": [{"startOffset": 3817, "endOffset": 4008, "count": 0}], "isBlockCoverage": false}, {"functionName": "text", "ranges": [{"startOffset": 4219, "endOffset": 4376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4514, "endOffset": 4727, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4819, "endOffset": 4882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4975, "endOffset": 5090, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "97", "url": "node:internal/streams/from", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4341, "count": 1}], "isBlockCoverage": false}, {"functionName": "from", "ranges": [{"startOffset": 241, "endOffset": 4316, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "98", "url": "node:internal/streams/writable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33628, "count": 1}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2779, "endOffset": 2796, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3852, "endOffset": 4074, "count": 20}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3927, "endOffset": 3971, "count": 2}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3977, "endOffset": 4066, "count": 30}, {"startOffset": 4007, "endOffset": 4027, "count": 3}, {"startOffset": 4027, "endOffset": 4060, "count": 27}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7038, "endOffset": 7116, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7122, "endOffset": 7288, "count": 3}, {"startOffset": 7152, "endOffset": 7232, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7358, "endOffset": 7458, "count": 10}, {"startOffset": 7409, "endOffset": 7443, "count": 0}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 7464, "endOffset": 7737, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7814, "endOffset": 7914, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7920, "endOffset": 8150, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8291, "endOffset": 8368, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8374, "endOffset": 8538, "count": 1}, {"startOffset": 8439, "endOffset": 8482, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8725, "endOffset": 8825, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8831, "endOffset": 9028, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9098, "endOffset": 9176, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 9182, "endOffset": 9349, "count": 0}], "isBlockCoverage": false}, {"functionName": "WritableState", "ranges": [{"startOffset": 9362, "endOffset": 11664, "count": 2}, {"startOffset": 9640, "endOffset": 9668, "count": 0}, {"startOffset": 9731, "endOffset": 9759, "count": 0}, {"startOffset": 10050, "endOffset": 10086, "count": 0}, {"startOffset": 10140, "endOffset": 10171, "count": 0}, {"startOffset": 10429, "endOffset": 10459, "count": 0}, {"startOffset": 10716, "endOffset": 10722, "count": 0}, {"startOffset": 10754, "endOffset": 10783, "count": 0}, {"startOffset": 10784, "endOffset": 10814, "count": 0}, {"startOffset": 10863, "endOffset": 11072, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetBuffer", "ranges": [{"startOffset": 11666, "endOffset": 11830, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11868, "endOffset": 11997, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 12093, "endOffset": 12204, "count": 0}], "isBlockCoverage": false}, {"functionName": "onConstructed", "ranges": [{"startOffset": 12253, "endOffset": 12439, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable", "ranges": [{"startOffset": 12442, "endOffset": 13548, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 13630, "endOffset": 13834, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.pipe", "ranges": [{"startOffset": 13935, "endOffset": 14003, "count": 0}], "isBlockCoverage": false}, {"functionName": "_write", "ranges": [{"startOffset": 14006, "endOffset": 15404, "count": 1}, {"startOffset": 14110, "endOffset": 14137, "count": 0}, {"startOffset": 14182, "endOffset": 14227, "count": 0}, {"startOffset": 14293, "endOffset": 14396, "count": 0}, {"startOffset": 14461, "endOffset": 14516, "count": 0}, {"startOffset": 14605, "endOffset": 14689, "count": 0}, {"startOffset": 14695, "endOffset": 15015, "count": 0}, {"startOffset": 15071, "endOffset": 15120, "count": 0}, {"startOffset": 15166, "endOffset": 15216, "count": 0}, {"startOffset": 15229, "endOffset": 15320, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable.write", "ranges": [{"startOffset": 15433, "endOffset": 15624, "count": 1}, {"startOffset": 15523, "endOffset": 15568, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable.cork", "ranges": [{"startOffset": 15653, "endOffset": 15751, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.uncork", "ranges": [{"startOffset": 15782, "endOffset": 16020, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultEncoding", "ranges": [{"startOffset": 16063, "endOffset": 16388, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeOr<PERSON>uffer", "ranges": [{"startOffset": 16578, "endOffset": 17782, "count": 1}, {"startOffset": 16695, "endOffset": 16698, "count": 0}, {"startOffset": 16829, "endOffset": 17236, "count": 0}, {"startOffset": 17296, "endOffset": 17335, "count": 0}, {"startOffset": 17526, "endOffset": 17547, "count": 0}, {"startOffset": 17562, "endOffset": 17600, "count": 0}], "isBlockCoverage": true}, {"functionName": "doWrite", "ranges": [{"startOffset": 17784, "endOffset": 18221, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwriteError", "ranges": [{"startOffset": 18223, "endOffset": 18608, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwrite", "ranges": [{"startOffset": 18610, "endOffset": 21216, "count": 1}, {"startOffset": 18727, "endOffset": 18801, "count": 0}, {"startOffset": 18895, "endOffset": 18917, "count": 0}, {"startOffset": 19066, "endOffset": 19680, "count": 0}, {"startOffset": 19731, "endOffset": 19772, "count": 0}, {"startOffset": 19849, "endOffset": 19870, "count": 0}, {"startOffset": 20304, "endOffset": 20424, "count": 0}, {"startOffset": 20508, "endOffset": 20567, "count": 0}, {"startOffset": 20585, "endOffset": 21151, "count": 0}, {"startOffset": 21157, "endOffset": 21210, "count": 0}], "isBlockCoverage": true}, {"functionName": "afterWriteTick", "ranges": [{"startOffset": 21218, "endOffset": 21405, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterWrite", "ranges": [{"startOffset": 21407, "endOffset": 21915, "count": 0}], "isBlockCoverage": false}, {"functionName": "errorBuffer", "ranges": [{"startOffset": 21987, "endOffset": 22538, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 22604, "endOffset": 24274, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._write", "ranges": [{"startOffset": 24304, "endOffset": 24473, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.end", "ranges": [{"startOffset": 24537, "endOffset": 26177, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>sh", "ranges": [{"startOffset": 26180, "endOffset": 26609, "count": 0}], "isBlockCoverage": false}, {"functionName": "onFinish", "ranges": [{"startOffset": 26611, "endOffset": 27243, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 27245, "endOffset": 27769, "count": 0}], "isBlockCoverage": false}, {"functionName": "finishMaybe", "ranges": [{"startOffset": 27771, "endOffset": 28270, "count": 0}], "isBlockCoverage": false}, {"functionName": "finish", "ranges": [{"startOffset": 28272, "endOffset": 28915, "count": 0}], "isBlockCoverage": false}, {"functionName": "callFinishedCallbacks", "ranges": [{"startOffset": 28917, "endOffset": 29235, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29319, "endOffset": 29422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29470, "endOffset": 29576, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 29582, "endOffset": 29832, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29879, "endOffset": 30282, "count": 1}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 30288, "endOffset": 30422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30477, "endOffset": 30595, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30652, "endOffset": 30772, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30825, "endOffset": 30920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30972, "endOffset": 31088, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31144, "endOffset": 31297, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31357, "endOffset": 31454, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31507, "endOffset": 31600, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31653, "endOffset": 31743, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31812, "endOffset": 31909, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31968, "endOffset": 32225, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.destroy", "ranges": [{"startOffset": 32303, "endOffset": 32583, "count": 1}, {"startOffset": 32448, "endOffset": 32485, "count": 0}, {"startOffset": 32487, "endOffset": 32534, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable._destroy", "ranges": [{"startOffset": 32671, "endOffset": 32703, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 32754, "endOffset": 32792, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 32857, "endOffset": 33021, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.fromWeb", "ranges": [{"startOffset": 33042, "endOffset": 33176, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.toWeb", "ranges": [{"startOffset": 33196, "endOffset": 33303, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 33347, "endOffset": 33626, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "99", "url": "node:stream/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 917, "count": 1}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 318, "endOffset": 869, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "100", "url": "node:internal/streams/transform", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7123, "count": 1}], "isBlockCoverage": false}, {"functionName": "Transform", "ranges": [{"startOffset": 3920, "endOffset": 5500, "count": 0}], "isBlockCoverage": false}, {"functionName": "final", "ranges": [{"startOffset": 5502, "endOffset": 5946, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 5948, "endOffset": 6029, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._transform", "ranges": [{"startOffset": 6101, "endOffset": 6196, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._write", "ranges": [{"startOffset": 6228, "endOffset": 6965, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._read", "ranges": [{"startOffset": 6996, "endOffset": 7121, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "101", "url": "node:internal/streams/passthrough", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1762, "count": 1}], "isBlockCoverage": false}, {"functionName": "PassThrough", "ranges": [{"startOffset": 1529, "endOffset": 1671, "count": 0}], "isBlockCoverage": false}, {"functionName": "PassThrough._transform", "ranges": [{"startOffset": 1708, "endOffset": 1760, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "102", "url": "node:internal/streams/duplexpair", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1373, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 219, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "duplexPair", "ranges": [{"startOffset": 1138, "endOffset": 1343, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "103", "url": "node:internal/stream_base_commons", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7170, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1057, "endOffset": 1082, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWriteReq", "ranges": [{"startOffset": 1201, "endOffset": 2056, "count": 1}, {"startOffset": 1301, "endOffset": 1468, "count": 0}, {"startOffset": 1473, "endOffset": 1487, "count": 0}, {"startOffset": 1492, "endOffset": 1556, "count": 0}, {"startOffset": 1644, "endOffset": 1706, "count": 0}, {"startOffset": 1711, "endOffset": 1723, "count": 0}, {"startOffset": 1728, "endOffset": 1741, "count": 0}, {"startOffset": 1746, "endOffset": 1761, "count": 0}, {"startOffset": 1766, "endOffset": 1830, "count": 0}, {"startOffset": 1835, "endOffset": 2050, "count": 0}], "isBlockCoverage": true}, {"functionName": "onWriteComplete", "ranges": [{"startOffset": 2058, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "createWriteWrap", "ranges": [{"startOffset": 2749, "endOffset": 2987, "count": 1}], "isBlockCoverage": true}, {"functionName": "writevGeneric", "ranges": [{"startOffset": 2989, "endOffset": 3628, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeGeneric", "ranges": [{"startOffset": 3630, "endOffset": 3835, "count": 1}], "isBlockCoverage": true}, {"functionName": "afterWriteDispatched", "ranges": [{"startOffset": 3837, "endOffset": 4144, "count": 1}, {"startOffset": 4004, "endOffset": 4059, "count": 0}], "isBlockCoverage": true}, {"functionName": "onStreamRead", "ranges": [{"startOffset": 4146, "endOffset": 6085, "count": 1}, {"startOffset": 4447, "endOffset": 4706, "count": 0}, {"startOffset": 4890, "endOffset": 5081, "count": 0}, {"startOffset": 5102, "endOffset": 6083, "count": 0}], "isBlockCoverage": true}, {"functionName": "setStreamTimeout", "ranges": [{"startOffset": 6087, "endOffset": 6963, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "104", "url": "node:diagnostics_channel", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10086, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 808, "endOffset": 1217, "count": 1}], "isBlockCoverage": false}, {"functionName": "#finalizers", "ranges": [{"startOffset": 888, "endOffset": 924, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 930, "endOffset": 1047, "count": 7}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1051, "endOffset": 1099, "count": 7}, {"startOffset": 1087, "endOffset": 1092, "count": 0}], "isBlockCoverage": true}, {"functionName": "incRef", "ranges": [{"startOffset": 1103, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 1161, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "markActive", "ranges": [{"startOffset": 1219, "endOffset": 1424, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeMarkInactive", "ranges": [{"startOffset": 1426, "endOffset": 1794, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultTransform", "ranges": [{"startOffset": 1796, "endOffset": 1846, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapStoreRun", "ranges": [{"startOffset": 1848, "endOffset": 2176, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 2258, "endOffset": 2492, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 2496, "endOffset": 2943, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindStore", "ranges": [{"startOffset": 2947, "endOffset": 3115, "count": 0}], "isBlockCoverage": false}, {"functionName": "unbindStore", "ranges": [{"startOffset": 3119, "endOffset": 3318, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 3322, "endOffset": 3365, "count": 0}], "isBlockCoverage": false}, {"functionName": "publish", "ranges": [{"startOffset": 3369, "endOffset": 3709, "count": 0}], "isBlockCoverage": false}, {"functionName": "runStores", "ranges": [{"startOffset": 3713, "endOffset": 4052, "count": 0}], "isBlockCoverage": false}, {"functionName": "Channel", "ranges": [{"startOffset": 4074, "endOffset": 4215, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4226, "endOffset": 4411, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 4415, "endOffset": 4500, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 4504, "endOffset": 4541, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindStore", "ranges": [{"startOffset": 4545, "endOffset": 4638, "count": 0}], "isBlockCoverage": false}, {"functionName": "unbindStore", "ranges": [{"startOffset": 4642, "endOffset": 4679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 4683, "endOffset": 4727, "count": 1}], "isBlockCoverage": true}, {"functionName": "publish", "ranges": [{"startOffset": 4731, "endOffset": 4743, "count": 0}], "isBlockCoverage": false}, {"functionName": "runStores", "ranges": [{"startOffset": 4747, "endOffset": 4834, "count": 0}], "isBlockCoverage": false}, {"functionName": "channel", "ranges": [{"startOffset": 4874, "endOffset": 5140, "count": 7}, {"startOffset": 4952, "endOffset": 4967, "count": 0}, {"startOffset": 5000, "endOffset": 5027, "count": 0}, {"startOffset": 5029, "endOffset": 5109, "count": 0}], "isBlockCoverage": true}, {"functionName": "subscribe", "ranges": [{"startOffset": 5142, "endOffset": 5232, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 5234, "endOffset": 5328, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasSubscribers", "ranges": [{"startOffset": 5330, "endOffset": 5465, "count": 0}], "isBlockCoverage": false}, {"functionName": "assertChannel", "ranges": [{"startOffset": 5554, "endOffset": 5696, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannelFrom", "ranges": [{"startOffset": 5698, "endOffset": 6223, "count": 5}, {"startOffset": 5854, "endOffset": 5923, "count": 0}, {"startOffset": 5925, "endOffset": 6222, "count": 0}], "isBlockCoverage": true}, {"functionName": "TracingChannel", "ranges": [{"startOffset": 6250, "endOffset": 6475, "count": 1}, {"startOffset": 6321, "endOffset": 6471, "count": 5}], "isBlockCoverage": true}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 6479, "endOffset": 6689, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 6693, "endOffset": 6845, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 6849, "endOffset": 7082, "count": 0}], "isBlockCoverage": false}, {"functionName": "traceSync", "ranges": [{"startOffset": 7086, "endOffset": 7597, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracePromise", "ranges": [{"startOffset": 7601, "endOffset": 8787, "count": 0}], "isBlockCoverage": false}, {"functionName": "trace<PERSON><PERSON>back", "ranges": [{"startOffset": 8791, "endOffset": 9884, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannel", "ranges": [{"startOffset": 9888, "endOffset": 9976, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "105", "url": "node:internal/perf/observe", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16722, "count": 1}], "isBlockCoverage": false}, {"functionName": "queuePending", "ranges": [{"startOffset": 2600, "endOffset": 2861, "count": 0}], "isBlockCoverage": false}, {"functionName": "getObserverType", "ranges": [{"startOffset": 2863, "endOffset": 3203, "count": 1}, {"startOffset": 2918, "endOffset": 2967, "count": 0}, {"startOffset": 2972, "endOffset": 3027, "count": 0}, {"startOffset": 3032, "endOffset": 3085, "count": 0}, {"startOffset": 3146, "endOffset": 3197, "count": 0}], "isBlockCoverage": true}, {"functionName": "maybeDecrementObserverCounts", "ranges": [{"startOffset": 3205, "endOffset": 3626, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeIncrementObserverCount", "ranges": [{"startOffset": 3628, "endOffset": 3970, "count": 0}], "isBlockCoverage": false}, {"functionName": "performanceObserverSorter", "ranges": [{"startOffset": 4047, "endOffset": 4114, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceObserverEntryList", "ranges": [{"startOffset": 4156, "endOffset": 4384, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntries", "ranges": [{"startOffset": 4388, "endOffset": 4527, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByType", "ranges": [{"startOffset": 4531, "endOffset": 4835, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByName", "ranges": [{"startOffset": 4839, "endOffset": 5332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5336, "endOffset": 5584, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5936, "endOffset": 9533, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueue", "ranges": [{"startOffset": 10015, "endOffset": 10217, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferUserTiming", "ranges": [{"startOffset": 10278, "endOffset": 11304, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferResourceTiming", "ranges": [{"startOffset": 11592, "endOffset": 12856, "count": 0}], "isBlockCoverage": false}, {"functionName": "setResourceTimingBufferSize", "ranges": [{"startOffset": 12944, "endOffset": 13216, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDispatchBufferFull", "ranges": [{"startOffset": 13218, "endOffset": 13283, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearEntriesFromBuffer", "ranges": [{"startOffset": 13285, "endOffset": 13885, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterBufferMapByNameAndType", "ranges": [{"startOffset": 13887, "endOffset": 14729, "count": 0}], "isBlockCoverage": false}, {"functionName": "observerCallback", "ranges": [{"startOffset": 14731, "endOffset": 15847, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasObserver", "ranges": [{"startOffset": 15884, "endOffset": 16003, "count": 1}], "isBlockCoverage": true}, {"functionName": "startPerf", "ranges": [{"startOffset": 16006, "endOffset": 16116, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopPerf", "ranges": [{"startOffset": 16118, "endOffset": 16433, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "106", "url": "node:internal/perf/performance_entry", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3272, "count": 1}], "isBlockCoverage": false}, {"functionName": "isPerformanceEntry", "ranges": [{"startOffset": 675, "endOffset": 748, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceEntry", "ranges": [{"startOffset": 777, "endOffset": 1132, "count": 0}], "isBlockCoverage": false}, {"functionName": "get name", "ranges": [{"startOffset": 1136, "endOffset": 1236, "count": 0}], "isBlockCoverage": false}, {"functionName": "get entryType", "ranges": [{"startOffset": 1240, "endOffset": 1355, "count": 0}], "isBlockCoverage": false}, {"functionName": "get startTime", "ranges": [{"startOffset": 1359, "endOffset": 1474, "count": 0}], "isBlockCoverage": false}, {"functionName": "get duration", "ranges": [{"startOffset": 1478, "endOffset": 1590, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1594, "endOffset": 1838, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 1842, "endOffset": 2064, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceEntry", "ranges": [{"startOffset": 2286, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get detail", "ranges": [{"startOffset": 2539, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 2653, "endOffset": 2904, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceNodeEntry", "ranges": [{"startOffset": 2908, "endOffset": 3112, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "107", "url": "node:tty", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5045, "count": 1}], "isBlockCoverage": false}, {"functionName": "isatty", "ranges": [{"startOffset": 1526, "endOffset": 1632, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream", "ranges": [{"startOffset": 1634, "endOffset": 2103, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream.setRawMode", "ranges": [{"startOffset": 2252, "endOffset": 2473, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream", "ranges": [{"startOffset": 2476, "endOffset": 3442, "count": 1}, {"startOffset": 2545, "endOffset": 2572, "count": 0}, {"startOffset": 2609, "endOffset": 2638, "count": 0}, {"startOffset": 2720, "endOffset": 2765, "count": 0}], "isBlockCoverage": true}, {"functionName": "WriteStream._refreshSize", "ranges": [{"startOffset": 3733, "endOffset": 4167, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.cursorTo", "ranges": [{"startOffset": 4223, "endOffset": 4363, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.moveCursor", "ranges": [{"startOffset": 4400, "endOffset": 4546, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.clearLine", "ranges": [{"startOffset": 4582, "endOffset": 4721, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.clearScreenDown", "ranges": [{"startOffset": 4763, "endOffset": 4898, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.getWindowSize", "ranges": [{"startOffset": 4938, "endOffset": 4988, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "108", "url": "node:internal/tty", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6478, "count": 1}], "isBlockCoverage": false}, {"functionName": "warnOnDeactivatedColors", "ranges": [{"startOffset": 2542, "endOffset": 2995, "count": 0}], "isBlockCoverage": false}, {"functionName": "getColorDepth", "ranges": [{"startOffset": 3153, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasColors", "ranges": [{"startOffset": 6151, "endOffset": 6424, "count": 0}], "isBlockCoverage": false}]}], "timestamp": 8231.239669}