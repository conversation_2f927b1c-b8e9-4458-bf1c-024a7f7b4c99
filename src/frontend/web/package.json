{"name": "na-portal", "version": "1.0.0", "description": "NA Portal", "main": "index.js", "engines": {"node": ">=22.0.0"}, "config": {"server_port_dev_server": "9040", "server_port_demo": "9240", "server_port_gradle": "9241", "server_port_ui_test_playground": "9242", "server_port_swagger": "9243", "server_port_nowc": "9244"}, "scripts": {"clean": "rm -rf build", "test": "babel-node  --extensions \".js\",\".jsx\",\".es6\",\".es\",\".ts\" -w babel-plugin-require-context-hook -- specs.js | faucet", "test-with-coverage": "rm -rf .coverage; c8 --temp-directory=.coverage npm test", "test-ui": "babel-node  --extensions \".js\",\".jsx\",\".es6\",\".es\",\".ts\" -w babel-plugin-require-context-hook -- specs-ui.js -o \"test-ui-reports/$(date --utc --iso-8601=seconds)\"  | faucet", "lint": "eslint src", "lint-fix": "eslint src --fix", "lint-fix-file": "eslint --fix", "dev": "run-p test watch server:demo server:gradle server:dev server:ui-test-playground server:nowc", "dev:demo": "run-p test watch:lint watch:demo watch:test server:demo", "dev:ui-test": "run-p test watch:lint watch:demo watch:ui-test-playground watch:test server:demo server:ui-test-playground", "build:demo": "webpack --env NODE_ENV=development --env outputDir=build/webpack/demo --config='webpack-demos.config.js'", "build:demo-prod": "webpack --env NODE_ENV=production --env outputDir=build/webpack/demo-prod --config='webpack-demos.config.js'", "build:ui-test-playground": "webpack --env NODE_ENV=development --env outputDir=build/webpack/ui-tests --config='webpack-ui-tests.config.js'", "server:demo": "http-server ./build/webpack/demo -p $npm_package_config_server_port_demo", "server:nowc": "http-server ./node_modules/@alticelabsprojects/nossis-orchestration-web-components/dist/docs -p $npm_package_config_server_port_nowc", "server:gradle": "http-server ./build/webpack -p $npm_package_config_server_port_gradle  --cors", "server:ui-test-playground": "http-server ./build/webpack/ui-tests -p $npm_package_config_server_port_ui_test_playground", "server:dev": "cd dev-server; npm run start", "watch": "run-p watch:lint watch:demo watch:test watch:gradle-nossis-ui watch:gradle-modules watch:ui-test-playground", "watch:demo": "webpack --env NODE_ENV=development --env outputDir=build/webpack/demo --config='webpack-demos.config.js' --watch", "watch:lint": "onchange 'src/**/*.js' -- eslint {{file}}", "watch:lint-fix": "onchange 'src/**/*.js' -- eslint {{file}} --fix", "watch:test": "onchange -k 'src/**/*.js' 'src/**/*.ts' -- npm run test-with-coverage", "watch:ui-test-playground": "webpack --env NODE_ENV=development --env outputDir=build/webpack/ui-tests --config='webpack-ui-tests.config.js' --watch", "watch:gradle-nossis-ui": "webpack --env NODE_ENV=development --env outputDir=build/webpack/gradle-nossis-ui --config='webpack-gradle-nossis-ui.config.js' --watch", "watch:gradle-modules": "webpack --env NODE_ENV=development --env outputDir=build/webpack/gradle-modules --config='webpack-gradle-modules.config.js' --watch", "gradle": "run-s build:demo gradle-prod", "gradle-dev": "run-s test gradle-dev-nossis-ui gradle-dev-modules", "gradle-dev-nossis-ui": "webpack --env NODE_ENV=development --env outputDir=build/webpack/gradle-nossis-ui --config='webpack-gradle-nossis-ui.config.js'", "gradle-dev-modules": "webpack --env NODE_ENV=development --env outputDir=build/webpack/gradle-modules --config='webpack-gradle-modules.config.js'", "gradle-prod": "run-s gradle-prod-nossis-ui gradle-prod-modules", "gradle-prod-nossis-ui": "webpack --env NODE_ENV=production --env outputDir=build/webpack/gradle-nossis-ui --config='webpack-gradle-nossis-ui.config.js'", "gradle-prod-modules": "webpack --env NODE_ENV=production --env outputDir=build/webpack/gradle-modules --config='webpack-gradle-modules.config.js'"}, "author": "", "license": "ISC", "dependencies": {"@alticelabsprojects/nossis-orchestration-web-components": "^2.0.0", "@webcomponents/custom-elements": "^1.2.2", "angular": "^1.7.7", "basepack": "^1.14.1", "bootstrap": "^3.4.1", "bootstrap-sass": "^3.4.1", "bootstrap-switch": "3.0", "buffer": "^6.0.3", "busy-load": "^0.1.2", "c8": "^8.0.1", "code-prettify": "^0.1.0", "codemirror": "^5.44.0", "commander": "^11.1.0", "dagre": "^0.8.5", "datatables.net": "1.10.19", "datatables.net-colreorder": "^1.5.1", "datatables.net-plugins": "^1.10.19", "datatables.net-rowgroup": "^1.1.0", "dayjs": "^1.11.10", "ejs": "^3.1.6", "eonasdan-bootstrap-datetimepicker": "^3.1.3", "fast-deep-equal": "^3.1.3", "fuxi": "2.15", "fuxi-nossis": "^3.1.0", "hasher": "^1.2.0", "jquery": "^3.6.0", "jquery-ui": "^1.13.3", "jsplumb": "^2.9.0", "jstree": "^3.3.16", "lodash": "^4.17.20", "moment": "^2.24.0", "moment-timezone": "^0.5.45", "process": "^0.11.10", "readable-stream": "^3.6.0", "sass": "^1.29.0", "sass-loader": "^10.1.0", "select2": "^4.0.13", "selenium-webdriver": "^4.0.0-alpha.8", "signals": "1.0.0", "toastr": "^2.1.4"}, "devDependencies": {"@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "@babel/preset-env": "^7.4.3", "@babel/preset-typescript": "^7.12.7", "@fontsource/architects-daughter": "^5.1.1", "@mermaid-js/layout-elk": "^0.1.7", "asciidoctor": "^2.2.1", "babel-loader": "^8.2.2", "babel-plugin-require-context-hook": "^1.0.0", "compression-webpack-plugin": "^7.1.2", "countries-and-timezones": "^2.3.1", "css-loader": "^5.1.2", "css-minimizer-webpack-plugin": "^1.2.0", "es-abstract": "^1.18.0", "eslint": "^8.51.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-n": "^16.2.0", "faucet": "^0.0.4", "file-loader": "^6.2.0", "highlight.js": "^9.15.6", "html-webpack-plugin": "^5.3.1", "http-server": "^14.1.1", "js-string-escape": "^1.0.1", "jsdom": "^22.1.0", "mermaid": "^11.4.0", "mini-css-extract-plugin": "^1.3.9", "module-alias": "^2.2.0", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "onchange": "^7.1.0", "pirates": "^4.0.1", "postcss": "^8.2.8", "postcss-loader": "^5.1.0", "purgecss-webpack-plugin": "^4.0.3", "raw-loader": "^4.0.2", "resolve-url-loader": "^3.1.2", "shelljs": "^0.8.3", "stream-browserify": "^3.0.0", "svn-info": "^1.2.0", "tape": "^5.2.2", "url-loader": "^4.1.1", "webpack": "^5.24.4", "webpack-cli": "^4.5.0"}, "nyc": {"include": ["src/**/*.js", "src/**/*.ts"], "exclude": ["**/*.spec.js", "**/test/*.js", "**/test/**/*.js", "**/test/*.ts", "**/test/**/*.ts", "coverage"]}, "browser": {"fs": false}}